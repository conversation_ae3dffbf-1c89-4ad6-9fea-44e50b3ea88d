import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import sensaOffersPageObject from '../../page-object/sensa/sensa-offers.pageObject.ts';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import path from 'path';
import AccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';
import camelCouponsPageObject from '../../page-object/camel/camel-coupons.pageObject.ts';
import sensaAccountPage from '../commonteps/account.page.ts';



class Offers {

    async couponsPage() {
        try {
            await elementActions.waitForDisplayed(AccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.clickusingJavascript(AccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.click(camelCouponsPageObject.hdrcoupons_camel);
            await elementActions.assertion(sensaOffersPageObject.txtoffertitle_sensa);
            console.log('Navigated to Coupons Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Coupons Page', { error });
            throw error;
        }
    }

    async redeemoffersbutton() {
        try {
            await elementActions.clickusingJavascript(camelCouponsPageObject.btnRedeem_camel);
            await elementActions.waitForDisplayed(sensaOffersPageObject.btnunderstood_sensa);
            await elementActions.assertion(sensaOffersPageObject.btnunderstood_sensa);
            await browser.execute(() => window.history.back());
            const claimoffers = await camelCouponsPageObject.btnRedeem_camel;
            let tries = 0;
            const maxtries = 4;
            while (!(await claimoffers.isDisplayed()) && tries < maxtries) {
                await browser.execute(() => window.history.back());
                console.log('Navigated Again');
                tries++;
            }
            await elementActions.waitForDisplayed(camelCouponsPageObject.btnRedeem_camel);
            await elementActions.assertion(camelCouponsPageObject.btnRedeem_camel);
            console.log('Navigated to SPA Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to SPA Page', { error });
            throw error;
        }
    }

    async couponsPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const availableOffers = testData.getCellValue(SHEET_NAME, scenarioname, 'lblavailableoffers');
            const availableOffers_Prod = testData.getCellValue(SHEET_NAME, scenarioname, 'lblavailableoffers_Prod');
            const hdrclaimOffers = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrclaimOffers');
            const lblclaimedsavings = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclaimedsavings');
            const step1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstep1');
            const step2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstep2');
            const step3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstep3');
            const step4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstep4');
            const step5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstep5');
            const lbldisclaimer1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblofferDisclaimer1');
            const lbldisclaimer2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblofferDisclaimer2');
            const lbldisclaimer3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblofferDisclaimer3');
            const lbldisclaimer4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblofferDisclaimer4');
            const lbldisclaimer5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblofferDisclaimer5');
            const lbldisclaimer6 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblofferDisclaimer6');
            const lbldisclaimer7 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblofferDisclaimer7');
            const lblFAQ = testData.getCellValue(SHEET_NAME, scenarioname, 'lblFAQ');
            const lblFAQdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblFAQdesc');
            const lblFAQQ1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblFAQQ1');
            const lblFAQQ2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblFAQQ2');
            const lblFAQQ3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblFAQQ3');
            const lblFAQQ4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblFAQQ4');
            const lblFAQQ5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblFAQQ5');
            const lblFAQA1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblFAQA1');
            const lblFAQA2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblFAQA2');
            const lblFAQA21 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblFAQA21');
            const lblFAQA3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblFAQA3');
            const lblFAQA4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblFAQA4');
            const lblFAQA5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblFAQA5');
            await elementActions.assertion(sensaOffersPageObject.txtoffertitle_sensa);
            const url = browser.getUrl();
            const availablemobilecoupons = await (sensaOffersPageObject.lbloffersAvailable_sensa).getText();
            const mobilecoupons = (await availablemobilecoupons).replace(/[0-9]/g, '');
            console.log('Generated Text is: ', mobilecoupons);
            if ((await url).includes('aem')) {
                console.log('Expected Text is: ', availableOffers);
                expect(mobilecoupons.trim()).toEqual(availableOffers);
            } else {
                console.log('Expected Text is: ', availableOffers_Prod);
                expect(mobilecoupons.trim()).toEqual(availableOffers_Prod);
            }
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.hdrclaimOffers_camel, hdrclaimOffers);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblclaimedsavings_camel, lblclaimedsavings);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblstep1_camel, step1);
            await elementActions.assertion(camelCouponsPageObject.imgStep1_camel);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblstep2_camel, step2);
            await elementActions.assertion(camelCouponsPageObject.imgStep2_camel);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblstep3_camel, step3);
            await elementActions.assertion(camelCouponsPageObject.imgStep3_camel);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblstep4_camel, step4);
            await elementActions.assertion(camelCouponsPageObject.imgStep4_camel);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblstep5_camel, step5);
            await elementActions.assertion(camelCouponsPageObject.imgStep5_camel);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblofferDisclaimer1_camel, lbldisclaimer1);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblofferDisclaimer2_camel, lbldisclaimer2);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblofferDisclaimer3_camel, lbldisclaimer3);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblofferDisclaimer4_camel, lbldisclaimer4);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblofferDisclaimer5_camel, lbldisclaimer5);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblofferDisclaimer6_camel, lbldisclaimer6);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblofferDisclaimer7_camel, lbldisclaimer7);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblFAQ_camel, lblFAQ);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblFAQdesc_camel, lblFAQdesc);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblFAQQ1_camel, lblFAQQ1);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblFAQA1_camel, lblFAQA1);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblFAQQ2_camel, lblFAQQ2);
            await elementActions.click(camelCouponsPageObject.lblFAQQ2_camel);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblFAQA2_camel, lblFAQA2);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblFAQA21_camel, lblFAQA21);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblFAQQ3_camel, lblFAQQ3);
            await elementActions.click(camelCouponsPageObject.lblFAQQ3_camel);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblFAQA3_camel, lblFAQA3);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblFAQQ4_camel, lblFAQQ4);
            await elementActions.click(camelCouponsPageObject.lblFAQQ4_camel);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblFAQA4_camel, lblFAQA4);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblFAQQ5_camel, lblFAQQ5);
            await elementActions.click(camelCouponsPageObject.lblFAQQ5_camel);
            await sensaAccountPage.mssgcomparision(camelCouponsPageObject.lblFAQA5_camel, lblFAQA5);
            console.log('Validated the Content in Coupons page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Coupons page', { error });
            throw error;
        }
    }


}
export default new Offers();