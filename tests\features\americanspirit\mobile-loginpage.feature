Feature: Login Page Validation for Nas Website

    Scenario Outline: Validate Login Page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        Then The user Validates Signin Page for Nas with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the nas application successfully
        Then The user validates that successfully logged out of nas brand

        @NasLogin_Validation_QA
        Examples:
            | Brand | URL                                  | Username                           | Password  | filename            | sheetname | scenarioname          |
            | Nas   | https://aem-stage.americanspirit.com | <EMAIL> | Password1 | aem-mobile-nas.json | Login     | Login Page Validation |

        @NasLogin_Validation_PROD
        Examples:
            | Brand | URL                            | Username                             | Password  | filename            | sheetname | scenarioname          |
            | Nas   | https://www.americanspirit.com | <EMAIL> | Password1 | aem-mobile-nas.json | Login     | Login Page Validation |

    Scenario Outline: Validate InValid Username and Password error in Login Page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with invalid user id <Username> or invalid password <invalidPassword>
        Then The user validates login error message <loginerror>
        When The user login with invalid user id <invalidUsername> or invalid password <Password>
        Then The user validates login error message <loginerror>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the nas application successfully
        Then The user validates that successfully logged out of nas brand

        @NasInValidData_Validation_QA
        Examples:
            | Brand | URL                                  | Username                           | Password  | invalidPassword | invalidUsername                      | loginerror                                                         |
            | Nas   | https://aem-stage.americanspirit.com | <EMAIL> | Password1 | Passcod#@e12    | <EMAIL> | Username or password does not match our records. Please try again. |

        @NasInValidData_Validation_PROD
        Examples:
            | Brand | URL                            | Username                             | Password  | invalidPassword | invalidUsername                       | loginerror                                                         |
            | Nas   | https://www.americanspirit.com | <EMAIL> | Password1 | Passcod#@e12    | <EMAIL> | Username or password does not match our records. Please try again. |

    Scenario Outline: Validate Rememeber Me Functionality in Login Page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user enters valid user id <Username> and password <Password>
        Then The user clicks on Remember me checkbox and log in
        Then The user should be able to login to the nas application successfully
        Then The user validates that successfully logged out of nas brand
        Then The username <Username> should be displayed with remeber me checkbox selected

        @NasRememberMe_Validation_QA
        Examples:
            | Brand | URL                                  | Username                           | Password  |
            | Nas   | https://aem-stage.americanspirit.com | <EMAIL> | Password1 |


        @NasRememberMe_Validation_PROD
        Examples:
            | Brand | URL                            | Username                             | Password  |
            | Nas   | https://www.americanspirit.com | <EMAIL> | Password1 |

