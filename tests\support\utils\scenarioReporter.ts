/**
 * Utility to help with scenario reporting and display
 * Provides methods to format and display scenario information
 */

export interface ScenarioInfo {
  name: string;
  feature: string;
  status: 'passed' | 'failed' | 'skipped' | 'running';
  attempts: number;
  tags?: string[];
  duration?: number;
  error?: string;
}

export class ScenarioReporter {
  private static scenarios: ScenarioInfo[] = [];

  /**
   * Add a scenario to the tracking list
   */
  static addScenario(scenario: ScenarioInfo): void {
    this.scenarios.push(scenario);
  }

  /**
   * Update an existing scenario's status
   */
  static updateScenario(scenarioName: string, updates: Partial<ScenarioInfo>): void {
    const index = this.scenarios.findIndex(s => s.name === scenarioName);
    if (index !== -1) {
      this.scenarios[index] = { ...this.scenarios[index], ...updates };
    }
  }

  /**
   * Get all scenarios
   */
  static getScenarios(): ScenarioInfo[] {
    return [...this.scenarios];
  }

  /**
   * Get scenarios by status
   */
  static getScenariosByStatus(status: ScenarioInfo['status']): ScenarioInfo[] {
    return this.scenarios.filter(s => s.status === status);
  }

  /**
   * Get scenarios by feature
   */
  static getScenariosByFeature(feature: string): ScenarioInfo[] {
    return this.scenarios.filter(s => s.feature === feature);
  }

  /**
   * Clear all scenarios
   */
  static clear(): void {
    this.scenarios.length = 0;
  }

  /**
   * Format scenario name for display
   */
  static formatScenarioName(scenario: ScenarioInfo): string {
    const icon = this.getStatusIcon(scenario.status);
    const retryInfo = scenario.attempts > 1 ? ` (${scenario.attempts} attempts)` : '';
    const tagsInfo = scenario.tags && scenario.tags.length > 0 ? ` [${scenario.tags.join(', ')}]` : '';
    const durationInfo = scenario.duration ? ` (${scenario.duration}ms)` : '';
    
    return `${icon} ${scenario.name}${retryInfo}${tagsInfo}${durationInfo}`;
  }

  /**
   * Get status icon for a scenario
   */
  static getStatusIcon(status: ScenarioInfo['status']): string {
    switch (status) {
      case 'passed': return '✅';
      case 'failed': return '❌';
      case 'skipped': return '⏭️';
      case 'running': return '🔄';
      default: return '❓';
    }
  }

  /**
   * Print a summary of all scenarios
   */
  static printSummary(): void {
    if (this.scenarios.length === 0) {
      console.log('📊 No scenarios to report');
      return;
    }

    console.log('\n' + '='.repeat(100));
    console.log('📊 SCENARIO EXECUTION SUMMARY');
    console.log('='.repeat(100));

    const passed = this.getScenariosByStatus('passed');
    const failed = this.getScenariosByStatus('failed');
    const skipped = this.getScenariosByStatus('skipped');

    console.log(`\n📈 TOTALS: ${this.scenarios.length} scenarios | ${passed.length} passed | ${failed.length} failed | ${skipped.length} skipped\n`);

    // Group by feature
    const byFeature = this.scenarios.reduce((acc, scenario) => {
      if (!acc[scenario.feature]) acc[scenario.feature] = [];
      acc[scenario.feature].push(scenario);
      return acc;
    }, {} as Record<string, ScenarioInfo[]>);

    for (const [feature, scenarios] of Object.entries(byFeature)) {
      console.log(`📁 ${feature}:`);
      scenarios.forEach(scenario => {
        console.log(`   ${this.formatScenarioName(scenario)}`);
        if (scenario.error && scenario.status === 'failed') {
          console.log(`      💥 Error: ${scenario.error.substring(0, 100)}...`);
        }
      });
      console.log('');
    }

    console.log('='.repeat(100));
  }

  /**
   * Print scenarios for a specific feature
   */
  static printFeatureSummary(featureName: string): void {
    const scenarios = this.getScenariosByFeature(featureName);
    
    if (scenarios.length === 0) {
      console.log(`📁 ${featureName}: No scenarios found`);
      return;
    }

    console.log(`📁 ${featureName}:`);
    scenarios.forEach(scenario => {
      console.log(`   ${this.formatScenarioName(scenario)}`);
    });
  }

  /**
   * Get summary statistics
   */
  static getStats(): { total: number; passed: number; failed: number; skipped: number; running: number } {
    return {
      total: this.scenarios.length,
      passed: this.getScenariosByStatus('passed').length,
      failed: this.getScenariosByStatus('failed').length,
      skipped: this.getScenariosByStatus('skipped').length,
      running: this.getScenariosByStatus('running').length,
    };
  }
}
