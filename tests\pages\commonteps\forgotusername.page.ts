import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import RegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';
import path from 'path';
import AccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';
import sensaAccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';
import sensaAccountPage from './account.page.ts';
import sensaForgotusernamePageObject from '../../page-object/sensa/sensa-forgotusername.pageObject.ts';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import { faker } from '@faker-js/faker';
import camelLoginPageObject from '../../page-object/camel/camel-login.pageObject.ts';



class ForgotUsername {

    async entertellUsaboutpageDetailsinForgotusernamepage(firstName: string, lastName: string, currentaddr: string, zipcode: string, city: string, state: string, birthdate: string) {
        try {
            const dobParts = birthdate.split('-');
            const month = dobParts[0];
            const day = dobParts[1];
            const year = dobParts[2];
            console.log('Month', month);
            console.log('Year', year);
            console.log('Day', day);
            const url = driver.getUrl();

            await elementActions.waitForDisplayed(RegistrationPageObject.txtFirstName_sensa);
            await elementActions.setValue(RegistrationPageObject.txtFirstName_sensa, firstName);
            await elementActions.setValue(RegistrationPageObject.txtLastName_sensa, lastName);
            await elementActions.setValue(RegistrationPageObject.txtaddress_sensa, currentaddr);
            await elementActions.setValue(RegistrationPageObject.txtcity_sensa, city);
            await elementActions.setValue(RegistrationPageObject.txtzipCode_sensa, zipcode);
            const stateselect = await RegistrationPageObject.txtstate_sensa;
            await stateselect.selectByVisibleText(state);
            if ((await url).includes('registration')) {
                const firstname = faker.person.firstName();
                const lastName = faker.person.lastName();
                const randomNumber = String(Math.floor(Math.random() * (9999 - 1001 + 1)) + 1001); // Generate a random number between 1001 and 999
                const email = `rjrautomationtest+${firstname}${randomNumber}${lastName}@gmail.com`;
                await elementActions.setValue(RegistrationPageObject.txtregemail_sensa, email);
            }
            const monthdd = await RegistrationPageObject.txtmonth_sensa;
            await monthdd.selectByVisibleText(month);
            const daydd = await RegistrationPageObject.txtday_sensa;
            await daydd.selectByVisibleText(day);
            const yeardd = await RegistrationPageObject.txtyear_sensa;
            await yeardd.selectByVisibleText(year);
            await $('body').click();
            if ((await url).includes('registration.html') || (await url).includes('americanspirit')) {
                await elementActions.clickusingJavascript(RegistrationPageObject.txtweCertify_sensa);
                await elementActions.clickusingJavascript(RegistrationPageObject.txtcontinuetoNextStep_sensa);
            } else {
                await elementActions.clickusingJavascript(sensaForgotusernamePageObject.btncontinue_sensa);
            }
            console.log('Entered Details Successfully');
        } catch (error) {
            logger.error('Unable to enter details', { error });
            throw error;
        }
    }

    async clickonforgotUsername() {
        try {
            await elementActions.click(sensaForgotusernamePageObject.lnkforgotUsername_sensa);
            await elementActions.assertion(AccountPageObject.lbllegalName_sensa);
            console.log('Navigated to ForgotUsername Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to ForgotUsername Page', { error });
            throw error;
        }
    }

    async enterdetailsinidentityPage(answer: string) {
        try {
            await elementActions.assertion(RegistrationPageObject.lblaccountSetup_sensa);
            const textbox = await RegistrationPageObject.txtsecuritynswer_sensa;
            await textbox.addValue(answer);
            await browser.execute((_selector) => {
                const el = document.querySelector('#regSecurityAnswer') as HTMLInputElement;
                if (el) {
                    el.dispatchEvent(new Event('change', { bubbles: true }));
                }
            }, await RegistrationPageObject.txtsecuritynswer_sensa);
            await elementActions.clickusingJavascript(sensaForgotusernamePageObject.btnnextStep_sensa);
            console.log('Entered Answer Successfully');
        } catch (error) {
            logger.error('Failed to Enter Answer', { error });
            throw error;
        }
    }

    async verifyusernameandenterpasswordinaccessAccountPage(username: string, password: string) {
        try {
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.txtusername_sensa, username);
            const textbox = await sensaAccountPageObject.txtpassword_sensa;
            await textbox.setValue(password);
            await browser.execute((_selector) => {
                const el = document.querySelector('#loginPassword') as HTMLInputElement;
                if (el) {
                    el.dispatchEvent(new Event('change', { bubbles: true }));
                }
            }, await sensaAccountPageObject.txtpassword_sensa);
            await elementActions.clickusingJavascript(sensaForgotusernamePageObject.btnlogin_sensa);
            console.log('Validated Username Successfully');
        } catch (error) {
            logger.error('Failed to Validate Username', { error });
            throw error;
        }
    }

    async tellUsaboutpageDetailsinForgotusernamepagevalidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdrForgotUsername = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrforgotusername');
            const lblfillform = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfillform');
            const lblallfields = testData.getCellValue(SHEET_NAME, scenarioname, 'lblallfieldsnoted');
            const lbllegalname = testData.getCellValue(SHEET_NAME, scenarioname, 'lbllegalname');
            const lblgovtid = testData.getCellValue(SHEET_NAME, scenarioname, 'lblgovtid');
            const lblcurrentaddr = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcurrentaddress');
            const lblfirstName = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfirstname');
            const lbllastName = testData.getCellValue(SHEET_NAME, scenarioname, 'lbllastname');
            const lblstreetaddr = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstreetaddress');
            const lblzipcode = testData.getCellValue(SHEET_NAME, scenarioname, 'lblzipcode');
            const lblcity = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcity');
            const lbldob = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldob');
            const lblstate = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstate');
            const lblmonth = testData.getCellValue(SHEET_NAME, scenarioname, 'lblmonth');
            const lbldate = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldate');
            const lblyear = testData.getCellValue(SHEET_NAME, scenarioname, 'lblyear');
            const lblassistance = testData.getCellValue(SHEET_NAME, scenarioname, 'lblassistance');
            const lblassistanceProd = testData.getCellValue(SHEET_NAME, scenarioname, 'lblassistance_Prod');
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.hdrforgotusername_sensa, hdrForgotUsername);
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblfillrequiredinformation_sensa, lblfillform);
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblallfields_sensa, lblallfields);
            await sensaAccountPage.mssgcomparision(AccountPageObject.lbllegalName_sensa, lbllegalname);
            await sensaAccountPage.mssgcomparision(AccountPageObject.lblgovtissue_sensa, lblgovtid);
            await elementActions.click(AccountPageObject.txtfirstName_sensa);
            await sensaAccountPage.mssgcomparision(AccountPageObject.lblfirstName_sensa, lblfirstName);
            await elementActions.click(AccountPageObject.txtlastName_sensa);
            await sensaAccountPage.mssgcomparision(AccountPageObject.lbllastName_sensa, lbllastName);
            await sensaAccountPage.mssgcomparision(AccountPageObject.lblcurrentAddr_sensa, lblcurrentaddr);
            await elementActions.click(AccountPageObject.txtcurrentAddr_sensa);
            await sensaAccountPage.mssgcomparision(AccountPageObject.lblstretaddr_sensa, lblstreetaddr);
            await elementActions.click(AccountPageObject.txtzipCode_sensa);
            await sensaAccountPage.mssgcomparision(AccountPageObject.lblzipcode_sensa, lblzipcode);
            await elementActions.click(AccountPageObject.txtaptOpt_sensa);
            await elementActions.click(AccountPageObject.txtcity_sensa);
            await sensaAccountPage.mssgcomparision(AccountPageObject.lblcity_sensa, lblcity);
            await elementActions.click(AccountPageObject.txtstate_sensa);
            await sensaAccountPage.mssgcomparision(AccountPageObject.lblstate_sensa, lblstate);
            await sensaAccountPage.mssgcomparision(AccountPageObject.lbldob_sensa, lbldob);
            await elementActions.click(RegistrationPageObject.txtmonth_sensa);
            await sensaAccountPage.mssgcomparision(AccountPageObject.lblmonth_sensa, lblmonth);
            await elementActions.click(RegistrationPageObject.txtday_sensa);
            await sensaAccountPage.mssgcomparision(AccountPageObject.lbldate_sensa, lbldate);
            await elementActions.click(RegistrationPageObject.txtyear_sensa);
            await sensaAccountPage.mssgcomparision(AccountPageObject.lblyear_sensa, lblyear);
            const url = browser.getUrl();
            if ((await url).includes('aem')) {
                await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblneedassistence_sensa, lblassistance);
            } else {
                await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblneedassistence_sensa, lblassistanceProd);
            }
            await elementActions.assertion(sensaForgotusernamePageObject.btncontinue_sensa);
            await elementActions.assertion(sensaForgotusernamePageObject.lnlgotologin_sensa);
            console.log('Validated the Content in Tellus about page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Tellus about page', { error });
            throw error;
        }
    }


    async verifyIdentitypagevalidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdrverifyidentity = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrverifyidentity');
            const lblanswerthequestion = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpleaseanswerquestion');
            const lblquestion = testData.getCellValue(SHEET_NAME, scenarioname, 'lblquestion');
            const lblquestionProd = testData.getCellValue(SHEET_NAME, scenarioname, 'lblquestionprod');
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.hdrverifyIdentity_sensa, hdrverifyidentity);
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblpleaseanswer_sensa, lblanswerthequestion);
            const url = browser.getUrl();
            if ((await url).includes('aem')) {
                await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblquestion_sensa, lblquestion);
            } else {
                await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblquestion_sensa, lblquestionProd);
            }
            await elementActions.assertion(RegistrationPageObject.lblaccountSetup_sensa);
            await elementActions.assertion(sensaForgotusernamePageObject.btnnextStep_sensa);
            console.log('Validated the Content in verify Identity page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in verify Identity page', { error });
            throw error;
        }
    }

    async UsernamePagevalidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdrgoodnews = testData.getCellValue(SHEET_NAME, scenarioname, 'lblgoodnews');
            const lblhereisusername = testData.getCellValue(SHEET_NAME, scenarioname, 'lblhersusername');
            const lblenterpassword = testData.getCellValue(SHEET_NAME, scenarioname, 'lblenterpassword');
            const lblresetaPassword = testData.getCellValue(SHEET_NAME, scenarioname, 'lblresetpassword');
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblgoodnews_sensa, hdrgoodnews);
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblhereisusername_sensa, lblhereisusername);
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblenterpassword_sensa, lblenterpassword);
            await elementActions.assertion(sensaAccountPageObject.txtpassword_sensa);
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblresetpassword_sensa, lblresetaPassword);
            await elementActions.assertion(sensaForgotusernamePageObject.btnlogin_sensa);
            console.log('Validated the Content in Username page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Username page', { error });
            throw error;
        }
    }

    async wrongSecurityAnswer(answer: string, errormssg: string) {
        try {
            const textbox = await RegistrationPageObject.txtsecuritynswer_sensa;
            await textbox.setValue(answer);
            await browser.execute((_selector) => {
                const el = document.querySelector('#regSecurityAnswer') as HTMLInputElement;
                if (el) {
                    el.dispatchEvent(new Event('change', { bubbles: true }));
                }
            }, await RegistrationPageObject.txtsecuritynswer_sensa);
            await elementActions.clickusingJavascript(sensaForgotusernamePageObject.btnnextStep_sensa);
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrormssg_sensa, errormssg);
            console.log('Validated Error Message Successfully');
        } catch (error) {
            logger.error('Failed to Validate Error Message', { error });
            throw error;
        }
    }

    async navigateToContactUsPage(answer: string) {
        try {
            const textbox = await RegistrationPageObject.txtsecuritynswer_sensa;
            await textbox.setValue(answer);
            await browser.execute((_selector) => {
                const el = document.querySelector('#regSecurityAnswer') as HTMLInputElement;
                if (el) {
                    el.dispatchEvent(new Event('change', { bubbles: true }));
                }
            }, await RegistrationPageObject.txtsecuritynswer_sensa);
            await elementActions.clickusingJavascript(sensaForgotusernamePageObject.btnnextStep_sensa);
            const url = browser.getUrl();
            if ((await url).includes('camel')) {
                await elementActions.assertion(camelLoginPageObject.lblFAQcontactUs_camel);
            } else if ((await url).includes('americanspirit')) {
                if ((await url).includes('aem')) {
                    await expect(sensaForgotusernamePageObject.lblcontactUs_nas).toBeDisplayed();
                } else {
                    await expect(sensaForgotusernamePageObject.lblcontactUs_prodnas).toBeDisplayed();
                }
            } else if ((await url).includes('sensa')) {
                await elementActions.assertion(sensaForgotusernamePageObject.lblcontactUs_sensa);
            }
            console.log('Navigated to Contact us page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Contact us page', { error });
            throw error;
        }
    }


    async tellUsaboutpageAllErrorsvalidation(filename: string, sheetname: string, scenarioname: string) {
        try {
            await elementActions.waitForDisplayed(AccountPageObject.txtfirstName_sensa);
            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const lblfirstName = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerrorfirstname');
            const lbllastName = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerrorlastname');
            const lblstreetaddr = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerrorstreetaddr');
            const lblzipcode = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerrorzipcode');
            const lblcity = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerrorcity');
            const lblstate = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerrorstate');
            const lblmonth = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerrormonth');
            const lbldate = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerrordate');
            const lblyear = testData.getCellValue(SHEET_NAME, scenarioname, 'lblerroryear');
            await elementActions.click(AccountPageObject.txtfirstName_sensa);
            await elementActions.click(AccountPageObject.txtlastName_sensa);
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorFirstName_sensa, lblfirstName);

            if (driver.isIOS) {
                await elementActions.click(AccountPageObject.txtlastName_sensa);
                await elementActions.click(AccountPageObject.txtfirstName_sensa);
            } else {
                await AccountPageObject.txtlastName_sensa.doubleClick();
                await AccountPageObject.txtfirstName_sensa.doubleClick();
            }
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorLastName_sensa, lbllastName);
            if (driver.isIOS) {
                await elementActions.click(AccountPageObject.txtcurrentAddr_sensa);
                await elementActions.click(AccountPageObject.txtlastName_sensa);
            } else {
                await AccountPageObject.txtcurrentAddr_sensa.doubleClick();
                await AccountPageObject.txtlastName_sensa.doubleClick();
            }
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorStreetaddr_sensa, lblstreetaddr);
            if (driver.isIOS) {
                await elementActions.click(RegistrationPageObject.txtzipCode_sensa);
                await elementActions.click(AccountPageObject.txtcurrentAddr_sensa);
            } else {
                await AccountPageObject.txtzipCode_sensa.doubleClick();
                await AccountPageObject.txtcurrentAddr_sensa.doubleClick();
            }
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorZipCode_sensa, lblzipcode);
            if (driver.isIOS) {
                await elementActions.click(AccountPageObject.txtcity_sensa);
                await elementActions.click(AccountPageObject.txtzipCode_sensa);
            } else {
                await AccountPageObject.txtcity_sensa.doubleClick();
                await AccountPageObject.txtzipCode_sensa.doubleClick();
            }
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorCity_sensa, lblcity);
            if (driver.isIOS) {
                await elementActions.click(AccountPageObject.txtstate_sensa);
                await elementActions.click(AccountPageObject.txtcity_sensa);
            } else {
                await AccountPageObject.txtstate_sensa.doubleClick();
                await AccountPageObject.txtcity_sensa.doubleClick();

            }
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorState_sensa, lblstate);
            if (driver.isIOS) {
                await elementActions.click(RegistrationPageObject.txtmonth_sensa);
                await elementActions.click(RegistrationPageObject.txtday_sensa);
            } else {
                await RegistrationPageObject.txtmonth_sensa.doubleClick();
                await RegistrationPageObject.txtday_sensa.doubleClick();
            }
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorMonth_sensa, lblmonth);
            if (driver.isIOS) {
                await elementActions.click(RegistrationPageObject.txtday_sensa);
                await elementActions.click(RegistrationPageObject.txtmonth_sensa);
            } else {
                await RegistrationPageObject.txtday_sensa.doubleClick();
                await RegistrationPageObject.txtmonth_sensa.doubleClick();
            }
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorDate_sensa, lbldate);
            if (driver.isIOS) {
                await elementActions.click(RegistrationPageObject.txtyear_sensa);
                await elementActions.click(RegistrationPageObject.txtmonth_sensa);
            } else {
                await RegistrationPageObject.txtyear_sensa.doubleClick();
                await RegistrationPageObject.txtmonth_sensa.doubleClick();
            }
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorYear_sensa, lblyear);
            console.log('Validated the Errors in Tellus about page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Errors in Tellus about page', { error });
            throw error;
        }
    }

    async tellusPageErrorValidation(filename: string, sheetname: string, scenarioname: string) {
        try {
            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const lblErrorMessage = testData.getCellValue(SHEET_NAME, scenarioname, 'lblanerroroccured');
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblAnErrorOccured_sensa, lblErrorMessage);
            const url = driver.getUrl();
            if ((await url).includes('forgot-username')) {
                await elementActions.click(sensaForgotusernamePageObject.lnlgotologin_sensa);
                await elementActions.assertion(RegistrationPageObject.lblsignIn_sensa);
                this.clickonforgotUsername();
            }
            console.log('Validated Error Message Successfully');
        } catch (error) {
            logger.error('Failed to Validate Error Message', { error });
            throw error;
        }
    }


    async answerFieldErrorValidation(filename: string, sheetname: string, scenarioname: string, answer: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            await elementActions.click(RegistrationPageObject.txtsecuritynswer_sensa);
            await elementActions.click(sensaForgotusernamePageObject.hdrverifyIdentity_sensa);
            const lblErrorMessage = testData.getCellValue(SHEET_NAME, scenarioname, 'lblanswerFieldError');
            await sensaAccountPage.mssgcomparision(sensaForgotusernamePageObject.lblerrorPassword_sensa, lblErrorMessage);
            await this.enterdetailsinidentityPage(answer);

            console.log('Validated Error Message Successfully');
        } catch (error) {
            logger.error('Failed to Validate Error Message', { error });
            throw error;
        }
    }

    async clickonresetYourPassword() {
        try {
            await elementActions.assertion(sensaForgotusernamePageObject.txtusername_sensa);
            await elementActions.waitForDisplayed(sensaForgotusernamePageObject.lnkresetPassword_sensa);
            await elementActions.clickusingJavascript(sensaForgotusernamePageObject.lnkresetPassword_sensa);
            await elementActions.assertion(sensaForgotusernamePageObject.lblPasswordHeader_sensa);
            console.log('Navigated to Forgot Password Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Forgot Password Page', { error });
            throw error;
        }
    }

}
export default new ForgotUsername();
