Feature: SGW Message Validation for Camel Website

    Scenario Outline: Validate SGW Message in all Pages for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        Then The user Validates SGW Message with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user login with valid user id <Username> and password <Password>
        Then The user Validates SGW Message with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user click on MyAccount link
        Then The user Validates SGW Message with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user clicks on Coupons Link
        Then The user Validates SGW Message with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user clicks on Promotions
        Then The user Validates SGW Message with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user clicks on Hump
        Then The user Validates SGW Message with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user validate the Store locator page
        Then The user Validates SGW Message with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user clicks on Products
        Then The user Validates SGW Message with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user validates that successfully logged out of camel brand


        @CamelSGW_Validation_QA
        Examples:
            | Brand | URL                         | Username                            | Password  | filename              | sheetname | scenarioname         |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | SGW       | Validate SGW Message |

        @CamelSGW_Validation_PROD
        Examples:
            | Brand | URL                   | Username                             | Password  | filename              | sheetname | scenarioname         |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | SGW       | Validate SGW Message |
