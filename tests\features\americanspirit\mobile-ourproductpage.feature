Feature: Our Product Validation for Nas Website

    Scenario Outline: Validate Our Product Page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the nas application successfully
        When The user clicks on Our Products
        Then The user Validates Nas OurProduct Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user validates that successfully logged out

        @NasOurProductPage_Validation_QA
        Examples:
            | Brand | URL                                  | Username                           | Password  | filename            | sheetname   | scenarioname               |
            | Nas   | https://aem-stage.americanspirit.com | <EMAIL> | Password1 | aem-mobile-nas.json | Our Product | Validate Our Product Pages |

        @NasOurProductPage_Validation_PROD
        Examples:
            | Brand | URL                            | Username                                 | Password  | filename            | sheetname   | scenarioname               |
            | Nas   | https://www.americanspirit.com | <EMAIL> | Password1 | aem-mobile-nas.json | Our Product | Validate Our Product Pages |