import { Then, When } from '@wdio/cucumber-framework';
import sensaForgotpasswordPage from '../../pages/commonteps/forgotpassword.page.ts';
import logger from '../../support/utils/logger.util.ts';
import sensaForgotpasswordPageObject from '../../page-object/sensa/sensa-forgotpassword.pageObject.ts';
import sensaRegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';
import camelLoginPageObject from '../../page-object/camel/camel-login.pageObject.ts';

When(/^The user click on ForgotPassword link$/, async function () {
    await sensaForgotpasswordPage.clickonforgotPasswordlink();
    await expect(sensaForgotpasswordPageObject.hdrforgotPassword_sensa).toBeDisplayed();
    logger.info('Navigated to ForgotPassword Page Successfully');

});

Then(/^The user enter valid username (.*) and click Continue$/, async function (email: string) {
    await sensaForgotpasswordPage.entervalidemailpage(email);
    await expect(sensaForgotpasswordPageObject.lblcheckyouremail_sensa).toBeDisplayed();
    logger.info('Entered Email Successfully');

});

Then(/^The user enter invalid username (.*) and validate error Message as (.*)$/, async function (email: string, errormessage: string) {
    await sensaForgotpasswordPage.enterinvalidemailpage(email, errormessage);
    await expect(sensaForgotpasswordPageObject.lblerroremail_sensa).toHaveText(errormessage);
    logger.info('Navigated to Forgot Password Page Successfully');
});

Then(/^The user validates the Forgot Password page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaForgotpasswordPage.forgotPasswordpagevalidation(filepath, sheetname, scenarioname);
    logger.info('Validated the Forgot Password page Successfully');
});

Then(/^The user validates the Reset Password page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaForgotpasswordPage.resetPasswordpagevalidation(filepath, sheetname, scenarioname);
    logger.info('Validated the Reset Password page Successfully');
});

Then(/^The user enter valid data in Password (.*) and ConfirmPassword (.*) fields and click Continue on ResetPassword form page$/, async function (password: string, Cpassword: string) {
    await sensaForgotpasswordPage.entervalidPassword(password, Cpassword);
    const url = await browser.getUrl();
    if (url.includes('sensa')) {
        await expect(sensaRegistrationPageObject.lbllogo_sensa).toBeDisplayed();
    } else if (url.includes('americanspirit')) {
        await expect(camelLoginPageObject.lbllogo_nas).toBeDisplayed();
    }else {
        await expect(camelLoginPageObject.lbllogo_camel).toBeDisplayed();
    }
    logger.info('Navigated to Home Page Successfully');
});
Then(/^The user validates the Reset Request Success Page page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaForgotpasswordPage.resetSuccessvalidation(filepath, sheetname, scenarioname);
    logger.info('Validated the Reset Request Success Page page Successfully');
});



