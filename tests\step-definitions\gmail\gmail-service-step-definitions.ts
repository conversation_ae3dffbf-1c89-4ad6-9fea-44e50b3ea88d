import { Given, When, Then } from '@wdio/cucumber-framework';
import { expect } from '@wdio/globals';
import GmailUtil from '../../support/utils/gmail.util.ts';
import logger from '../../support/utils/logger.util.ts';
import { EmailObject } from '../../support/utils/gmail.util.ts';
const gmailUtil = new GmailUtil();
let currentEmails: EmailObject[] = [];
let currentEmail: EmailObject | null = null;
let emailCount: number = 0;
let extractedLinks: string[] = [];

// Background step
Given(/^the Gmail service is configured and authenticated$/, async function () {
    await logger.info('Step: Verify Gmail service configuration');
    // The Gmail service should be configured in wdio.conf.ts
    // This step serves as a documentation step and basic validation
    expect(browser.checkInbox).toBeDefined();
    await logger.info('Gmail service is properly configured');
});

// Basic Gmail functionality
When(/^I check the Gmail inbox for emails from (.*)$/, async function (fromEmail: string) {
    await logger.info(`Step: Check Gmail inbox for emails from ${fromEmail}`);
    try {
        currentEmails = await gmailUtil.checkInbox({
            from: fromEmail,
            includeBody: true,
        });
        await logger.info(`Found ${currentEmails ? currentEmails.length : 0} emails from ${fromEmail}`);
    } catch (error) {
        await logger.error(`Error checking inbox: ${error}`);
        throw error;
    }
});

Then(/^I should receive a list of emails$/, async function () {
    await logger.info('Step: Verify that emails list is received');
    expect(Array.isArray(currentEmails)).toBeTruthy();
    await logger.info(`Received ${currentEmails.length} emails`);
});

Then(/^the emails should contain valid email properties$/, async function () {
    await logger.info('Step: Verify email properties');
    if (currentEmails.length > 0) {
        const email = currentEmails[0];
        // Verify basic email properties
        expect(email).toHaveProperty('subject');
        expect(email).toHaveProperty('from');
        expect(email).toHaveProperty('date');
        await logger.info('Email properties validation passed');
    } else {
        await logger.info('No emails to validate properties');
    }
});

// Search functionality
When(/^I search for email with subject containing (.*)$/, async function (subjectText: string) {
    await logger.info(`Step: Search for emails with subject containing ${subjectText}`);
    console.log(`Step: Search for emails with subject containing ${subjectText}`);

    try {
        // Use the new waitForLatestEmailBySubject function for better reliability
        console.log(`Waiting for latest email with subject containing: ${subjectText}`);

        currentEmail = await gmailUtil.waitForLatestEmailBySubject(
            subjectText,
            true, // includeBody
            60000, // 60 second timeout
            3000, // 3 second intervals
            5000, // 5 second buffer for email delivery delays
        );

        if (currentEmail) {
            await logger.info(`Found latest email with subject containing ${subjectText}`);
            console.log(`Found email from: ${currentEmail.from}`);
            console.log(`Email date: ${currentEmail.date}`);
            console.log(`Email subject: ${currentEmail.subject}`);
        }
    } catch (error) {
        console.log(`Error searching emails: ${error}`);
        await logger.error(`Error searching emails: ${error}`);

        // Fallback to regular search if waitForEmail fails
        try {
            console.log(`Fallback: Trying regular search for emails with subject containing: ${subjectText}`);
            currentEmail = await gmailUtil.searchLatestEmailBySubject(subjectText, true);

            if (currentEmail) {
                console.log(`Fallback successful - Found email from: ${currentEmail.from}, date: ${currentEmail.date}`);
            }
        } catch (fallbackError) {
            console.log(`Fallback also failed: ${fallbackError}`);
            currentEmail = null;
        }
    }
});

Then(/^I should find emails matching the subject criteria$/, async function () {
    await logger.info('Step: Verify emails match subject criteria');
    expect(Array.isArray(currentEmails)).toBeTruthy();
    await logger.info(`Found ${currentEmails.length} matching emails`);
});

Then(/^each email should have the subject containing "([^"]*)"$/, async function (subjectText: string) {
    await logger.info(`Step: Verify each email subject contains ${subjectText}`);
    if (currentEmails.length > 0) {
        for (let index = 0; index < currentEmails.length; index++) {
            const email = currentEmails[index];
            expect(email.subject).toContain(subjectText);
            await logger.info(`Email ${index + 1} subject validation passed`);
        }
    } else {
        await logger.info('No emails to validate subject');
    }
});

// New step definition specifically for getting the latest email by subject
When(/^I get the latest email with subject containing (.*)$/, async function (subjectText: string) {
    await logger.info(`Step: Get the latest email with subject containing ${subjectText}`);
    console.log(`Step: Get the latest email with subject containing ${subjectText}`);

    try {
        console.log(`Searching for the most recent email with subject containing: ${subjectText}`);

        currentEmail = await gmailUtil.waitForLatestEmailBySubject(
            subjectText,
            true, // includeBody
            60000, // 60 second timeout
            3000, // 3 second intervals
            5000, // 5 second buffer for email delivery delays
        );

        if (currentEmail) {
            await logger.info(`Successfully retrieved latest email with subject containing ${subjectText}`);
            console.log('Latest email found:');
            console.log(`  From: ${currentEmail.from}`);
            console.log(`  Date: ${currentEmail.date}`);
            console.log(`  Subject: ${currentEmail.subject}`);
        }
    } catch (error) {
        console.log(`Error getting latest email: ${error}`);
        await logger.error(`Error getting latest email: ${error}`);
        currentEmail = null;
    }
});

// Enhanced step definition for high-volume email scenarios
When(/^I wait for the latest email with subject containing "([^"]*)" with (\d+) second buffer$/, async function (subjectText: string, bufferSeconds: string) {
    await logger.info(`Step: Wait for latest email with subject containing ${subjectText} with ${bufferSeconds}s buffer`);
    console.log(`Step: Wait for latest email with subject containing ${subjectText} with ${bufferSeconds}s buffer`);

    try {
        const bufferMs = parseInt(bufferSeconds) * 1000;
        console.log(`Using ${bufferMs}ms buffer for email delivery delays`);

        currentEmail = await gmailUtil.waitForLatestEmailBySubject(
            subjectText,
            true, // includeBody
            90000, // 90 second timeout for high-volume scenarios
            2000, // 2 second intervals for more frequent checks
            bufferMs, // Custom buffer time
        );

        if (currentEmail) {
            await logger.info('Successfully retrieved latest email with custom buffer');
            console.log('Latest email found with custom buffer:');
            console.log(`  From: ${currentEmail.from}`);
            console.log(`  Date: ${currentEmail.date}`);
            console.log(`  Subject: ${currentEmail.subject}`);
        }
    } catch (error) {
        console.log(`Error getting latest email with custom buffer: ${error}`);
        await logger.error(`Error getting latest email with custom buffer: ${error}`);
        currentEmail = null;
    }
});

// Step definition for precise timestamp control
When(/^I record the current timestamp and wait for email with subject containing "([^"]*)"$/, async function (subjectText: string) {
    await logger.info(`Step: Record timestamp and wait for email with subject containing ${subjectText}`);
    console.log(`Step: Record timestamp and wait for email with subject containing ${subjectText}`);

    try {
        // Record timestamp before any action
        const actionTime = new Date();
        console.log(`Recorded action timestamp: ${actionTime.toISOString()}`);

        // Wait a moment to simulate action that triggers email
        await new Promise(resolve => setTimeout(resolve, 1000));

        currentEmail = await gmailUtil.waitForNewEmailBySubjectAfterTime(
            subjectText,
            actionTime,
            true, // includeBody
            60000, // 60 second timeout
            3000, // 3 second intervals
        );

        if (currentEmail) {
            await logger.info('Successfully retrieved email received after action timestamp');
            console.log('Email received after action:');
            console.log(`  From: ${currentEmail.from}`);
            console.log(`  Date: ${currentEmail.date}`);
            console.log(`  Subject: ${currentEmail.subject}`);
            console.log(`  Received after: ${actionTime.toISOString()}`);
        }
    } catch (error) {
        console.log(`Error getting email after timestamp: ${error}`);
        await logger.error(`Error getting email after timestamp: ${error}`);
        currentEmail = null;
    }
});

// Latest email functionality
When(/^I get the latest email from (.*)$/, async function (senderEmail: string) {
    await logger.info(`Step: Get latest email from ${senderEmail}`);
    try {
        currentEmail = await gmailUtil.getLatestEmailFrom(senderEmail, true);
        await logger.info(`Retrieved latest email from ${senderEmail}`);
    } catch (error) {
        await logger.error(`Error getting latest email: ${error}`);
        currentEmail = null;
    }
});

Then(/^I should receive the most recent email from that sender$/, async function () {
    await logger.info('Step: Verify latest email is received');
    if (currentEmail) {
        expect(currentEmail).toBeDefined();
        expect(currentEmail).toHaveProperty('date');
        await logger.info('Latest email validation passed');
    } else {
        await logger.info('No latest email found');
    }
});

Then(/^the email should have a valid date and content$/, async function () {
    await logger.info('Step: Verify email date and content');
    if (currentEmail) {
        expect(currentEmail.date).toBeDefined();
        expect(new Date(currentEmail.date)).toBeInstanceOf(Date);
        await logger.info('Email date and content validation passed');
    } else {
        await logger.info('No email to validate');
    }
});

// Content verification
Given(/^I have an email from "([^"]*)" with subject containing "([^"]*)"$/, async function (senderEmail: string, subjectText: string) {
    await logger.info(`Step: Get email from ${senderEmail} with subject containing ${subjectText}`);
    try {
        const emails = await gmailUtil.checkInbox({
            from: senderEmail,
            subject: subjectText,
            includeBody: true,
        });
        currentEmail = emails.length > 0 ? emails[0] : null;
        await logger.info(`Found email: ${currentEmail ? 'Yes' : 'No'}`);
    } catch (error) {
        await logger.error(`Error finding email: ${error}`);
        currentEmail = null;
    }
});

When(/^I verify the email content contains "([^"]*)"$/, async function (expectedText: string) {
    await logger.info(`Step: Verify email content contains ${expectedText}`);
    if (currentEmail) {
        const contentFound = gmailUtil.verifyEmailContent(currentEmail, expectedText, true);
        expect(contentFound).toBeTruthy();
        await logger.info('Email content verification passed');
    } else {
        await logger.info('No email available for content verification');
    }
});

Then(/^the content verification should be successful$/, async function () {
    await logger.info('Step: Confirm content verification success');
    // This step is mainly for BDD readability
    await logger.info('Content verification completed successfully');
});

Then(/^I should be able to extract links from the email$/, async function () {
    await logger.info('Step: Extract links from email');
    if (currentEmail) {
        extractedLinks = gmailUtil.extractLinksFromEmail(currentEmail);
        expect(Array.isArray(extractedLinks)).toBeTruthy();
        await logger.info(`Extracted ${extractedLinks.length} links from email`);
    } else {
        await logger.info('No email available for link extraction');
        extractedLinks = [];
    }
});

// Wait for email functionality
When(/^I trigger an action that sends an email to "([^"]*)"$/, async function (recipientEmail: string) {
    await logger.info(`Step: Trigger action to send email to ${recipientEmail}`);
    // This is a placeholder step for actions that would trigger email sending
    // In real tests, this would involve UI interactions or API calls
    await logger.info('Email trigger action simulated');
});

When(/^I wait for an email from "([^"]*)" with subject "([^"]*)"$/, async function (senderEmail: string, subjectText: string) {
    await logger.info(`Step: Wait for email from ${senderEmail} with subject ${subjectText}`);
    try {
        currentEmail = await gmailUtil.waitForEmail({
            from: senderEmail,
            subject: subjectText,
            includeBody: true,
        }, 30000, 5000); // 30 second timeout, 5 second intervals
        await logger.info('Email received within timeout');
    } catch (error) {
        await logger.error(`Error waiting for email: ${error}`);
        currentEmail = null;
    }
});

Then(/^the email should arrive within the specified timeout$/, async function () {
    await logger.info('Step: Verify email arrived within timeout');
    if (currentEmail) {
        expect(currentEmail).toBeDefined();
        await logger.info('Email arrival validation passed');
    } else {
        await logger.info('Email did not arrive within timeout');
    }
});

Then(/^the email content should be accessible$/, async function () {
    await logger.info('Step: Verify email content is accessible');
    if (currentEmail) {
        expect(currentEmail).toHaveProperty('body');
        await logger.info('Email content accessibility validation passed');
    } else {
        await logger.info('No email content to verify');
    }
});

// Email count functionality
When(/^I count emails from "([^"]*)" received after "([^"]*)"$/, async function (senderEmail: string, afterDate: string) {
    await logger.info(`Step: Count emails from ${senderEmail} after ${afterDate}`);
    try {
        const afterDateObj = new Date(afterDate);
        emailCount = await gmailUtil.getEmailCount({
            from: senderEmail,
            after: afterDateObj,
        });
        await logger.info(`Email count: ${emailCount}`);
    } catch (error) {
        await logger.error(`Error counting emails: ${error}`);
        emailCount = 0;
    }
});

Then(/^I should get a valid count of matching emails$/, async function () {
    await logger.info('Step: Verify email count is valid');
    expect(typeof emailCount).toBe('number');
    await logger.info(`Valid email count received: ${emailCount}`);
});

Then(/^the count should be a non-negative number$/, async function () {
    await logger.info('Step: Verify count is non-negative');
    expect(emailCount).toBeGreaterThanOrEqual(0);
    await logger.info('Email count validation passed');
});

// Attachments functionality
When(/^I check for emails from "([^"]*)" with attachments$/, async function (senderEmail: string) {
    await logger.info(`Step: Check for emails with attachments from ${senderEmail}`);
    try {
        currentEmails = await gmailUtil.checkInbox({
            from: senderEmail,
            includeAttachments: true,
        });
        await logger.info(`Found ${currentEmails.length} emails from ${senderEmail}`);
    } catch (error) {
        await logger.error(`Error checking for emails with attachments: ${error}`);
        currentEmails = [];
    }
});

Then(/^I should find emails that include attachments$/, async function () {
    await logger.info('Step: Verify emails include attachments');
    if (currentEmails.length > 0) {
        // Check if any emails have attachments
        const emailsWithAttachments = currentEmails.filter(email =>
            email.attachments && email.attachments.length > 0,
        );
        await logger.info(`Found ${emailsWithAttachments.length} emails with attachments`);
    } else {
        await logger.info('No emails found to check for attachments');
    }
});

Then(/^the attachment data should be accessible$/, async function () {
    await logger.info('Step: Verify attachment data accessibility');
    if (currentEmails.length > 0) {
        const emailWithAttachment = currentEmails.find(email =>
            email.attachments && email.attachments.length > 0,
        );
        if (emailWithAttachment) {
            expect(emailWithAttachment.attachments).toBeDefined();
            expect(Array.isArray(emailWithAttachment.attachments)).toBeTruthy();
            await logger.info('Attachment data accessibility validation passed');
        } else {
            await logger.info('No emails with attachments found');
        }
    }
});

// Labels functionality
When(/^I check emails in the "([^"]*)" label$/, async function (label: string) {
    await logger.info(`Step: Check emails in ${label} label`);
    try {
        currentEmails = await gmailUtil.checkInbox({
            label: label,
            includeBody: true,
        });
        await logger.info(`Found ${currentEmails.length} emails in ${label} label`);
    } catch (error) {
        await logger.error(`Error checking emails in label: ${error}`);
        currentEmails = [];
    }
});

Then(/^I should receive emails from the specified label$/, async function () {
    await logger.info('Step: Verify emails from specified label');
    expect(Array.isArray(currentEmails)).toBeTruthy();
    await logger.info(`Received ${currentEmails.length} emails from label`);
});

Then(/^the emails should be properly categorized$/, async function () {
    await logger.info('Step: Verify email categorization');
    // This is mainly a documentation step for BDD
    await logger.info('Email categorization verification completed');
});

// Date filter functionality
When(/^I search for emails received between "([^"]*)" and "([^"]*)"$/, async function (startDate: string, endDate: string) {
    await logger.info(`Step: Search emails between ${startDate} and ${endDate}`);
    try {
        const afterDateObj = new Date(startDate);
        const beforeDateObj = new Date(endDate);
        currentEmails = await gmailUtil.checkInbox({
            after: afterDateObj,
            before: beforeDateObj,
            includeBody: true,
        });
        await logger.info(`Found ${currentEmails.length} emails in date range`);
    } catch (error) {
        await logger.error(`Error searching emails by date: ${error}`);
        currentEmails = [];
    }
});

Then(/^I should find emails within the specified date range$/, async function () {
    await logger.info('Step: Verify emails within date range');
    expect(Array.isArray(currentEmails)).toBeTruthy();
    await logger.info(`Found ${currentEmails.length} emails within date range`);
});

Then(/^all returned emails should have dates within the range$/, async function () {
    await logger.info('Step: Verify all email dates are within range');
    if (currentEmails.length > 0) {
        for (let index = 0; index < currentEmails.length; index++) {
            const email = currentEmails[index];
            expect(email.date).toBeDefined();
            const emailDate = new Date(email.date);
            expect(emailDate).toBeInstanceOf(Date);
            await logger.info(`Email ${index + 1} date validation passed`);
        }
    } else {
        await logger.info('No emails to validate dates');
    }
});

Then(/^The user click on the link with text (.*) in the email$/, async function (linkText: string) {
    await logger.info(`Step: Click on the link with text "${linkText}" in the email`);
    if (currentEmail) {
        await gmailUtil.clickOnLinkInEmail(currentEmail, linkText);
    } else {
        await logger.info('No email available to click the link');
    }
});