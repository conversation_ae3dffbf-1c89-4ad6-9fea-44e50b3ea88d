
class sensaStorlocatorPgeObject {

    //StoreLocator
    get hamburgerMenu() { return $('[aria-label="hamburger menu"]'); }
    get sensaStorelocator() { return $('[title="Store Locator"]'); }
     get sensaStorelocator_NAS() { return $('a[title="Store Finder"]'); }
    get sensafindastore() { return $('//*[contains(text(),"Find A Store")]'); }
    get sensaUsemyLocation() { return $('//*[contains(text(),"Use My Location")]'); }
     get btnfindme_nas() { return $('//*[@class="cmp-button cmp-store-locator__form__find-me"]'); }
    get sensaZipcode() { return $('input[id="zipCode"]'); }
    get sensafindStores() { return $('//*[contains(text(),"Find stores")]'); }
    get sensafindStoresCamel() { return $('//*[contains(text(),"Find Stores")]'); }
    get sensafilterbyproduct() { return $('(//*[contains(text(),"Filter By Product")])[1]'); }
    get sensafilterbyproductCamel() { return $('(//*[contains(text(),"Filter by Product")])[1]'); }
    get sensa1stStore() { return $('(//*[@class="cmp-store-locator__store-list__stores-store__number"])[1]'); }
    get sensa1stStorename() { return $('(//*[@class="cmp-store-locator__store-list__stores-store__headline"])[1]'); }
    get sensa1stStoredirection() { return $('(//a[contains(text(),"Get Directions")])[1]'); }
    get sensafilterbyproducttext() { return $('//*[contains(text(),"Individual store inventory may vary.")]'); }
    get sensaloadMore() { return $('//*[contains(text(),"Load More")]'); }
    get sensaLogout() { return $('(//*[contains(text(),"Log Out")])[2]'); }

    // Filterproduct
    get btnfilterbyproduct() { return $('//span[text()=\'Filter By Product\']'); }
    btnAllproduct() { return $('(//img[@class="cmp-store-locator__filter__img"])'); }
    get closebutton() { return $('//button[@class="cmp-store-locator__filter__modal-close icon-close"]'); }
    btnAllproduct1(i: number) { return $(`(//img[@class='cmp-store-locator__filter__img'])[${i}]`); }
    get btnApplyFilter() { return $('//span[contains(text(),\'Apply Filter\')]/ancestor::button'); }
    get btnfilterproduct() { return $('//button[@class=\'cmp-store-locator__filter__button cmp-button\']'); }
    get labelProduct() { return $('//a[@class=\'cmp-store-locator__filter__text cmp-store-locator__filter__clear-filter-button icon-close\']'); }
    get filterDescription_Stores() { return $('//div[@class=\'cmp-store-locator__store-list__description\']'); }
    get firstStoreNumber_Stores() { return $('//li[contains(@class,\'cmp-store-locator__store-list__stores-store\')][1]//span[@class=\'cmp-store-locator__store-list__stores-store__number\']'); }
    get firstStoreHeader_Stores() { return $('//li[contains(@class,\'cmp-store-locator__store-list__stores-store\')][1]//h4[@class=\'cmp-store-locator__store-list__stores-store__headline\']'); }
    get clearFilter() { return $('(//*[@class="cmp-store-locator__filter__clear-filter-button cmp-button"])[2]//span'); }


    getnumberLocator(index: number) {
        return $(`(//*[@class="cmp-store-locator__store-list__stores-store__number"])[${index}]`);
    }

    getnameLocator(index: number) {
        return $(`(//*[@class="cmp-store-locator__store-list__stores-store__headline"])[${index}]`);
    }

    getaddressLine1(index: number) {
        return $(`((//*[@class="cmp-store-locator__store-list__stores-store__headline"])//following::p[1])[${index}]`);
    }

    getaddressLine2(index: number) {
        return $(`((//*[@class="cmp-store-locator__store-list__stores-store__headline"])//following::p[2])[${index}]`);
    }

    getcontactNumber(index: number) {
        return $(`((//*[@class="cmp-store-locator__store-list__stores-store__headline"])//following::p[3])[${index}]`);
    }

    getdirectionLink(index: number) {
        return $(`(//a[contains(text(),"Get Directions")])[${index}]`);
    }

    getdistanceLocator(index: number) {
        return $(`(//*[@class="cmp-store-locator__store-list__stores-store__distance"])[${index}]`);
    }


    getproducts(index: number) {
        return $(`(//img[@class='cmp-store-locator__filter__img'])[${index}]`);
    }

    get products() {
        return $$('(//img[@class=\'cmp-store-locator__filter__img\'])');
    }
}
export default new sensaStorlocatorPgeObject();