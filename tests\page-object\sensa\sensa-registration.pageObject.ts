class RegistrationPageObject {

    get lblsignIn_sensa() { return $('//*[text()="Sign in"]'); }
    get txtregemail_sensa() { return $('//*[@id="regEmail"]'); }
    get txtjoinnow_sensa() { return $('//*[@class="cmp-login__register-link"]//*[@type="submit"]'); }
    get lblregistrationPage_sensa() { return $('//*[contains(text(),"Tell us about you")]'); }
    get txtFirstName_sensa() { return $('(//*[@name="regFirstName"])[1]'); }
    get txtLastName_sensa() { return $('(//*[@name="regLastName"])[1]'); }
    get txtstate_sensa() { return $('//*[@id="regState"]'); }
    get txtcity_sensa() { return $('//*[@id="regCity"]'); }
    get txtzipCode_sensa() { return $('//*[@id="regZipCode"]'); }
    get txtaddress_sensa() { return $('//*[@id="regStreetAddress"]'); }
    get txtyear_sensa() { return $('//div[@class="cmp-register__smallSection"]//select[@name="regYear"]'); }
    get txtmonth_sensa() { return $('(//*[@name="regMonth"])[1]'); }
    get txtday_sensa() { return $('(//*[@name="regDay"])[1]'); }
    get txtweCertify_sensa() { return $('//span[contains(text(),"I certify that")]'); }
    get txtweCertifycheckbox_sensa() { return $('//input[@id="tosBox"]'); }
    get txtcontinuetoNextStep_sensa() { return $('//form[@class="cmp-register__stepOne-form"]//span[@class="cmp-button__text"]'); }


    get lblaccountSetup_sensa() { return $('(//*[@class="cmp-register__stepHeader-title"])[2]'); }
    get txtpassword_sensa() { return $('*[name="regPassword"]'); }
    get txtconfirmPassword_sensa() { return $('*[name="regConfirmPassword"]'); }
    get txtsecurityQuestion_sensa() { return $('*[id="regSecurityQuestion"]'); }
    get txtsecuritynswer_sensa() { return $('*[name="regSecurityAnswer"]'); }
    get txtcontinuetonextStep_sensa() { return $('//form[@class="cmp-register__stepTwo-form"]//span[@class="cmp-button__text"][normalize-space()="Continue to Next Step"]'); }
    get lblallfields_sensa() { return $('(//*[contains(text(),"All fields ")])[2]'); }
    get lblusername_sensa() { return $('//*[@class="cmp-register__usernameRegister"]//h4[@class="cmp-register__sectionHeading"]'); }
    get lblyourusername_sensa() { return $('//*[@class="cmp-register__usernameRegister"]//p'); }
    get txtusername_sensa() { return $('//*[@class="cmp-register__usernameRegister"]//input'); }
    get hdrPassword_sensa() { return $('//*[@class="cmp-register__passwordRegister"]//*[@class="cmp-register__sectionHeading"]'); }
    get lblpleasechoose_sensa() { return $('//*[@class="cmp-register__passwordRegister"]//p'); }
    get lblpasswordCondition1_sensa() { return $('//*[@class="cmp-register__passwordRegister"]//ul//li[1]'); }
    get lblpasswordCondition2_sensa() { return $('//*[@class="cmp-register__passwordRegister"]//ul//li[2]'); }
    get lblpasswordCondition3_sensa() { return $('//*[@class="cmp-register__passwordRegister"]//ul//li[3]'); }
    get lblpasswordCondition4_sensa() { return $('//*[@class="cmp-register__passwordRegister"]//ul//li[4]'); }
    get lblpasswordlabel_sensa() { return $('//label[@class="blurred"][text()="Password"]'); }
    get lblcpasswordlabel_sensa() { return $('//label[text()="Confirm Password"]'); }
    get hdrSecurity_sensa() { return $('//*[@class="cmp-register__accountSecurity"]//*[@class="cmp-register__sectionHeading"]'); }
    get lblsecurityanswercase_sensa() { return $('//*[@class="cmp-register__accountSecurity"]//p'); }
    get lblanswerlabel_sensa() { return $('//*[@class="cmp-register__accountSecurity"]//label'); }
    get lblconfirmpasswordError_sensa() { return $('//*[@class="notMatching active"]'); }
    get lblpasswordError_sensa() { return $('//div[@class="validation wrongCheck"]'); }
    get lblsecurityQuestionError_sensa() { return $('//*[@class="validation wrongSelect"]'); }
    get lblsecurityAnswerError_sensa() { return $('//*[@class="validation wrongCheck"][contains(text(),"field")]'); }

    get txtphone_sensa_iOS() { return $('//XCUIElementTypeTextField'); }
    get txtphone_sensa() { return $('//android.widget.EditText'); }
    get btnsendmeCode_sensa() { return $('//android.widget.Button[@text="Send Me a Code"]'); }
    get btnSendMeCode_sensa_iOS() { return $('//XCUIElementTypeButton[@name="Send Me a Code"]'); }

    get btnverifyMe_sensa_iOS() { return $('//XCUIElementTypeButton[@name="Verify Me"]'); }
    get btnverifyMe_sensa() { return $('//android.widget.Button[@text="Verify Me"]'); }


    get lbloopstry() { return $('//XCUIElementTypeStaticText[@name="Oops! Let\'s try another way"]'); }
    get lblssn_sensa() { return $('//XCUIElementTypeStaticText[@name="Last 4 SSN"]'); }
    get txtssn_sensa() { return $('//android.widget.EditText[@inputType="number"]'); }
    get lblinvalidSSNError_sensa() { return $('//XCUIElementTypeStaticText[contains(@name,"We were unable to verify you. Please make sure you")]'); }

    get lblrequiredMoreDetails_sensa() { return $('//XCUIElementTypeStaticText[@name="More Details Required"]'); }
    get btnlicense_sensa() { return $('//XCUIElementTypeButton[@name="Scan my License or ID"]'); }
    get btnpassport_sensa() { return $('//XCUIElementTypeButton[@name="Scan my Passport"]'); }
    get lblscanfront_ios_sensa() { return $('//XCUIElementTypeButton[@name="Upload an Image of the Front"]'); }
    get lblscanfront_sensa() { return $('//*[@id="front"]'); }
    get lblscanback_sensa() { return $('//*[@id="back"]'); }
    get lblscanback_ios_sensa() { return $('//XCUIElementTypeStaticText[@name="Upload an Image of the Back"]'); }
    get lblverify_sensa() { return $('//XCUIElementTypeButton[@name="Verify"]'); }

    get lblsignup_sensa() { return $('//*[@class="cmp-register__smsOptIn active"] //h3'); }
    get lbljoinsensa() { return $('//*[@class="cmp-register__smsOptInDescription cmp-register__stepMessage"] '); }
    get hdrmobilenumber_sensa() { return $('//*[@class="cmp-register__mobileProfile"]//h4'); }
    get txtmobilenumber_sensa() { return $('*[name="regSmsOptInMobilePhone"]'); }
    get lnknoThanks_sensa() { return $('(//*[contains(text(), "No thanks")])'); }
    get lblicertify_sensa() { return $('//*[@aria-label="Subscription agreement"]//p'); }
    get lblmobilelabel_sensa() { return $('//*[@id="labelMobileNumberSmsOptin"]'); }
    get btnsubscribe_sensa() { return $('//div[@class="cmp-register__smsOptIn-content-fields"]//button[@type="submit"]'); }

    get lblcongratulations_sensa() { return $('//*[text()="Congratulations!"]'); }
    get lnktakemetosite_sensa() { return $('(//*[contains(text(), "Take me to the")])[1]'); }
    get btnsave_sensa() { return $('//form[@class="cmp-register__congratulations-form"]//button[@type="submit"]'); }

    get lbllogo_sensa() { return $('//*[@title="Sensa"]'); }
    get lnklogout_sensa() { return $('(//span[contains(text(),"Log Out")]//parent::*)[3]'); }



    get lblsmsMobile_sensa() { return $('//*[contains(text(), "Mobile Number ")]'); }
    get lblsmscertify_sensa() { return $('//*[@class = "cmp-register__agreement-checkbox cmp-form-options--checkbox cmp-form-options--fancy"]'); }
    get lblsmssubscribe_sensa() { return $('//span[text()="Subscribe"]/parent::*'); }

    get lblcongratsSave_sensa() { return $('//span[text()="Save"]/parent::*'); }
    get lblcongratsdescription_sensa() { return $('//*[@class="cmp-register__stepMessage cmp-register__stepMessage---no-sms-optin active"]'); }

    get lblletsverifyidentity_sensa() { return $('//*[contains(text(), "Let\'s verify your identity")]'); }
    get lblregistrationError_sensa() { return $('//*[@class="alreadyRegisteredOtherBrand active"]'); }
    get lblregistrationsameBrand_sensa() { return $('//*[@class="alreadyRegistered active"]'); }
    get lbltellusabout_sensa() { return $('//*[@class="cmp-register__stepOne active"]//*[@class="cmp-register__stepHeader-title"]'); }
    get hdremail_sensa() { return $('//div[@class="cmp-register__emailRegister"]//h4[@class="cmp-register__sectionHeading"]'); }
    get lblyouemail_sensa() { return $('//div[@class="cmp-register__emailRegister"]//p[@class="cmp-register__sectionDescription"]'); }
    get lblemail_sensa() { return $('//*[@id="labelEmail"]'); }


    get txtssn_camel() { return $('(//XCUIElementTypeOther[@name="veratad"])[2]/XCUIElementTypeTextField[4]'); }
    get btnverifyMe_camel_iOS() { return $('//XCUIElementTypeButton[@name="VERIFY ME"]'); }
    get btnSendMeCode_camel_iOS() { return $('//XCUIElementTypeButton[@name="SEND ME A CODE"]'); }


}
export default new RegistrationPageObject();