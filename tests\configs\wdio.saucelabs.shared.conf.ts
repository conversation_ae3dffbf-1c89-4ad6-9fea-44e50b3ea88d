import type { Options } from '@wdio/types';
import { config as sharedConfig } from './wdio.shared.conf.ts';
import { getSauceCredentials } from '../support/helpers/index.ts';
import { ElementHighlighter } from '../support/helpers/ElementHighlighter.ts';
import path from 'path';
//
// Get the Sauce Labs credentials
const { sauceUsername, sauceAccessKey } = await getSauceCredentials();
// Define the highlight configuration
const highlightConfig = {
  enabled: process.env.HIGHLIGHT_ELEMENTS === 'true' || false,
  highlightDuration: 500,
  borderColor: 'red',
  borderWidth: '3px',
  backgroundColor: 'rgba(255, 0, 0, 0.2)',
};
// Create and export the highlighter instance
export const highlighter = new ElementHighlighter(highlightConfig);

// Create a global namespace for our custom Element Highlighter
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace WebdriverIO {
    interface Browser {
      highlightElements: boolean;
    }
  }
}
export const config: WebdriverIO.Config & Options.Testrunner = {
  ...sharedConfig,
  //
  // =================
  // Service Providers
  // =================
  user: sauceUsername,
  key: sauceAccessKey,
  region: (process.env.SAUCE_REGION || 'us-west-1') as Options.SauceRegions,

  // Sauce Labs specific configuration
  hostname: process.env.SAUCE_REGION === 'us-west-1'
    ? 'ondemand.eu-central-1.saucelabs.com'
    : 'ondemand.us-west-1.saucelabs.com',
  protocol: 'https',
  path: '/wd/hub',
  port: 443,
  //
  // ============
  // Capabilities
  // ============
  // Are not configured here, they can be found in:
  // - wdio.saucelabs.desktop.conf.ts
  // - wdio.saucelabs.mobile.conf.ts
  //
  // ========
  // Services
  // ========
  // Reporting Configuration
  reporters: [
    ['spec', {
      addConsoleLogs: true,
      printBasicLog: true,
      showPreface: true,
      realtimeReporting: true,
    }],
    ['allure', {
      outputDir: './reports/allure-results',
      disableWebdriverStepsReporting: true,  // Critical: prevents auto-step generation
      useCucumberStepReporter: false,        // Key: keeps false to avoid errors
      disableWebdriverScreenshotsReporting: false,
      disableMochaHooks: true,              // Prevents blank steps
      addConsoleLogs: false,
    }],
  ],
  services: [
    //
    // This service is needed for WDIO to make sure it can connect to Sauce Labs to:
    // - automatically update the status (passed/failed)
    // - automatically send the stacktrace in case of a failure
    // Note: We handle test naming manually in beforeScenario hook
    // The automatic status setting will be overridden in our hooks
    //
    'sauce',
    'appium',
    ['image-comparison', {
      baselineFolder: './tests/baselineImages',
      formatImageName: '{tag}-{logName}-{width}x{height}',
      screenshotPath: './reports/screenshots',
      savePerInstance: true,
      autoSaveBaseline: true,
      blockOutStatusBar: true,
      blockOutToolBar: true,
    }],
    ['gmail', {
      credentials: path.join(process.cwd(), 'tests/resources/google-key/gmailCredentials.json'),
      token: path.join(process.cwd(), 'tests/resources/google-key/token.json'),
      intervalSec: 10,
      timeoutSec: 60,
    }],
    //
    // This service is needed for the Sauce Visual service to work
    //
    [
      '@saucelabs/wdio-sauce-visual-service',
      // The options for the Sauce Visual service
      {
        buildName: process.env.BUILD_NAME || `Build-${new Date().toISOString()}`,
        branch: process.env.SAUCE_BRANCH || 'main',
        project: process.env.SAUCE_PROJECT || 'WDIO Cucumber Demo Project for' + process.env.SAUCE_USERNAME,
        captureDom: true,
        updateBaseline: false,
      },
    ],
  ],
  // Hook to set up the highlighter and add the custom command
  before: function (_capabilities, _specs) {
    browser.highlightElements = highlightConfig.enabled;
    // Attach the highlighter to the browser object for easy access
    browser.addCommand('highlight', async function (this: WebdriverIO.Element) {
      return highlighter.highlight(this);
    }, true); // 'true' makes it an element command
  },

};
