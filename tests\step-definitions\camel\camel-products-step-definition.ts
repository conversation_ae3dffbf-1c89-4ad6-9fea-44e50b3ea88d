import { Then, When } from '@wdio/cucumber-framework';
import logger from '../../support/utils/logger.util.ts';
import camelProductsPage from '../../pages/camel/camel-products.page.ts';
import camelProductsPageObject from '../../page-object/camel/camel-products.pageObject.ts';


Then(/^The user validates Products Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
     await camelProductsPage.productsPageValidation(filepath, sheetname, scenarioname);
     await expect(camelProductsPageObject.hdrcrush_camel).toBeDisplayed();
     logger.info('Validated Products Page Successfully');

});

Then(/^The user validates Crush Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
     await camelProductsPage.crushProductsPageValidation(filepath, sheetname, scenarioname);
     await expect(camelProductsPageObject.hdrcrush_camel).toBeDisplayed();
     logger.info('Validated Crush Page Successfully');

});

When(/^The user clicks on Crush link from products page$/, async function () {
     await camelProductsPage.navigatetocrushpage();
     await expect(camelProductsPageObject.hdrcrush_camel).toBeDisplayed();
     logger.info('Navigate Crush Page Successfully');
});

Then(/^The user validates Classic Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
     await camelProductsPage.classicProductsPageValidation(filepath, sheetname, scenarioname);
     await expect(camelProductsPageObject.imgclassicgoldproduct).toBeDisplayed();
     logger.info('Validated classic Page Successfully');

});

When(/^The user clicks on Classic link from products page$/, async function () {
     await camelProductsPage.navigatetoclassicpage();
     await expect(camelProductsPageObject.imgclassicgoldproduct).toBeDisplayed();
     logger.info('Navigate classic Page Successfully');

});


Then(/^The user validates Turkish Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
     await camelProductsPage.turkishProductsPageValidation(filepath, sheetname, scenarioname);
     await expect(camelProductsPageObject.imgturkishloyalproduct).toBeDisplayed();
     logger.info('Validated Turkish Page Successfully');

});

When(/^The user clicks on Turkish link from products page$/, async function () {
     await camelProductsPage.navigatetoturkishpage();
     await expect(camelProductsPageObject.imgturkishloyalproduct).toBeDisplayed();
     logger.info('Navigate Turkish Page Successfully');

});

Then(/^The user validates No9 Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
     await camelProductsPage.no9ProductsPageValidation(filepath, sheetname, scenarioname);
     await expect(camelProductsPageObject.imgno9product).toBeDisplayed();
     logger.info('Validated No9 Page Successfully');

});

When(/^The user clicks on No9 link from products page$/, async function () {
     await camelProductsPage.navigatetono9page();
     await expect(camelProductsPageObject.imgno9product).toBeDisplayed();
     logger.info('Navigate No9 Page Successfully');

});


Then(/^The user validates Red Kamel Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
     await camelProductsPage.redkamelProductsPageValidation(filepath, sheetname, scenarioname);
     await expect(camelProductsPageObject.imgredkamelproduct).toBeDisplayed();
     logger.info('Validated Red Kamel Page Successfully');

});

When(/^The user clicks on Red Kamel link from products page$/, async function () {
     await camelProductsPage.navigatetoredkamelpage();
     await expect(camelProductsPageObject.imgredkamelproduct).toBeDisplayed();
     logger.info('Navigate Red Kamel Page Successfully');

});