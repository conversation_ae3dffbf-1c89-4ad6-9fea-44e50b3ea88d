import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import AccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';
import NasProductPageObject from '../../page-object/americanspirit/americanspirit-product.pageObject.ts';
import sensaAccountPage from '../commonteps/account.page.ts';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import sensaFooterlinkPage from '../../pages/commonteps/footerlink.page.ts';
import path from 'path';

class NasProductPage {

    async clickonOurproduct() {
        try {
            await elementActions.waitForDisplayed(AccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.clickusingJavascript(AccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.click(NasProductPageObject.Nas_ourProductsTitle);
            await elementActions.assertion(NasProductPageObject.Nas_productHeroImage);
            await sensaFooterlinkPage.allowpopup();
            console.log('Navigated to Ourproduct Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Ourproduct Page', { error });
            throw error;
        }
    }
    async OurProductPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const lblfindpack = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfindpack');
            const lblstyle = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstyle');
            const lblorganic = testData.getCellValue(SHEET_NAME, scenarioname, 'lblorganic');
            const lblmenthol = testData.getCellValue(SHEET_NAME, scenarioname, 'lblmenthol');
            const lblusgrown = testData.getCellValue(SHEET_NAME, scenarioname, 'lblusgrown');
            const lblnonfiltered = testData.getCellValue(SHEET_NAME, scenarioname, 'lblnonfiltered');
            const lblrollyourown = testData.getCellValue(SHEET_NAME, scenarioname, 'lblrollyourown');
            const lblsmoothmellow = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsmoothmellow');
            const lblbalanced = testData.getCellValue(SHEET_NAME, scenarioname, 'lblbalanced');
            const lblfullbodied = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfullbodied');
            const lblrichrobust = testData.getCellValue(SHEET_NAME, scenarioname, 'lblrichrobust');
            const lbltaste = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltaste');
            const lbl19product = testData.getCellValue(SHEET_NAME, scenarioname, 'lbl19product');
            const lbl19productdetails = testData.getCellValue(SHEET_NAME, scenarioname, 'lbl19productdetails');

            const productname1 = testData.getCellValue(SHEET_NAME, scenarioname, 'productname1');
            const productfloatinglabel1 = testData.getCellValue(SHEET_NAME, scenarioname, 'productfloatinglabel1');
            const productTasteDescription1 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTasteDescription1');
            const productTitle2 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTitle2');
            const productDescription1 = testData.getCellValue(SHEET_NAME, scenarioname, 'productDescription1');
            const productratingname1 = testData.getCellValue(SHEET_NAME, scenarioname, 'productratingname1');
            const productdetailscontainer1 = testData.getCellValue(SHEET_NAME, scenarioname, 'productdetailscontainer1');

            const productname2 = testData.getCellValue(SHEET_NAME, scenarioname, 'productname2');
            const productfloatinglabel2 = testData.getCellValue(SHEET_NAME, scenarioname, 'productfloatinglabel2');
            const productTasteDescription2 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTasteDescription2');
            const productTitle3 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTitle3');
            const productDescription2 = testData.getCellValue(SHEET_NAME, scenarioname, 'productDescription2');
            const productratingname2 = testData.getCellValue(SHEET_NAME, scenarioname, 'productratingname2');
            const productdetailscontainer2 = testData.getCellValue(SHEET_NAME, scenarioname, 'productdetailscontainer2');

            const productname3 = testData.getCellValue(SHEET_NAME, scenarioname, 'productname3');
            const productfloatinglabel3 = testData.getCellValue(SHEET_NAME, scenarioname, 'productfloatinglabel3');
            const productTasteDescription3 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTasteDescription3');
            const productTitle4 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTitle4');
            const productDescription3 = testData.getCellValue(SHEET_NAME, scenarioname, 'productDescription3');
            const productratingname3 = testData.getCellValue(SHEET_NAME, scenarioname, 'productratingname3');
            const productdetailscontainer3 = testData.getCellValue(SHEET_NAME, scenarioname, 'productdetailscontainer3');

            const productname4 = testData.getCellValue(SHEET_NAME, scenarioname, 'productname4');
            const productfloatinglabel4 = testData.getCellValue(SHEET_NAME, scenarioname, 'productfloatinglabel4');
            const productTasteDescription4 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTasteDescription4');
            const productTitle5 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTitle5');
            const productDescription4 = testData.getCellValue(SHEET_NAME, scenarioname, 'productDescription4');
            const productratingname4 = testData.getCellValue(SHEET_NAME, scenarioname, 'productratingname4');
            const productdetailscontainer4 = testData.getCellValue(SHEET_NAME, scenarioname, 'productdetailscontainer4');

            const productname5 = testData.getCellValue(SHEET_NAME, scenarioname, 'productname5');
            const productfloatinglabel5 = testData.getCellValue(SHEET_NAME, scenarioname, 'productfloatinglabel5');
            const productTasteDescription5 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTasteDescription5');
            const productTitle6 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTitle6');
            const productDescription5 = testData.getCellValue(SHEET_NAME, scenarioname, 'productDescription5');
            const productratingname5 = testData.getCellValue(SHEET_NAME, scenarioname, 'productratingname5');
            const productdetailscontainer5 = testData.getCellValue(SHEET_NAME, scenarioname, 'productdetailscontainer5');

            const productname6 = testData.getCellValue(SHEET_NAME, scenarioname, 'productname6');
            const productfloatinglabel6 = testData.getCellValue(SHEET_NAME, scenarioname, 'productfloatinglabel6');
            const productTasteDescription6 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTasteDescription6');
            const productTitle7 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTitle7');
            const productDescription6 = testData.getCellValue(SHEET_NAME, scenarioname, 'productDescription6');
            const productratingname6 = testData.getCellValue(SHEET_NAME, scenarioname, 'productratingname6');
            const productdetailscontainer6 = testData.getCellValue(SHEET_NAME, scenarioname, 'productdetailscontainer6');


            const url = browser.getUrl();
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_filtersHeading, lblfindpack);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_mystyle, lblstyle);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_organic, lblorganic);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_menthol, lblmenthol);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_usGrown, lblusgrown);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_nonFiltered, lblnonfiltered);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_rollYourOwn, lblrollyourown);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_myTaste, lbltaste);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_smoothMellow, lblsmoothmellow);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_balanced, lblbalanced);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_fullBodied, lblfullbodied);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_richRobust, lblrichrobust);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle1, lbl19product);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nasproductssubtitle, lbl19productdetails);

            await elementActions.click(NasProductPageObject.Nas_Yellowsignaturestyle);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productname1, productname1);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productfloatinglabel1, productfloatinglabel1);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTasteDescription1, productTasteDescription1);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle2, productTitle2);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productDescription1, productDescription1);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productratingname1, productratingname1);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productdetailscontainer1, productdetailscontainer1);

            await elementActions.click(NasProductPageObject.Nas_Bluesignaturestyle);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productname2, productname2);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productfloatinglabel2, productfloatinglabel2);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTasteDescription2, productTasteDescription2);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle3, productTitle3);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productDescription2, productDescription2);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productratingname2, productratingname2);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productdetailscontainer2, productdetailscontainer2);

            await elementActions.click(NasProductPageObject.Nas_Turquoiseorganicstyle);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productname3, productname3);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productfloatinglabel3, productfloatinglabel3);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTasteDescription3, productTasteDescription3);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle4, productTitle4);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productDescription3, productDescription3);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productratingname3, productratingname3);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productdetailscontainer3, productdetailscontainer3);
            await elementActions.assertion(NasProductPageObject.equityProductsDescription1);
            await elementActions.assertion(NasProductPageObject.exploreOrganicButton);

            await elementActions.click(NasProductPageObject.Nas_Goldorganicstyle);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productname4, productname4);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productfloatinglabel4, productfloatinglabel4);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTasteDescription4, productTasteDescription4);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle5, productTitle5);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productDescription4, productDescription4);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productratingname4, productratingname4);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productdetailscontainer4, productdetailscontainer4);

            await elementActions.click(NasProductPageObject.Nas_Greenorganicstyle);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productname5, productname5);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productfloatinglabel5, productfloatinglabel5);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTasteDescription5, productTasteDescription5);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle6, productTitle6);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productDescription5, productDescription5);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productratingname5, productratingname5);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productdetailscontainer5, productdetailscontainer5);

            await elementActions.click(NasProductPageObject.Nas_Darkgreenorganicstyle);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productname6, productname6);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productfloatinglabel6, productfloatinglabel6);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTasteDescription6, productTasteDescription6);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle7, productTitle7);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productDescription6, productDescription6);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productratingname6, productratingname6);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productdetailscontainer6, productdetailscontainer6);
            await elementActions.assertion(NasProductPageObject.equityProductsDescription2);
            await elementActions.assertion(NasProductPageObject.getStartedButton);

            // Test Data Declarations (7 to 12)
            const productname7 = testData.getCellValue(SHEET_NAME, scenarioname, 'productname7');
            const productfloatinglabel7 = testData.getCellValue(SHEET_NAME, scenarioname, 'productfloatinglabel7');
            const productTasteDescription7 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTasteDescription7');
            const productTitle8 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTitle8');
            const productDescription7 = testData.getCellValue(SHEET_NAME, scenarioname, 'productDescription7');
            const productratingname7 = testData.getCellValue(SHEET_NAME, scenarioname, 'productratingname7');
            const productdetailscontainer7 = testData.getCellValue(SHEET_NAME, scenarioname, 'productdetailscontainer7');

            const productname8 = testData.getCellValue(SHEET_NAME, scenarioname, 'productname8');
            const productfloatinglabel8 = testData.getCellValue(SHEET_NAME, scenarioname, 'productfloatinglabel8');
            const productTasteDescription8 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTasteDescription8');
            const productTitle9 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTitle9');
            const productDescription8 = testData.getCellValue(SHEET_NAME, scenarioname, 'productDescription8');
            const productratingname8 = testData.getCellValue(SHEET_NAME, scenarioname, 'productratingname8');
            const productdetailscontainer8 = testData.getCellValue(SHEET_NAME, scenarioname, 'productdetailscontainer8');

            const productname9 = testData.getCellValue(SHEET_NAME, scenarioname, 'productname9');
            const productfloatinglabel9 = testData.getCellValue(SHEET_NAME, scenarioname, 'productfloatinglabel9');
            const productTasteDescription9 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTasteDescription9');
            const productTitle10 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTitle10');
            const productDescription9 = testData.getCellValue(SHEET_NAME, scenarioname, 'productDescription9');
            const productratingname9 = testData.getCellValue(SHEET_NAME, scenarioname, 'productratingname9');
            const productdetailscontainer9 = testData.getCellValue(SHEET_NAME, scenarioname, 'productdetailscontainer9');


            const productname10 = testData.getCellValue(SHEET_NAME, scenarioname, 'productname10');
            const productfloatinglabel10 = testData.getCellValue(SHEET_NAME, scenarioname, 'productfloatinglabel10');
            const productTasteDescription10 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTasteDescription10');
            const productTitle11 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTitle11');
            const productDescription10 = testData.getCellValue(SHEET_NAME, scenarioname, 'productDescription10');
            const productratingname10 = testData.getCellValue(SHEET_NAME, scenarioname, 'productratingname10');
            const productdetailscontainer10 = testData.getCellValue(SHEET_NAME, scenarioname, 'productdetailscontainer10');

            const productname11 = testData.getCellValue(SHEET_NAME, scenarioname, 'productname11');
            const productfloatinglabel11 = testData.getCellValue(SHEET_NAME, scenarioname, 'productfloatinglabel11');
            const productTasteDescription11 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTasteDescription11');
            const productTitle12 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTitle12');
            const productDescription11 = testData.getCellValue(SHEET_NAME, scenarioname, 'productDescription11');
            const productratingname11 = testData.getCellValue(SHEET_NAME, scenarioname, 'productratingname11');
            const productdetailscontainer11 = testData.getCellValue(SHEET_NAME, scenarioname, 'productdetailscontainer11');

            const productname12 = testData.getCellValue(SHEET_NAME, scenarioname, 'productname12');
            const productfloatinglabel12 = testData.getCellValue(SHEET_NAME, scenarioname, 'productfloatinglabel12');
            const productTasteDescription12 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTasteDescription12');
            const productTitle13 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTitle13');
            const productDescription12 = testData.getCellValue(SHEET_NAME, scenarioname, 'productDescription12');
            const productratingname12 = testData.getCellValue(SHEET_NAME, scenarioname, 'productratingname12');
            const productdetailscontainer12 = testData.getCellValue(SHEET_NAME, scenarioname, 'productdetailscontainer12');


            // Validation calls (7 to 12)
            await elementActions.click(NasProductPageObject.Nas_Skyorganicstyle);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productname7, productname7);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productfloatinglabel7, productfloatinglabel7);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTasteDescription7, productTasteDescription7);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle8, productTitle8);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productDescription7, productDescription7);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productratingname7, productratingname7);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productdetailscontainer7, productdetailscontainer7);

            await elementActions.click(NasProductPageObject.Nas_Orangesignaturestyle);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productname8, productname8);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productfloatinglabel8, productfloatinglabel8);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTasteDescription8, productTasteDescription8);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle9, productTitle9);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productDescription8, productDescription8);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productratingname8, productratingname8);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productdetailscontainer8, productdetailscontainer8);

            await elementActions.click(NasProductPageObject.Nas_Blackperiquestyle);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productname9, productname9);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productfloatinglabel9, productfloatinglabel9);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTasteDescription9, productTasteDescription9);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle10, productTitle10);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productDescription9, productDescription9);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productratingname9, productratingname9);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productdetailscontainer9, productdetailscontainer9);
            await elementActions.assertion(NasProductPageObject.equityProductsDescription3);
            await elementActions.assertion(NasProductPageObject.exploreOurTobaccoButton);

            await elementActions.click(NasProductPageObject.Nas_Greyperiquestyle);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productname10, productname10);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productfloatinglabel10, productfloatinglabel10);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTasteDescription10, productTasteDescription10);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle11, productTitle11);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productDescription10, productDescription10);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productratingname10, productratingname10);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productdetailscontainer10, productdetailscontainer10);

            await elementActions.click(NasProductPageObject.Nas_Darkbluesignaturestyle);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productname11, productname11);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productfloatinglabel11, productfloatinglabel11);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTasteDescription11, productTasteDescription11);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle12, productTitle12);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productDescription11, productDescription11);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productratingname11, productratingname11);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productdetailscontainer11, productdetailscontainer11);

            await elementActions.click(NasProductPageObject.Nas_Celadonsignaturestyle);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productname12, productname12);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productfloatinglabel12, productfloatinglabel12);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTasteDescription12, productTasteDescription12);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle13, productTitle13);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productDescription12, productDescription12);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productratingname12, productratingname12);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productdetailscontainer12, productdetailscontainer12);
            await elementActions.assertion(NasProductPageObject.equityProductsDescription4);
            await elementActions.assertion(NasProductPageObject.meetOurPartnersButton);

            // Test Data Declarations (13 to 18)
            const productname13 = testData.getCellValue(SHEET_NAME, scenarioname, 'productname13');
            const productfloatinglabel13 = testData.getCellValue(SHEET_NAME, scenarioname, 'productfloatinglabel13');
            const productTasteDescription13 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTasteDescription13');
            const productTitle14 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTitle14');
            const productDescription13 = testData.getCellValue(SHEET_NAME, scenarioname, 'productDescription13');
            const productratingname13 = testData.getCellValue(SHEET_NAME, scenarioname, 'productratingname13');
            const productdetailscontainer13 = testData.getCellValue(SHEET_NAME, scenarioname, 'productdetailscontainer13');

            const productname14 = testData.getCellValue(SHEET_NAME, scenarioname, 'productname14');
            const productfloatinglabel14 = testData.getCellValue(SHEET_NAME, scenarioname, 'productfloatinglabel14');
            const productTasteDescription14 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTasteDescription14');
            const productTitle15 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTitle15');
            const productDescription14 = testData.getCellValue(SHEET_NAME, scenarioname, 'productDescription14');
            const productratingname14 = testData.getCellValue(SHEET_NAME, scenarioname, 'productratingname14');
            const productdetailscontainer14 = testData.getCellValue(SHEET_NAME, scenarioname, 'productdetailscontainer14');

            const productname15 = testData.getCellValue(SHEET_NAME, scenarioname, 'productname15');
            const productfloatinglabel15 = testData.getCellValue(SHEET_NAME, scenarioname, 'productfloatinglabel15');
            const productTasteDescription15 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTasteDescription15');
            const productTitle16 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTitle16');
            const productDescription15 = testData.getCellValue(SHEET_NAME, scenarioname, 'productDescription15');
            const productratingname15 = testData.getCellValue(SHEET_NAME, scenarioname, 'productratingname15');
            const productdetailscontainer15 = testData.getCellValue(SHEET_NAME, scenarioname, 'productdetailscontainer15');


            const productname16 = testData.getCellValue(SHEET_NAME, scenarioname, 'productname16');
            const productfloatinglabel16 = testData.getCellValue(SHEET_NAME, scenarioname, 'productfloatinglabel16');
            const productTasteDescription16 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTasteDescription16');
            const productTitle17 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTitle17');
            const productDescription16 = testData.getCellValue(SHEET_NAME, scenarioname, 'productDescription16');
            const productratingname16 = testData.getCellValue(SHEET_NAME, scenarioname, 'productratingname16');
            const productdetailscontainer16 = testData.getCellValue(SHEET_NAME, scenarioname, 'productdetailscontainer16');

            const productname17 = testData.getCellValue(SHEET_NAME, scenarioname, 'productname17');
            const productfloatinglabel17 = testData.getCellValue(SHEET_NAME, scenarioname, 'productfloatinglabel17');
            const productTasteDescription17 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTasteDescription17');
            const productTitle18 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTitle18');
            const productDescription17 = testData.getCellValue(SHEET_NAME, scenarioname, 'productDescription17');
            const productratingname17 = testData.getCellValue(SHEET_NAME, scenarioname, 'productratingname17');
            const productdetailscontainer17 = testData.getCellValue(SHEET_NAME, scenarioname, 'productdetailscontainer17');

            const productname18 = testData.getCellValue(SHEET_NAME, scenarioname, 'productname18');
            const productfloatinglabel18 = testData.getCellValue(SHEET_NAME, scenarioname, 'productfloatinglabel18');
            const productTasteDescription18 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTasteDescription18');
            const productTitle19 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTitle19');
            const productDescription18 = testData.getCellValue(SHEET_NAME, scenarioname, 'productDescription18');
            const productratingname18 = testData.getCellValue(SHEET_NAME, scenarioname, 'productratingname18');
            const productdetailscontainer18 = testData.getCellValue(SHEET_NAME, scenarioname, 'productdetailscontainer18');


            // Validation calls (13 to 18)
            await elementActions.click(NasProductPageObject.Nas_Huntersignaturestyle);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productname13, productname13);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productfloatinglabel13, productfloatinglabel13);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTasteDescription13, productTasteDescription13);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle14, productTitle14);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productDescription13, productDescription13);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productratingname13, productratingname13);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productdetailscontainer13, productdetailscontainer13);

            await elementActions.click(NasProductPageObject.Nas_Brownsignaturestyle);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productname14, productname14);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productfloatinglabel14, productfloatinglabel14);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTasteDescription14, productTasteDescription14);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle15, productTitle15);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productDescription14, productDescription14);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productratingname14, productratingname14);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productdetailscontainer14, productdetailscontainer14);

            await elementActions.click(NasProductPageObject.Nas_Tansignaturestyle);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productname15, productname15);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productfloatinglabel15, productfloatinglabel15);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTasteDescription15, productTasteDescription15);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle16, productTitle16);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productDescription15, productDescription15);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productratingname15, productratingname15);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productdetailscontainer15, productdetailscontainer15);
            await elementActions.assertion(NasProductPageObject.equityProductsDescription5);
            await elementActions.assertion(NasProductPageObject.exploreNowButton);

            await elementActions.click(NasProductPageObject.Nas_Bluerollyourown);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productname16, productname16);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productfloatinglabel16, productfloatinglabel16);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTasteDescription16, productTasteDescription16);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle17, productTitle17);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productDescription16, productDescription16);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productratingname16, productratingname16);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productdetailscontainer16, productdetailscontainer16);

            await elementActions.click(NasProductPageObject.Nas_Turquoiserollyourown);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productname17, productname17);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productfloatinglabel17, productfloatinglabel17);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTasteDescription17, productTasteDescription17);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle18, productTitle18);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productDescription17, productDescription17);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productratingname17, productratingname17);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productdetailscontainer17, productdetailscontainer17);

            await elementActions.click(NasProductPageObject.Nas_Darkblueollyourown);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productname18, productname18);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productfloatinglabel18, productfloatinglabel18);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTasteDescription18, productTasteDescription18);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle19, productTitle19);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productDescription18, productDescription18);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productratingname18, productratingname18);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productdetailscontainer18, productdetailscontainer18);
            await elementActions.assertion(NasProductPageObject.equityProductsDescription6);
            await elementActions.assertion(NasProductPageObject.checkForOffersButton);
            // Test Data Declarations (19)
            const productname19 = testData.getCellValue(SHEET_NAME, scenarioname, 'productname19');
            const productfloatinglabel19 = testData.getCellValue(SHEET_NAME, scenarioname, 'productfloatinglabel19');
            const productTasteDescription19 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTasteDescription19');
            const productTitle20 = testData.getCellValue(SHEET_NAME, scenarioname, 'productTitle20');
            const productDescription19 = testData.getCellValue(SHEET_NAME, scenarioname, 'productDescription19');
            const productratingname19 = testData.getCellValue(SHEET_NAME, scenarioname, 'productratingname19');
            const productdetailscontainer19 = testData.getCellValue(SHEET_NAME, scenarioname, 'productdetailscontainer19');

            await elementActions.click(NasProductPageObject.Nas_Blackrollyourown);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productname19, productname19);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productfloatinglabel19, productfloatinglabel19);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTasteDescription19, productTasteDescription19);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productTitle20, productTitle20);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productDescription19, productDescription19);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productratingname19, productratingname19);
            await sensaAccountPage.mssgcomparision(NasProductPageObject.Nas_productdetailscontainer19, productdetailscontainer19);
            await elementActions.assertion(NasProductPageObject.Nas_zeroWasteHeadline);

            await elementActions.assertion(NasProductPageObject.dontKnowWhereToStartButton);
            await elementActions.assertion(NasProductPageObject.tobaccoPreferenceParagraph2);
            await elementActions.assertion(NasProductPageObject.getStartedButton2);
            await elementActions.assertion(NasProductPageObject.redeemMobileOffersText1);
            await elementActions.assertion(NasProductPageObject.beTheFirstToKnowText1);
            await elementActions.assertion(NasProductPageObject.seeOffersButton1);
            await elementActions.assertion(NasProductPageObject.findYourSpiritText);
            await elementActions.assertion(NasProductPageObject.productTasteParagraph);
            await elementActions.assertion(NasProductPageObject.findAStoreButton);
            await elementActions.assertion(NasProductPageObject.styleAvailabilityNote);











            console.log('Validated the Content in OurProduct page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in OurProduct page', { error });
            throw error;
        }
    }

}
export default new NasProductPage();