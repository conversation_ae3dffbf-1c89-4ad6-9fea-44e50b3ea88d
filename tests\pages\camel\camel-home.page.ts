import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import path from 'path';
import camelLoginPageObject from '../../page-object/camel/camel-login.pageObject.ts';
import sensaAccountPage from '../commonteps/account.page.ts';
import camelCouponsPageObject from '../../page-object/camel/camel-coupons.pageObject.ts';
import sensaAccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';
import camelHomepagePageObject from '../../page-object/camel/camel-homepage.pageObject.ts';
import camelHumppagePageObject from '../../page-object/camel/camel-humppage.pageObject.ts';
import camelPromotionsPageObject from '../../page-object/camel/camel-promotions.pageObject.ts';
import sensaHomepagePage from '../sensa/sensa-homepage.page.ts';
import sensaHomepagePageObject from '../../page-object/sensa/sensa-homepage.pageObject.ts';
import camelFooterlinkPageObject from '../../page-object/camel/camel-footerlink.pageObject.ts';
import sensaFooterlinkPage from '../commonteps/footerlink.page.ts';


class CameHomePage {

    async homePageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {
            await elementActions.waitForDisplayed(sensaAccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.clickusingJavascript(sensaAccountPageObject.btnhamburgerMenu_sensa);
            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const lblPromotions = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPromotions');
            const lblProducts = testData.getCellValue(SHEET_NAME, scenarioname, 'lblProducts');
            const lblHump = testData.getCellValue(SHEET_NAME, scenarioname, 'lblHump');
            const lblStoreLocator = testData.getCellValue(SHEET_NAME, scenarioname, 'lblStoreLocator');
            const lblaccount = testData.getCellValue(SHEET_NAME, scenarioname, 'lblaccount');
            const lblcoupons = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcoupons');
            await sensaAccountPage.mssgcomparision(camelPromotionsPageObject.hdrpromotions_camel, lblPromotions);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lnkproducts_camel, lblProducts);
            await sensaAccountPage.mssgcomparision(camelHumppagePageObject.lnkhump_camel, lblHump);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lnkstoreLocator_camel, lblStoreLocator);
            await sensaAccountPage.mssgcomparision(sensaAccountPageObject.lnkaccountProd_sensa, lblaccount);
            await elementActions.highlightElement(camelCouponsPageObject.hdrcoupons_camel);
            const availablemobilecoupons = (await camelCouponsPageObject.hdrcoupons_camel).getText();
            const mobilecoupons = (await availablemobilecoupons).replace(/[0-9]/g, '');
            console.log('Generated Text is: ', mobilecoupons);
            console.log('Expected Text is: ', lblcoupons);
            expect(mobilecoupons.trim()).toEqual(lblcoupons);
            await elementActions.click(camelHomepagePageObject.lblhambergercloseicon_camel);
            await elementActions.assertion(camelHomepagePageObject.lnkcamelrewards_camel);
            const lblwarningHeadline = testData.getCellValue(SHEET_NAME, scenarioname, 'lblwarningHeadline');
            const lblwelcometocameldesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblwelcometocameldesc');
            const lblhumpTitle = testData.getCellValue(SHEET_NAME, scenarioname, 'lblhumpTitle');
            const lblfindyourfavourites = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfindyourfavourites');
            const lblyourtast = testData.getCellValue(SHEET_NAME, scenarioname, 'lblyourtast');
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblwarningHeadline_camel, lblwarningHeadline);
            await elementActions.assertion(camelHomepagePageObject.lblwelcometocamel_camel);
            const url = await browser.getUrl();
            if (url.includes('aem')) {
                await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblwelcometocameldesc_camel, lblwelcometocameldesc);
            }
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblhumptitle_camel, lblhumpTitle);
            await elementActions.assertion(camelHomepagePageObject.btncheckitout_camel);
            await elementActions.assertion(camelHomepagePageObject.imghump_camel);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblfindyourfavourites_camel, lblfindyourfavourites);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblyourtast_camel, lblyourtast);
            await elementActions.assertion(camelHomepagePageObject.btnexplore_camel);
            await elementActions.assertion(camelHomepagePageObject.imgdaretodiscover_camel);
            const lblcontactus = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcontactus');
            const lblfaq = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfaq');
            const lbltobbacco = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltobbacco');
            const lblsiterequirement = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsiterequirement');
            const lblsustainability = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsustainability');
            const lbltermsuse = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltermsuse');
            const lblprivacypolicy = testData.getCellValue(SHEET_NAME, scenarioname, 'lblprivacypolicy');
            const lbltextmessage = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltextmessage');
            const lblcamelpoints = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcamelpoints');
            const lbllogout = testData.getCellValue(SHEET_NAME, scenarioname, 'lbllogout');
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lnkcontactUsPostLogin_camel, lblcontactus);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lnkfaq_sensa, lblfaq);
            await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnktobaccopostlogin_camel, lbltobbacco);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lnkSiteRequirementsPostLogin_camel, lblsiterequirement);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lnksustainabilitypostlogin_camel, lblsustainability);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lnktermsofUsepostlogin_sensa, lbltermsuse);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lnkprivacypolicy_camel, lblprivacypolicy);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lnktextmessaging_camel, lbltextmessage);
            await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnkcamelpoitspostlogin_camel, lblcamelpoints);
            await sensaAccountPage.mssgcomparision(camelLoginPageObject.lbllogout_camel, lbllogout);
            await elementActions.click(camelHomepagePageObject.lnkhowitworks_camel);
            await sensaFooterlinkPage.allowpopup();
            await sensaFooterlinkPage.windowswitchandback();
            await elementActions.assertion(camelHomepagePageObject.hdrhowitworks_camel);
            await browser.execute(() => {
                window.close();
            });
            await sensaFooterlinkPage.windowswitchandback();
            await sensaHomepagePage.clickOnEachButtonAndNaviagteBack(camelHomepagePageObject.btnexplore_camel, camelHomepagePageObject.hdrfindyourfavourites_camel);
            console.log('Validated the Content in Home page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Home page', { error });
            throw error;
        }
    }

    async fedbannerValidation(filename: string, sheetname: string, scenarioname: string) {
        try {
            await elementActions.waitForDisplayed(camelHomepagePageObject.lnkthesestatements_camel);
            await elementActions.clickusingJavascript(camelHomepagePageObject.lnkthesestatements_camel);
            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const lblthesestatementsdesc1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc1');
            const lblthesestatementsdesc2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc2');
            const lblthesestatementsdesc3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc3');
            const lblthesestatementsdesc4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc4');
            const lblthesestatementsdesc5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc5');
            const lblthesestatementsdesc6 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc6');
            const lblthesestatementsdesc7 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc7');
            const lblthesestatementsdesc8 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc8');
            const lblthesestatementsdesc9 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc9');
            const lblthesestatementsdesc10 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc10');
            const lblthesestatementsdesc11 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc11');
            const lblthesestatementsdesc12 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc12');
            const lblthesestatementsdesc13 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc13');
            const lblthesestatementsdesc14 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc14');
            const lblthesestatementsdesc15 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc15');
            const lblthesestatementsdesc16 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc16');
            const lblthesestatementsdesc17 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc17');
            const lblthesestatementsdesc18 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc18');
            const lblthesestatementsdesc19 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc19');
            const lblthesestatementsdesc20 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc20');
            const lblthesestatementsdesc21 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc21');
            const lblthesestatementsdesc22 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc22');
            const lblthesestatementsdesc23 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc23');
            const lblthesestatementsdesc24 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc24');
            const lblthesestatementsdesc25 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc25');
            const lblthesestatementsdesc26 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc26');
            const lblthesestatementsdesc27 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc27');
            const lblthesestatementsdesc28 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc28');
            const lblthesestatementsdesc29 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc29');
            const lblthesestatementsdesc30 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthesestatementsdesc30');
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc1_camel, lblthesestatementsdesc1);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc2_camel, lblthesestatementsdesc2);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc3_camel, lblthesestatementsdesc3);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc4_camel, lblthesestatementsdesc4);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc5_camel, lblthesestatementsdesc5);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc6_camel, lblthesestatementsdesc6);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc7_camel, lblthesestatementsdesc7);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc8_camel, lblthesestatementsdesc8);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc9_camel, lblthesestatementsdesc9);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc10_camel, lblthesestatementsdesc10);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc11_camel, lblthesestatementsdesc11);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc12_camel, lblthesestatementsdesc12);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc13_camel, lblthesestatementsdesc13);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc14_camel, lblthesestatementsdesc14);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc15_camel, lblthesestatementsdesc15);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc16_camel, lblthesestatementsdesc16);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc17_camel, lblthesestatementsdesc17);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc18_camel, lblthesestatementsdesc18);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc19_camel, lblthesestatementsdesc19);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc20_camel, lblthesestatementsdesc20);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc21_camel, lblthesestatementsdesc21);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc22_camel, lblthesestatementsdesc22);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc23_camel, lblthesestatementsdesc23);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc24_camel, lblthesestatementsdesc24);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc25_camel, lblthesestatementsdesc25);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc26_camel, lblthesestatementsdesc26);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc27_camel, lblthesestatementsdesc27);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc28_camel, lblthesestatementsdesc28);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc29_camel, lblthesestatementsdesc29);
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblthesestatementsdesc30_camel, lblthesestatementsdesc30);
            await elementActions.clickusingJavascript(camelHomepagePageObject.lblclosefebanner_camel);
            console.log('Validated the Content in Home page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Home page', { error });
            throw error;
        }
    }

    async navigatetoProductspage() {
        try {
            await elementActions.clickusingJavascript(camelHomepagePageObject.btnhamburgerMenu_camel);
            await elementActions.clickusingJavascript(camelHomepagePageObject.lnkproducts_camel);
            await elementActions.assertion(camelHomepagePageObject.lblproductpageheader_camel);

            console.log('Navigated to Products Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Products Page', { error });
            throw error;
        }
    }

}
export default new CameHomePage();