import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import sensaOffersPageObject from '../../page-object/sensa/sensa-offers.pageObject.ts';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import sensaAccountPage from '../commonteps/account.page.ts';
import path from 'path';
import AccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';



class Offers {

    async clickonofferslink() {
        try {
            await elementActions.waitForDisplayed(AccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.clickusingJavascript(AccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.waitForDisplayed(sensaOffersPageObject.hdroffers_sensa);
            await elementActions.click(sensaOffersPageObject.hdroffers_sensa);
            await elementActions.assertion(sensaOffersPageObject.txtoffertitle_sensa);
            console.log('Navigated to Offers Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Offers Page', { error });
            throw error;
        }
    }

    async clickonclaimoffersbuttonandvalidatenavigationtospa() {
        try {
            await elementActions.clickusingJavascript(sensaOffersPageObject.btnclaimoffers_sensa);
            await elementActions.assertion(sensaOffersPageObject.btnunderstood_sensa);
            await browser.execute(() => window.history.back());
            const claimoffers = await sensaOffersPageObject.btnclaimoffers_sensa;
            let tries = 0;
            const maxtries = 4;
            while (!(await claimoffers.isDisplayed()) && tries < maxtries) {
                await browser.execute(() => window.history.back());
                console.log('Navigated Again');
                tries++;
            }
            await elementActions.assertion(sensaOffersPageObject.btnclaimoffers_sensa);
            console.log('Navigated to SPA Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to SPA Page', { error });
            throw error;
        }
    }

    async offersPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const availableOffers = testData.getCellValue(SHEET_NAME, scenarioname, 'lblavailableoffers');
            const headline = testData.getCellValue(SHEET_NAME, scenarioname, 'lblheadline');
            const subline = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsubline');
            const step1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstep1');
            const stepdesc1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstep1desc');
            const step2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstep2');
            const stepdesc2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstep2desc');
            const step3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstep3');
            const stepdesc3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstep3desc');
            const step4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstep4');
            const stepdesc4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstep4desc');
            const lbldisclaimer1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldisclaimer1');
            const lbldisclaimer2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldisclaimer2');
            const lbldisclaimer3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldisclaimer3');
            const lbldisclaimer4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldisclaimer4');
            const lbldisclaimer5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldisclaimer5');
            const lbldisclaimer6 = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldisclaimer6');
            await elementActions.assertion(sensaOffersPageObject.txtoffertitle_sensa);
            await elementActions.assertion(sensaOffersPageObject.imgoffers_sensa);
            const availablemobilecoupons = await (sensaOffersPageObject.lbloffersAvailable_sensa).getText();
            const mobilecoupons = (await availablemobilecoupons).replace(/[0-9]/g, '');
            console.log('Generated Text is: ', mobilecoupons);
            console.log('Expected Text is: ', availableOffers);
            expect(mobilecoupons.trim()).toEqual(availableOffers);
            await sensaAccountPage.mssgcomparision(sensaOffersPageObject.lbloffersheadline_sensa, headline);
            await sensaAccountPage.mssgcomparision(sensaOffersPageObject.lblofferssubline_sensa, subline);
            await sensaAccountPage.mssgcomparision(sensaOffersPageObject.lblofferstep1_sensa, step1);
            await sensaAccountPage.mssgcomparision(sensaOffersPageObject.lblofferdesc1_sensa, stepdesc1);
            await sensaAccountPage.mssgcomparision(sensaOffersPageObject.lblofferstep2_sensa, step2);
            await sensaAccountPage.mssgcomparision(sensaOffersPageObject.lblofferdesc2_sensa, stepdesc2);
            await sensaAccountPage.mssgcomparision(sensaOffersPageObject.lblofferstep3_sensa, step3);
            await sensaAccountPage.mssgcomparision(sensaOffersPageObject.lblofferdesc3_sensa, stepdesc3);
            await sensaAccountPage.mssgcomparision(sensaOffersPageObject.lblofferstep4_sensa, step4);
            await sensaAccountPage.mssgcomparision(sensaOffersPageObject.lblofferdesc4_sensa, stepdesc4);
            await sensaAccountPage.mssgcomparision(sensaOffersPageObject.lblofferdisclaimer1_sensa, lbldisclaimer1);
            await sensaAccountPage.mssgcomparision(sensaOffersPageObject.lblofferdisclaimer2_sensa, lbldisclaimer2);
            await sensaAccountPage.mssgcomparision(sensaOffersPageObject.lblofferdisclaimer3_sensa, lbldisclaimer3);
            await sensaAccountPage.mssgcomparision(sensaOffersPageObject.lblofferdisclaimer4_sensa, lbldisclaimer4);
            await sensaAccountPage.mssgcomparision(sensaOffersPageObject.lblofferdisclaimer5_sensa, lbldisclaimer5);
            await sensaAccountPage.mssgcomparision(sensaOffersPageObject.lblofferdisclaimer6_sensa, lbldisclaimer6);
            console.log('Validated the Content in Offers page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Offers page', { error });
            throw error;
        }
    }


}
export default new Offers();