class AccountPageObject {

    get txtusername_sensa() { return $('//input[@name="j_username"]'); }
    get lnklogoutheader_sensa() { return $('//a[normalize-space()="logout"]'); }
    get txtpassword_sensa() { return $('//input[@id="loginPassword"]'); }
    get btnlogin_sensa() { return $('(//button[@type="submit"])[1]'); }
    get btnhamburgerMenu_sensa() { return $('//*[@class="mobile-nav-icon"]'); }
    get lnkaccount_sensa() { return $('//*[@href="/secure/my-profile.html"][@title="My Account"]'); }
    get lnkaccountProd_sensa() { return $('//*[@href="/secure/my-profile.html"][@title="My Profile"][1]'); }
    get lnkaccount_nas() { return $$('[title="My Profile"]')[0]; }
    get lblaccountheader_sensa() { return $('//*[@class="cmp-profile__header-main-profile"]'); }

    get lnkcontact_sensa() { return $('//label[@id=\'tab-contact-label\']//h4'); }
    get lbllegalName_sensa() { return $('//*[text()="Legal Name"]'); }
    get lblgovtissue_sensa() { return $('//*[@class="cmp-register__sectionDescription"]'); }
    get lblnamemiss_sensa() { return $('//div[@class="cmp-register__legalName"]//p[2]'); }
    get txtfirstName_sensa() { return $('//input[@placeholder="First Name"]'); }
    get lblfirstName_sensa() { return $('//label[@id="labelFirstName"]'); }
    get lbllastName_sensa() { return $('//label[@id="labelLastName"]'); }
    get txtlastName_sensa() { return $('//input[@placeholder="Last Name"]'); }
    get txtcurrentAddr_sensa() { return $('//*[@id="regStreetAddress"]'); }
    get lblcurrentAddr_sensa() { return $('//h4[text()="Current Address"]'); }
    get lblstretaddr_sensa() { return $('//label[@id="labelStreetAddress"]'); }
    get lblzipcode_sensa() { return $('//label[@id="labelZipCode"]'); }
    get txtaptOpt_sensa() { return $('//input[@id="regApt"]'); }
    get txtcity_sensa() { return $('//input[@id="regCity"]'); }
    get lblcity_sensa() { return $('//label[@id="labelCity"]'); }
    get lblstate_sensa() { return $('//label[@id="labelState"]'); }
    get lblmonth_sensa() { return $('//label[@id="labelMonth"]'); }
    get lbldate_sensa() { return $('//label[@id="labelDay"]'); }
    get lblyear_sensa() { return $('//label[@id="labelYear"]'); }
    get lbldob_sensa() { return $('//h4[text()="Date of Birth"]'); }
    get txtstate_sensa() { return $('//select[@id="regState"]'); }
    get lblmobileNumber_sensa() { return $('//h4[text()="Mobile Number "]'); }
    get txtzipCode_sensa() { return $('//*[@id="regZipCode"]'); }
    get txtmobile_sensa() { return $('//*[@id="regMobilePhone"]'); }
    get btnupdate_sensa() { return $('//*[@id="updateContactButton"]'); }
    get txtsuccessmessage_sensa() { return $('//*[@id="contactUpdateSuccess"][@class="updateMessage active"]'); }

    get lnkemail_sensa() { return $('//label[@id=\'tab-email-label\']//h4'); }
    get lblcurrentEmail_sensa() { return $('//h4[text()="Current Email/Username"]'); }
    get lblnewEmail_sensa() { return $('//h4[text()="New Email/Username"]'); }
    get lblconfirmEmail_sensa() { return $('//h4[text()="Confirm New Email/Username"]'); }
    get lblsubscription_sensa() { return $('//h4[text()="Subscriptions"]'); }
    get lblstayloop_sensa() { return $('//p[@class="cmp-register__stepDescription"]'); }
    get txtcurrentEmail_sensa() { return $('//input[@id="regEmail"]'); }
    get txtnewEmail_sensa() { return $('//input[@id="newEmail"]'); }
    get txtcnewEmail_sensa() { return $('//input[@id="confirmEmail"]'); }
    get btnemailUpdate_sensa() { return $('//button[@id="updateEmailButton"]'); }
    get lblemailerrormssg_sensa() { return $('//*[@id="emailUpdateNoMatch"]'); }
    get lblemailsuccessmssg_sensa() { return $('//*[@id="emailUpdateSuccess"]'); }
    get lblsubscriptionsuccessmssg_sensa() { return $('//*[@id="subscriptionsUpdateSuccess"]'); }
    gettxtcheckbox_sensa(brand: string) { return $(`//input[@id='${brand}Checkbox']`); }
    get lblloginerror_sensa() { return $('//*[@class="cmp-login__login-error active"]'); }
    get lblcamecheckbox_sensa() { return $('//*[@id="camelCheckbox"]/following-sibling::span'); }
    get lblcamesnuscheckbox_sensa() { return $('//*[@id="snusCheckbox"]/following-sibling::span'); }
    get lblcougarcheckbox_sensa() { return $('//*[@id="cougarCheckbox"]/following-sibling::span'); }
    get lblgrizzlycheckbox_sensa() { return $('//*[@id="grizzlyCheckbox"]/following-sibling::span'); }
    get lblgrizzlysnuscheckbox_sensa() { return $('//*[@id="grizzlySnusCheckbox"]/following-sibling::span'); }
    get lblgrizzlyNicotinecheckbox_sensa() { return $('//*[@id="grizzlynmoCheckbox"]/following-sibling::span'); }
    get lblkodiakcheckbox_sensa() { return $('//*[@id="kodiakCheckbox"]/following-sibling::span'); }
    get lblleviagrrettcheckbox_sensa() { return $('//*[@id="levigarrettCheckbox"]/following-sibling::span'); }
    get lblluckystrikecheckbox_sensa() { return $('//*[@id="luckystrikeCheckbox"]/following-sibling::span'); }
    get lblnascheckbox_sensa() { return $('//*[@id="naturalAmericanCheckbox"]/following-sibling::span'); }
    get lblnewportcheckbox_sensa() { return $('//*[@id="newportCheckbox"]/following-sibling::span'); }
    get lblpallmallcheckbox_sensa() { return $('//*[@id="pallMallCheckbox"]/following-sibling::span'); }
    get lblsensacheckbox_sensa() { return $('//*[@id="sensaCheckbox"]/following-sibling::span'); }
    get lblvelocheckbox_sensa() { return $('//*[@id="veloCheckbox"]/following-sibling::span'); }
    get lblvusecheckbox_sensa() { return $('//*[@id="vuseCheckbox"]/following-sibling::span'); }


    get lnksecurity_sensa() { return $('//label[@id=\'tab-security-label\']//h4'); }
    get lblpassword_sensa() { return $('//h4[text()="Password"]'); }
    get lblpasswordconditions_sensa() { return $('//p[contains(text(),"Password must be a combination of 8-12 letters and")]'); }
    get lblchallengequestion_sensa() { return $('//h4[text()="Challenge Question"]'); }
    get lblchallengequestionconditions_sensa() { return $('//p[contains(text(),"The Challenge Answer must be 1 to 16 characters lo")]'); }
    get txtcurrentPassword_sensa() { return $('//*[@id=\'currentPassword\']'); }
    get btnpasswordUpdate_sensa() { return $('//button[@id="updatePasswordButton"]'); }
    get btnsecurityanswerUpdate_sensa() { return $('//button[@id="updateChallengeButton"]'); }
    get lblpassworderrormssg_sensa() { return $('//*[@id="passwordUpdateNoMatch"]'); }
    get lblsamepassworderrormssg_sensa() { return $('//*[@id="passwordUpdateSame"]'); }
    get lblpasswordsuccessmssg_sensa() { return $('//*[@id="passwordUpdateSuccess"]'); }
    get lblsecuritysuccessmssg_sensa() { return $('*[id="questionUpdateSuccess"]'); }

    get lnktobaccoPreferences_sensa() { return $('//label[@id="tab-preferences-label"]//h4'); }
    get lbltellus_sensa() { return $('//p[contains(text(),"Tell us a little bit about the kind of products yo")]'); }
    get lblproducttypes_sensa() { return $('.//*[contains(text(),"What types of tobacco")]'); }
    gettxtproductcheckbox_sensa(productName: string) { return $(`//span[normalize-space()='${productName}']`); }
    get productcheckboxes_sensa() { return $$('//input[@name="products"][@type="checkbox"]'); }
    get productcheckboxes_camel() { return $$('//input[@name="products"][@type="checkbox"]//span//input[@name="products"][@type="checkbox"]//parent::*//span'); }
    get txttobaccodrpdown_sensa() { return $$('(//select[contains(@name, "TobaccoPreferencesSurvey")])'); }


    get lblcigarettCheckbox() { return $('//span[normalize-space()="CIGARETTES"]'); }
    get lblvaporCheckbox() { return $('//span[normalize-space()="VAPOR"]'); }
    get lblnicotineCheckbox() { return $('//span[normalize-space()="NICOTINE POUCHES"]'); }
    get lblmoistCheckbox() { return $('//span[normalize-space()="MOIST SNUFF/DIP"]'); }
    get lblsnusCheckbox() { return $('//span[normalize-space()="SNUS"]'); }

    get hdrcigarett_sensa() { return $('//*[text()="CIGARETTES PREFERENCES"]'); }
    get hdrvapor_sensa() { return $('//*[text()="VAPOR PREFERENCES"]'); }
    get hdrnicotine_sensa() { return $('//*[text()="NICOTINE PREFERENCES"]'); }
    get hdrmoist_sensa() { return $('//*[text()="MOIST PREFERENCES"]'); }
    get hdrsnus_sensa() { return $('//*[text()="SNUS PREFERENCES"]'); }

    get txtcigarrettbranddropdown_sensa() { return $('(//*[@class="cmp-form-options__field cmp-form-options__field--drop-down"])[1]'); }
    get txtcigarrettflavordropdown_sensa() { return $('(//*[@class="cmp-form-options__field cmp-form-options__field--drop-down"])[2]'); }
    get txtcigarrettpurchasesropdown_sensa() { return $('(//*[@class="cmp-form-options__field cmp-form-options__field--drop-down"])[3]'); }
    get txtvaporbranddropdown_sensa() { return $('(//*[@class="cmp-form-options__field cmp-form-options__field--drop-down"])[8]'); }
    get txtvaporflavordropdown_sensa() { return $('(//*[@class="cmp-form-options__field cmp-form-options__field--drop-down"])[9]'); }
    get txtvaporpurchasesropdown_sensa() { return $('(//*[@class="cmp-form-options__field cmp-form-options__field--drop-down"])[11]'); }
    get txtvapornicotinepreferencedropdown_sensa() { return $('(//*[@class="cmp-form-options__field cmp-form-options__field--drop-down"])[10]'); }
    get txtnicotinebranddropdown_sensa() { return $('(//*[@class="cmp-form-options__field cmp-form-options__field--drop-down"])[15]'); }
    get txtnicotineflavordropdown_sensa() { return $('(//*[@class="cmp-form-options__field cmp-form-options__field--drop-down"])[16]'); }
    get txtnicotinepurchasesropdown_sensa() { return $('(//*[@class="cmp-form-options__field cmp-form-options__field--drop-down"])[17]'); }
    get txtmoistbranddropdown_sensa() { return $('(//*[contains(@class,"cmp-form-options__field cmp-form-options__field--drop-down")])[4]'); }
    get txtmoistflavordropdown_sensa() { return $('(//*[contains(@class,"cmp-form-options__field cmp-form-options__field--drop-down")])[5]'); }
    get txtmoistpurchasesropdown_sensa() { return $('(//*[contains(@class,"cmp-form-options__field cmp-form-options__field--drop-down")])[7]'); }
    get txtmoistregularcutdropdown_sensa() { return $('(//*[contains(@class,"cmp-form-options__field cmp-form-options__field--drop-down")])[6]'); }
    get txtsnusbranddropdown_sensa() { return $('(//*[@class="cmp-form-options__field cmp-form-options__field--drop-down"])[12]'); }
    get txtsnusflavordropdown_sensa() { return $('(//*[@class="cmp-form-options__field cmp-form-options__field--drop-down"])[13]'); }
    get txtsnuspurchasesropdown_sensa() { return $('(//*[@class="cmp-form-options__field cmp-form-options__field--drop-down"])[14]'); }
    get txtvapecheckbox_sensa() { return $$('//input[contains(@name,"TobaccoPreferencesSurvey.ScreenerSections[0]")]'); }
    get btnsurveyUpdate_sensa() { return $('//button[@type="submit"][@id = "updateSurveyButton"]'); }
    get lbltobaccoSurveymssg_sensa() { return $('//*[@id="updateSurveySuccess"]'); }
    get lblicurrentlyVape_sensa() { return $('//legend[normalize-space()="I currently vape (mark all that apply)"]'); }

    get lblcigarrettbrandquestion_sensa() { return $('(//label[@class="cmp-form-options__label"])[1]'); }
    get lblcigarrettflavorquestion_sensa() { return $('(//label[@class="cmp-form-options__label"])[2]'); }
    get lblcigarrettpurchasesquestion_sensa() { return $('(//label[@class="cmp-form-options__label"])[3]'); }
    get lblvaporbrandquestion_sensa() { return $('(//label[@class="cmp-form-options__label"])[8]'); }
    get lblvaporflavorquestion_sensa() { return $('(//label[@class="cmp-form-options__label"])[9]'); }
    get lblvaporpurchasesquestion_sensa() { return $('(//label[@class="cmp-form-options__label"])[11]'); }
    get lblvapornicotinepreferencequestion_sensa() { return $('(//label[@class="cmp-form-options__label"])[10]'); }
    get lblnicotinebrandquestion_sensa() { return $('(//label[@class="cmp-form-options__label"])[15]'); }
    get lblnicotineflavorquestion_sensa() { return $('(//label[@class="cmp-form-options__label"])[16]'); }
    get lblnicotinepurchasesquestion_sensa() { return $('(//label[@class="cmp-form-options__label"])[17]'); }
    get lblmoistbrandquestion_sensa() { return $('(//label[contains(@class,"cmp-form-options__label")])[4]'); }
    get lblmoistflavorquestion_sensa() { return $('(//label[contains(@class,"cmp-form-options__label")])[5]'); }
    get lblmoistpurchasesquestion_sensa() { return $('(//label[contains(@class,"cmp-form-options__label")])[7]'); }
    get lblmoistregularcutquestion_sensa() { return $('(//label[contains(@class,"cmp-form-options__label")])[6]'); }
    get lblsnusbrandquestion_sensa() { return $('(//label[@class="cmp-form-options__label"])[12]'); }
    get lblsnusflavorquestion_sensa() { return $('(//label[@class="cmp-form-options__label"])[13]'); }
    get lblsnuspurchasesquestion_sensa() { return $('(//label[@class="cmp-form-options__label"])[14]'); }

    get lnkretailAccount_sensa() { return $('//label[@id="tab-dpp-label"]//h4'); }
    get txtcirclekcheckbox_sensa() { return $('//span[text()="Circle K Take it Easy (CIRCLE K MARKETING)"]/parent::*'); }
    get txtxspeedycheckbox_sensa() { return $('//span[text()="Speedy Rewards (SPEEDWAY HQ)"]/parent::*'); }
    get txtrewardscheckbox_sensa() { return $('//span[text()="7Rewards (7-ELEVEN CORPORATION)"]/parent::*'); }
    get txtkwickcheckbox_sensa() { return $('//span[text()="Kwik Rewards (KWIK TRIP HQ)"]/parent::*'); }
    get txtmurphytextbox_sensa() { return $('//span[text()="Murphy Drive Rewards (MURPHY USA)"]/parent::*'); }
    get lbllinksuccessmssg_sensa() { return $('//*[@id="dppOptInSuccessful"]'); }
    get lblunlinksuccessmssg_sensa() { return $('//*[@id="dppOptOutSuccessful"]'); }
    get txtcheckboxes_sensa() { return $$('//*[@id="dppTab"]//*[@type="checkbox"]'); }


    get lnkcoupons_sensa() { return $('//label[@id="tab-coupons-label"]//h4'); }
    get lblcouponscount_sensa() { return $('//span[@class="cmp-profile__mobile-offers-cta-count moreThan"]'); }
    get lblavailableCoupons_sensa() { return $('//*[text()="Available Mobile Coupons "]'); }
    get lblsavings_sensa() { return $('//*[@class="cmp-profile__savings-text"]'); }
    get lblclaimedmobilecoupons_sensa() { return $('//*[text()="*Claimed mobile coupons only"]'); }
    get lblsavingcountbox$_sensa() { return $('(//*[@class="cmp-profile__savings-count-box"])[1]'); }
    get lblsavingcountbox0_sensa() { return $('(//*[@class="cmp-profile__savings-count-box"])[2]'); }

    get lnklogout_sensa() { return $('//label[@id="tab-logout-label"]//h4'); }

}
export default new AccountPageObject();