class ForgotUsernamePageObject {

    get lnkforgotUsername_sensa() { return $('//a[contains(text(),"Username")]'); }
    get btnnextStep_sensa() { return $('//form[@class="cmp-register__stepTwoQuiz-form"]//button[@type="submit"]'); }
    get txtusername_sensa() { return $('//*[@class = "username"]'); }
    get btnlogin_sensa() { return $('//form[@name="loginform"]//button[@type="submit"]'); }
    get lblallfields_sensa() { return $('//*[contains(text(),"All fields ")]'); }
    get hdrforgotusername_sensa() { return $('//div[@class="cmp-register__forgotUsernameStepOne active"]//div[@class="cmp-register__stepHeader"]//h3'); }
    get lblfillrequiredinformation_sensa() { return $('//p[contains(text(),"We can help. Please fill out the required informat")]'); }
    get lblneedassistence_sensa() { return $('//*[text()="If you need assistance, please call "]'); }
    get lnlgotologin_sensa() { return $('//a[normalize-space()="Go to Login"]'); }
    get hdrverifyIdentity_sensa() { return $('//*[text()="Verify identity"]'); }
    get lblpleaseanswer_sensa() { return $('//*[contains(text(),"Please answer the following")]'); }
    get lblquestion_sensa() { return $('//div[@class="question-container"]//label'); }
    get lblgoodnews_sensa() { return $('//h3[contains(text(),"Good news! We found your username. Please enter yo")]'); }
    get lblhereisusername_sensa() { return $('//*[contains(text()," Here is your username:")]'); }
    get lblenterpassword_sensa() { return $('//*[contains(text(),"Enter your password")]'); }
    get lblresetpassword_sensa() { return $('//*[contains(text(),"Do you need to")]'); }
    get lblerrormssg_sensa() { return $('//span[@class="cmp-register__input-error answer-invalid active"]'); }
    get lblcontactUs_sensa() { return $('//*[contains(text(),"CONTACT US")]'); }
    get lblcontactUs_nas() { return $('(//*[text()="Contact Us"])'); }
    get lblcontactUs_prodnas() { return $('(//*[text()="Contact Us"])[2]'); }
    get lblerrorFirstName_sensa() { return $('//*[@class="validation wrongCheck"][contains(text(),"First ")]'); }
    get lblerrorLastName_sensa() { return $('//*[@class="validation wrongCheck"][contains(text(),"Last ")]'); }
    get lblerrorStreetaddr_sensa() { return $('//*[@class="validation wrongCheck"][contains(text(),"Street")]'); }
    get lblerrorZipCode_sensa() { return $('//*[@class="validation wrongCheck"][contains(text(),"valid zip")]'); }
    get lblerrorCity_sensa() { return $('//*[@class="validation wrongCheck"][contains(text(),"city")]'); }
    get lblerrorState_sensa() { return $('//*[@class="validation wrongSelect"][contains(text(),"state")]'); }
    get lblerrorMonth_sensa() { return $('//*[@class="validation wrongSelect"][contains(text(),"month")]'); }
    get lblerrorEmail_sensa() { return $('//*[@class="validation wrongCheck"][contains(text(),"email")]'); }
    get lblerrorDate_sensa() { return $('//*[@class="validation wrongSelect"][contains(text(),"date")]'); }
    get lblerrorYear_sensa() { return $('//*[@class="validation wrongSelect"][contains(text(),"year")]'); }
    get lblAnErrorOccured_sensa() { return $('//*[@class="cmp-register__input-error generic-error active"]'); }
    get lblerrorAnswer_sensa() { return $('//*[@class="validation wrongCheck"][contains(text(),"field")]'); }
    get lblerrorPassword_sensa() { return $('//*[@class="validation wrongCheck"][contains(text(),"field")]'); }
    get lnkresetPassword_sensa() { return $('a[href="/forgot-password.html"]'); }
    get lblPasswordHeader_sensa() { return $('//*[@class="cmp-register__forgotPasswordStepOne active"] //h3[@class="cmp-register__stepHeader-title"]'); }
    get btncontinue_sensa() { return $('//form[@class="cmp-register__stepOne-form"]//span[@class="cmp-button__text"]/parent::*'); }

}
export default new ForgotUsernamePageObject();