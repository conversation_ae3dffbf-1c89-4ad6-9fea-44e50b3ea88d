class CouponsCamelPageObject {

    get hdrcoupons_camel() { return $('//*[@title="Coupons"]'); }
    get lblclaimedsavings_camel() { return $('//*[contains(@class,"cmp-offers-mobile-only__savings-text")]'); }
    get btnRedeem_camel() { return $('//*[contains(@class,"cmp-button__text")][text()="Redeem Now"]'); }
    get hdrclaimOffers_camel() { return $('//h2[normalize-space()="How to Claim Offers"]'); }
    get lblstep1_camel() { return $('//h4[normalize-space()="Step 1"]'); }
    get lblstep2_camel() { return $('//h4[normalize-space()="Step 2"]'); }
    get lblstep3_camel() { return $('//h4[normalize-space()="Step 3"]'); }
    get lblstep4_camel() { return $('//h4[normalize-space()="Step 4"]'); }
    get lblstep5_camel() { return $('//h4[normalize-space()="Step 5"]'); }
    get imgStep1_camel() { return $('//*[@alt="Step 1"]'); }
    get imgStep2_camel() { return $('//*[@alt="Step 2"]'); }
    get imgStep3_camel() { return $('//*[@alt="Step 3"]'); }
    get imgStep4_camel() { return $('//*[@alt="Step 4"]'); }
    get imgStep5_camel() { return $('//*[@alt="Step 5"]'); }
    get lblofferDisclaimer1_camel() { return $('//*[contains(@class,"cmp-grid-container__items row")]//parent::*//p[1]'); }
    get lblofferDisclaimer2_camel() { return $('//*[contains(@class,"cmp-grid-container__items row")]//parent::*//p[2]'); }
    get lblofferDisclaimer3_camel() { return $('//*[contains(@class,"cmp-grid-container__items row")]//parent::*//p[3]'); }
    get lblofferDisclaimer4_camel() { return $('//*[contains(@class,"cmp-grid-container__items row")]//parent::*//p[4]'); }
    get lblofferDisclaimer5_camel() { return $('//*[contains(@class,"cmp-grid-container__items row")]//parent::*//p[5]'); }
    get lblofferDisclaimer6_camel() { return $('//*[contains(@class,"cmp-grid-container__items row")]//parent::*//p[6]'); }
    get lblofferDisclaimer7_camel() { return $('//*[contains(@class,"cmp-grid-container__items row")]//parent::*//p[7]'); }
    get lblFAQ_camel() { return $('//h2[normalize-space()="FAQ"]'); }
    get lblFAQdesc_camel() { return $('//p[normalize-space()="Get the answers to the questions we hear the most."]'); }
    get lblFAQQ1_camel() { return $('(//*[contains(@class,"cmp-accordion-item__headerText")]//span)[1]'); }
    get lblFAQQ2_camel() { return $('(//*[contains(@class,"cmp-accordion-item__headerText")]//span)[2]'); }
    get lblFAQQ3_camel() { return $('(//*[contains(@class,"cmp-accordion-item__headerText")]//span)[3]'); }
    get lblFAQQ4_camel() { return $('(//*[contains(@class,"cmp-accordion-item__headerText")]//span)[4]'); }
    get lblFAQQ5_camel() { return $('(//*[contains(@class,"cmp-accordion-item__headerText")]//span)[5]'); }
    get lblFAQA1_camel() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[1]'); }
    get lblFAQA2_camel() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[2]'); }
    get lblFAQA21_camel() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//ol)'); }
    get lblFAQA3_camel() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[3]'); }
    get lblFAQA4_camel() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[4]'); }
    get lblFAQA5_camel() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[5]'); }


}
export default new CouponsCamelPageObject();