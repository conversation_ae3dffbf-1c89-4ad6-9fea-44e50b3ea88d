Feature: Hump Page Validation for Camel Website

    Scenario Outline: Vaidate Hump Page  for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        When The user clicks on Hump
        Then The user Validates Hump Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user clicks on anyone button on each link and enters valid comments as <comments> and verify last comment timestap as <timestamp>
        Then The user validates that successfully logged out of camel brand

        @CamelValidateHumpPage_Validation_QA
        Examples:
            | Brand | URL                         | Username                           | Password  | comments | timestamp   | filename              | sheetname | scenarioname       |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | Testing  | 0 Hours Ago | aem-mobile-camel.json | Hump      | Validate Hump Page |


        @CamelValidateHumpPage_Validation_PROD 
        Examples:
            | Brand | URL                   | Username                             | Password  | comments | timestamp   | filename              | sheetname | scenarioname       |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | Testing  | 0 Hours Ago | aem-mobile-camel.json | Hump      | Validate Hump Page |

