import { Then, When } from '@wdio/cucumber-framework';
import camelHumpPage from '../../pages/camel/camel-hump.page.ts';
import logger from '../../support/utils/logger.util.ts';
import camelHumppagePageObject from '../../page-object/camel/camel-humppage.pageObject.ts';

When(/^The user clicks on Hump$/, async function () {
    await camelHumpPage.humpPage();
    await expect(camelHumppagePageObject.lnkall_camel).toBeDisplayed();
    logger.info('User Navigates to Hump Page');
});

Then(/^The user Validates Hump Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await camelHumpPage.humpPageValidation(filepath, sheetname, scenarioname);
    await camelHumpPage.clickoneachlinkandVerify();
    await expect(camelHumppagePageObject.lnkalllinks_camel).toBeDisplayed();
    logger.info('User Validates Hump Page');

});

Then(/^The user clicks on anyone button on each link and enters valid comments as (.*) and verify last comment timestap as (.*)$/, async function (comment: string, timestamp: string) {
    await camelHumpPage.clickoneachbuttonandVerifycomments(comment, timestamp);
    await expect(camelHumppagePageObject.lnkalllinks_camel).toBeDisplayed();
    logger.info('User Validates Comments in Hump Page');
});

