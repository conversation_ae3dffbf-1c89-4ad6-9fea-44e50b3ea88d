import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import AccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';
import NasOurDiffernecPageObject from '../../page-object/americanspirit/americanspirit-ourdifference.pageObject.ts';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import path from 'path';
import accountPage from '../commonteps/account.page.ts';

class NasOurDifferencepage {

    async clickonDiscover() {
        try {
            await elementActions.waitForDisplayed(AccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.clickusingJavascript(AccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.click(NasOurDiffernecPageObject.Nas_mobileSubMenue);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_discoverTitle);
            await elementActions.clickusingJavascript(NasOurDiffernecPageObject.Nas_discoverTitle);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_ourDifferenceImg);
            console.log('Navigated to Discover Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Discover Page', { error });
            throw error;
        }
    }

    async clickonOurPartner() {
        try {
            await elementActions.waitForDisplayed(AccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.clickusingJavascript(AccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.click(NasOurDiffernecPageObject.Nas_mobileSubMenue);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_ourPartners);
            await elementActions.clickusingJavascript(NasOurDiffernecPageObject.Nas_ourPartners);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_partnersHeading);
            console.log('Navigated to Our PartnerDiscover Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Our PartnerDiscover Page', { error });
            throw error;
        }
    }

    async clickonOurTobacco() {
        try {
            await elementActions.waitForDisplayed(AccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.clickusingJavascript(AccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.click(NasOurDiffernecPageObject.Nas_mobileSubMenue);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_ourTobaccoTitle);
            await elementActions.clickusingJavascript(NasOurDiffernecPageObject.Nas_ourTobaccoTitle);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_uniquelyCrafted);
            console.log('Navigated to Our Tobacco Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Our Tobacco Page', { error });
            throw error;
        }
    }

    async clickoneachbuttonfromdiscoverpage() {
        try {
            await elementActions.clickusingJavascript(NasOurDiffernecPageObject.Nas_learnMore_1);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_uniquelyCraftedImg);
            await browser.execute(() => window.history.back());
            await elementActions.clickusingJavascript(NasOurDiffernecPageObject.Nas_learnMore_2);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_sustainability_1);
            await browser.execute(() => window.history.back());
            await elementActions.clickusingJavascript(NasOurDiffernecPageObject.Nas_learnMore_3);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_partnersHeading);
            await browser.execute(() => window.history.back());
            await elementActions.clickusingJavascript(NasOurDiffernecPageObject.Nas_learnMore_4);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_uniquelyCraftedImg);
            await browser.execute(() => window.history.back());
            await elementActions.click(NasOurDiffernecPageObject.Nas_doesOrganicTobaccoTasteDiffere);
            await elementActions.clickusingJavascript(NasOurDiffernecPageObject.Nas_learnMore_5);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_findYourFavoritePack);
            await browser.execute(() => window.history.back());
            await elementActions.clickusingJavascript(NasOurDiffernecPageObject.Nas_learnMore_6);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_sustainability_1);
            await browser.execute(() => window.history.back());
            await elementActions.clickusingJavascript(NasOurDiffernecPageObject.Nas_learnMore_7);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_partnersHeading);
            await browser.execute(() => window.history.back());
            await elementActions.click(NasOurDiffernecPageObject.Nas_cardOneFront);
            await elementActions.clickusingJavascript(NasOurDiffernecPageObject.Nas_learnMore_8);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_cleanUpWithButtPouches);
            await browser.execute(() => window.history.back());
            console.log('Validated all Button from Discover Page Successfully');
        } catch (error) {
            logger.error('Failed to Validated all Button from Discover Page', { error });
            throw error;
        }
    }

    async DiscoverPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const lblouruncompromising = testData.getCellValue(SHEET_NAME, scenarioname, 'lblouruncompromising');
            const lblTobaccowe = testData.getCellValue(SHEET_NAME, scenarioname, 'lblTobaccowe');
            const lblcrafting = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcrafting');
            const lblZerowaste = testData.getCellValue(SHEET_NAME, scenarioname, 'lblZerowaste');
            const lbldifference = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldifference');

            const lblexplore = testData.getCellValue(SHEET_NAME, scenarioname, 'lblexplore');
            const lblexploredisc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblexploredisc');
            const lblourTobacco = testData.getCellValue(SHEET_NAME, scenarioname, 'lblourTobacco');
            const lbluniquelycraftet = testData.getCellValue(SHEET_NAME, scenarioname, 'lbluniquelycraftet');
            const lbluniquelycraftetdes = testData.getCellValue(SHEET_NAME, scenarioname, 'lbluniquelycraftetdes');

            const lblorganitobacco = testData.getCellValue(SHEET_NAME, scenarioname, 'lblorganitobacco');
            const lblorganitobaccodes = testData.getCellValue(SHEET_NAME, scenarioname, 'lblorganitobaccodes');
            const lblourprocess = testData.getCellValue(SHEET_NAME, scenarioname, 'lblourprocess');
            const lblsustainability = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsustainability');
            const lblexplore1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblexplore1');

            await elementActions.assertion(NasOurDiffernecPageObject.Nas_ourDifferenceImg);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_ourUncompromisingPara);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_andTheTobacco, lblTobaccowe);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_CraftingOrganic, lblcrafting);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_learnMore_1);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_Zerowaste, lblZerowaste);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_learnMore_2);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_DifferenceForest, lbldifference);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_learnMore_3);

            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_exploreLike_2, lblexplore);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_wantToKnowCraft_2, lblexploredisc);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_checkItOut_2);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_catchUpHeadlineDesktopImg);

            await elementActions.assertion(NasOurDiffernecPageObject.Nas_learnMoreAboutWhat);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_ourTobaccoText, lblourTobacco);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_uniquelyCrafted, lbluniquelycraftet);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_fromLandToYourHand, lbluniquelycraftetdes);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_learnMore_4);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_doesOrganicTobaccoTasteDiffere, lblorganitobacco);
            await elementActions.click(NasOurDiffernecPageObject.Nas_doesOrganicTobaccoTasteDiffere);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_organicTobaccoDeliversTrue, lblorganitobaccodes);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_learnMore_5);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_ourProcess_1, lblourprocess);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_sustainability_1, lblsustainability);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_exploreOurInitiatives_1, lblexplore1);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_learnMore_6);
            await elementActions.click(NasOurDiffernecPageObject.Nas_cardFourFront);

            const lblextraDust = testData.getCellValue(SHEET_NAME, scenarioname, 'lblextraDust');
            const lblourPartners = testData.getCellValue(SHEET_NAME, scenarioname, 'lblourPartners');
            const lbltogetherNature = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltogetherNature');
            const lblweCantDoThis = testData.getCellValue(SHEET_NAME, scenarioname, 'lblweCantDoThis');
            const lblterraCycle = testData.getCellValue(SHEET_NAME, scenarioname, 'lblterraCycle');

            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_extraTobaccoDustText, lblextraDust);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_exploreOurProcessText);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_ourPartnersText, lblourPartners);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_togetherForNatureText, lbltogetherNature);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_weCantDoThisAloneText, lblweCantDoThis);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_learnMore_7);
            await elementActions.click(NasOurDiffernecPageObject.Nas_cardOneFront);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_partnerTerraCycleText, lblterraCycle);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_learnMore_8);
            await this.clickoneachbuttonfromdiscoverpage();

            console.log('Validated the Content in Discover page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Discover page', { error });
            throw error;
        }
    }

    async OurPartnersPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const lblpartners = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpartners');
            const lblteamingupcare = testData.getCellValue(SHEET_NAME, scenarioname, 'lblteamingupcare');
            const lblkeepamericabeautiful = testData.getCellValue(SHEET_NAME, scenarioname, 'lblkeepamericabeautiful');
            const lblleadingnational = testData.getCellValue(SHEET_NAME, scenarioname, 'lblleadingnational');
            const lblkeyinitiatives = testData.getCellValue(SHEET_NAME, scenarioname, 'lblkeyinitiatives');
            const lblgreatamericancleanup = testData.getCellValue(SHEET_NAME, scenarioname, 'lblgreatamericancleanup');
            const lblamericarecyclesday = testData.getCellValue(SHEET_NAME, scenarioname, 'lblamericarecyclesday');
            const lblcigaretteprevention = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcigaretteprevention');


            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_teamingUpCare_2, lblteamingupcare);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_textAlignCenter_5);

            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_keepAmericaBeautiful, lblkeepamericabeautiful);
            await elementActions.click(NasOurDiffernecPageObject.Nas_toggleArrow_1);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_imageIndex_2);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_leadingNationalText, lblleadingnational);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_keyInitiatives_1, lblkeyinitiatives);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_greatAmericanCleanup_2, lblgreatamericancleanup);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_americaRecyclesDay_2, lblamericarecyclesday);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_cigarettePrevention_2, lblcigaretteprevention);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_visitPartner_2);

            const lblkeepamericabeautiful3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblkeepamericabeautiful3');
            const lblrodaleinstitute = testData.getCellValue(SHEET_NAME, scenarioname, 'lblrodaleinstitute');
            const lblfurtheringorganic1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfurtheringorganic1');
            const lblkeyinitiatives2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblkeyinitiatives2');
            const lblhelpingfarmers2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblhelpingfarmers2');
            const lblresearchingfarming2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblresearchingfarming2');
            const lbleducatingconsumers2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lbleducatingconsumers2');
            const lblfurtheringorganic3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfurtheringorganic3');
            const lblterracycle = testData.getCellValue(SHEET_NAME, scenarioname, 'lblterracycle');


            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_keepAmericaBeautiful_3, lblkeepamericabeautiful3);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_imageIndex_3);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_rodaleInstitute, lblrodaleinstitute);
            await elementActions.click(NasOurDiffernecPageObject.Nas_toggleArrow_2);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_imageIndex_4);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_furtheringOrganic_1, lblfurtheringorganic1);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_keyInitiatives_2, lblkeyinitiatives2);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_helpingFarmers_2, lblhelpingfarmers2);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_researchingFarming_2, lblresearchingfarming2);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_educatingConsumers_2, lbleducatingconsumers2);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_visitPartnerRodale_2);

            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_furtheringOrganic_3, lblfurtheringorganic3);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_imageIndex_5);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_terracycle, lblterracycle);
            await elementActions.click(NasOurDiffernecPageObject.Nas_toggleArrow_3);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_imageIndex_6);

            const lblrecyclingpioneer = testData.getCellValue(SHEET_NAME, scenarioname, 'lblrecyclingpioneer');
            const lblkeyinitiatives3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblkeyinitiatives3');
            const lblrecyclingplatforms2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblrecyclingplatforms2');
            const lblpioneersofloop2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpioneersofloop2');
            const lblterracycleprograms3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblterracycleprograms3');

            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_recyclingPioneer, lblrecyclingpioneer);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_keyInitiatives_3, lblkeyinitiatives3);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_recyclingPlatforms_2, lblrecyclingplatforms2);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_pioneersOfLoop_2, lblpioneersofloop2);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_visitPartner_3);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_terraCyclePrograms_3, lblterracycleprograms3);

            await elementActions.assertion(NasOurDiffernecPageObject.Nas_imageIndex_7);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_imageIndex_8);

            const lblourTobacco_bold = testData.getCellValue(SHEET_NAME, scenarioname, 'lblourTobacco_bold');
            const lblourProcess_bold = testData.getCellValue(SHEET_NAME, scenarioname, 'lblourProcess_bold');
            const lblourPartners_bold = testData.getCellValue(SHEET_NAME, scenarioname, 'lblourPartners_bold');
            const lblthanksForDisposing_2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthanksForDisposing_2');

            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_ourTobacco_bold, lblourTobacco_bold);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_ourProcess_bold, lblourProcess_bold);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_ourPartners_bold, lblourPartners_bold);
            await elementActions.assertion(NasOurDiffernecPageObject.Nas_imageIndex_9);
            await accountPage.mssgcomparision(NasOurDiffernecPageObject.Nas_thanksForDisposing_2, lblthanksForDisposing_2);



            console.log('Validated the Content in Our Partners page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Our Partners page', { error });
            throw error;
        }
    }
}
export default new NasOurDifferencepage();