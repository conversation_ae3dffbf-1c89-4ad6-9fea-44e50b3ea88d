import { Then, When } from '@wdio/cucumber-framework';
import sensaStorelocatorPage from '../../pages/commonteps/storelocator.page.ts';
import sensaLoginPage from '../../pages/sensa/sensa-login.page.ts';
import logger from '../../support/utils/logger.util.ts';
import storelocatorObject from '../../page-object/sensa/sensa-storelocatorObject.ts';


Then(/^The user click on Store Locator from Hamburger menu$/, async function () {
    await sensaStorelocatorPage.navigateToStorelocatorPage();
    const url = await browser.getUrl();
    if (url.includes('americanspirit')) {
        await expect(storelocatorObject.sensafindStoresCamel).toBeDisplayed();
    } else {
        await expect(storelocatorObject.sensafindastore).toBeDisplayed();
    }
    logger.info('Navigated to Store Locator Page Successfully');
});

Then(/^The user click on Use My Location$/, async function () {
    await sensaStorelocatorPage.usemylocation();
    const currentUrl = await browser.getUrl();
    if (currentUrl.includes('camel') || currentUrl.includes('americanspirit')) {
        await expect(storelocatorObject.sensafindStoresCamel).toBeDisplayed();
    } else {
        await expect(storelocatorObject.sensafindStores).toBeDisplayed();
    }
    logger.info('Clicked on Use MY  Location Successfully');
    ;
});

When(/^The user validate the Store locator page$/, async function () {
    await sensaStorelocatorPage.validateAllStores();
    await expect(storelocatorObject.getaddressLine1(2)).toBeDisplayed();
    logger.info('Validated all initial stores');
});

When(/^click on Load more and validate more 10 closest stores$/, async function () {
    await sensaStorelocatorPage.validateLoadMoreStores();
    await expect(storelocatorObject.getaddressLine1(2)).toBeDisplayed();
    logger.info('Validated Load More store results');

});
Then(/^The user enter valid zipcode (.*) and click on Find stores$/, async function (Zipcode: string) {
    await sensaStorelocatorPage.enterZipcode(Zipcode);
    const currentUrl = await browser.getUrl();
    if (currentUrl.includes('camel') || currentUrl.includes('americanspirit')) {
        await expect(storelocatorObject.sensafindStoresCamel).toBeDisplayed();
    } else {
        await expect(storelocatorObject.sensafindStores).toBeDisplayed();
    }
    logger.info('Entered Zipcode and the stores are displayed Successfully');
});
When(/^I set my location to "([^"]*)" and "([^"]*)"$/, async function (latitude: string, longitude: string) {
    // [Given] Sets up the initial state of the system.
    const lat = parseFloat(latitude);
    const lon = parseFloat(longitude);
    await sensaLoginPage.setLocation(lat, lon);
    logger.info('Setting Location');
});
Then(/^The user click on Filter by Product and validate all Product from filter by Product$/, async function () {
    await sensaStorelocatorPage.ClickonFilterbyProductvalidateAll6ProductsFromFilterByProduct();
    await expect(storelocatorObject.btnfilterproduct).toBeDisplayed();
    logger.info('Setting Location');

});

