import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import path from 'path';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import sensaAccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';
import sensaAccountPage from '../commonteps/account.page.ts';
import camelHumppagePageObject from '../../page-object/camel/camel-humppage.pageObject.ts';
import camelPromotionsPage from './camel-promotions.page.ts';


class humpPage {

    async humpPage() {
        try {
            await elementActions.waitForDisplayed(sensaAccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.clickusingJavascript(sensaAccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.clickusingJavascript(camelHumppagePageObject.lnkhump_camel);
            await elementActions.waitForDisplayed(camelHumppagePageObject.lnkall_camel);
            await elementActions.assertion(camelHumppagePageObject.lnkall_camel);
            console.log('Navigated to Hump Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Hump Page', { error });
            throw error;
        }
    }

    async humpPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const lnkall = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkall');
            const lnknightout = testData.getCellValue(SHEET_NAME, scenarioname, 'lnknightout');
            const lnkdiscoversound = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkdiscoversound');
            const lnkadventurescale = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkadventurescale');
            const lnkorigins = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkorigins');
            const lnkdarelist = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkdarelist');
            const lnkterritory = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkterritory');
            const lnknewbienatural = testData.getCellValue(SHEET_NAME, scenarioname, 'lnknewbienatural');
            await elementActions.waitForDisplayed(camelHumppagePageObject.lnknewbienatural_camel);
            await sensaAccountPage.mssgcomparision(camelHumppagePageObject.lnkall_camel, lnkall);
            await sensaAccountPage.mssgcomparision(camelHumppagePageObject.lnknightout_camel, lnknightout);
            await sensaAccountPage.mssgcomparision(camelHumppagePageObject.lnkdiscoversound_camel, lnkdiscoversound);
            await sensaAccountPage.mssgcomparision(camelHumppagePageObject.lnkadventurescale_camel, lnkadventurescale);
            await sensaAccountPage.mssgcomparision(camelHumppagePageObject.lnkorigins_camel, lnkorigins);
            await sensaAccountPage.mssgcomparision(camelHumppagePageObject.lnkdarelist_camel, lnkdarelist);
            await sensaAccountPage.mssgcomparision(camelHumppagePageObject.lnkterritory_camel, lnkterritory);
            await sensaAccountPage.mssgcomparision(camelHumppagePageObject.lnknewbienatural_camel, lnknewbienatural);
            console.log('Validated the Content in  Hump page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Hump page', { error });
            throw error;
        }
    }


    async clickoneachlinkandVerify() {
        try {
            const allinks = await camelHumppagePageObject.lnkalllinks_camel;
            const updatedTitles = await camelHumppagePageObject.lbltitle_camel;
            for (let i = 0; i < await allinks.length; i++) {
                const links = allinks[i];
                console.log('links', allinks.length);
                const ExpectedText = (await links.getText());
                await elementActions.click(links);
                for (let j = 1; j < 2; j++) {
                    const titles = updatedTitles[j];
                    console.log(`Title count: ${updatedTitles.length}`);
                    await elementActions.waitForDisplayed(camelHumppagePageObject.getlbllike_camel(j));
                    await elementActions.assertion(camelHumppagePageObject.getlbllike_camel(j));
                    await elementActions.assertion(camelHumppagePageObject.getlblcomment_camel(j));
                    await elementActions.assertion(camelHumppagePageObject.getlbltitle_camel(j));
                    if (!ExpectedText.includes('ALL')) {
                        await sensaAccountPage.mssgcomparision(titles, ExpectedText);
                    }
                    await elementActions.assertion(camelHumppagePageObject.getlbltitledesc_camel(j));
                    await elementActions.assertion(camelHumppagePageObject.getlblbuttons_camel(j));

                }
            }


            console.log('Navigated to Hump Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Hump Page', { error });
            throw error;
        }
    }

    async clickoneachbuttonandVerifycomments(comment: string, timestamp: string) {
        try {
            const allinks = await camelHumppagePageObject.lnkalllinks_camel;
            for (let i = 0; i < await allinks.length; i++) {
                const links = allinks[i];
                await elementActions.click(links);
                await elementActions.waitForDisplayed(camelHumppagePageObject.btnloadmore_camel);
                const loadmore = await camelHumppagePageObject.btnloadmore_camel;
                await elementActions.waitForDisplayed(loadmore);
                await elementActions.clickusingJavascript(loadmore);
                for (let j = 1; j < 1; j++) {
                    await elementActions.clickusingJavascript(camelHumppagePageObject.getlblbuttons_camel(j));
                    await elementActions.assertion(camelHumppagePageObject.lbleachPagetitle_camel);
                    await camelPromotionsPage.commentverification(comment, timestamp);
                    await browser.execute(() => window.history.back());
                }
            }
            console.log('Validated buttons and comments in Hump Page Successfully');
        } catch (error) {
            logger.error('Failed to Validate buttons and comments in Hump Page', { error });
            throw error;
        }
    }

}
export default new humpPage();
