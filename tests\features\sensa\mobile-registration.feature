Feature: Registration Validation for Sensa Brand

    Scenario Outline: Validate that the user should be able to register using the Registration functionality for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        Then The user clicks on the Join Now
        When The user enters the valid details added in verated data and click continue button
        Then The user enters user details in the Account Set up page with Password <Password> Confirm Password <ConfirmPassword> Security Question <Securityquestion> Security answer <Securityanswer> in the Account setup page
        When The user validates and enter the phone number <phonenumber> in the popup
        Then The user enters valid details Verify your identity page
        Then The user validate signup page page
        When The user click on <product> product checkbox
        Then The user validates the page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname> based on <product>
        When The user selects brand <brand> flavour <flavour> last purchases <purchases> and prefer nicotine <prefernicotine> and regular cur <regularcut> if present based on <product>
        Then The user should be able to login to the application successfully
        Then The user validates that successfully logged out

        @Sensa_Registration_withTobacco_preferences_QA
        Examples:
            | Brand | URL                             | siteurl                  | Streetaddress   | city     | state | Zipcode | Password  | ConfirmPassword | Securityquestion                        | Securityanswer | phonenumber   | product         | brand  | flavour     | purchases | prefernicotine | regularcut | filename              | sheetname    | scenarioname                |
            | Sensa | https://aem-stage.sensavape.com | https://rai.veratad.app/ | 950 The Alameda | San Jose | CA    | 95126   | Password1 | Password1       | What was the name of your first school? | school         | (000)000-0000 | MOIST SNUFF/DIP | KODIAK | WINTERGREEN | 7         | Nicotine       | FINE CUT   | aem-mobile-sensa.json | Registration | Validate Registration Pages |

    Scenario Outline: Validate Registration flow When user registers with a email which is already registered in other brand
        Given The user is on the login page for <Brand> with login <URL>
        When The user enters email as <Email>
        Then The user navigates to signin page and validates error message <message>

        @Sensa_Register_withAlreadyRegisteredUserofdiffbrand_QA
        Examples:
            | Brand | URL                             | Email                                | message                                                                                         |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | An account already exists for this email address. Please use your Newport credentials to login. |

    Scenario Outline: Validate Registration flow When user registers with a registered email in other brand
        Given The user is on the login page for <Brand> with login <URL>
        When The user enters email as <Email>
        When The user enters first Name as <FirstName> Last Name as <LastName> street address as <streetAddr> Zipcode as <zipCode> City as <city> and DOB as <dateOfBirth>
        Then The user navigates to signin page and validates message <message>

        @Sensa_Register_withAlreadyRegisteredUserofsamebrand_QA
        Examples:
            | Brand | URL                             | Email                             | FirstName | LastName        | streetAddr      | city     | state | zipCode | dateOfBirth | message                                                                                                      |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | olive     | snvjSFVVqiGFXlU | 950 The Alameda | San Jose | CA    | 95126   | 01-01-2001  | Good News! You already have an account. Please use <NAME_EMAIL> to login. |

    Scenario Outline: Validate that the user should be able to register using the Registration functionality for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        Then The user clicks on the Join Now
        Then The user validates Tell us Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user enters the valid details added in verated data and click continue button
        Then The user validates Account Setup Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user enters user details in the Account Set up page with Password <Password> Confirm Password <ConfirmPassword> Security Question <Securityquestion> Security answer <Securityanswer> in the Account setup page
        When The user validates and enter the phone number <phonenumber> in the popup
        Then The user validates Verify your identity page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user enters valid details Verify your identity page
        Then The user validates Signup with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user validate signup page page
        Then The user validate congratulation message page
        Then The user should be able to login to the application successfully
        Then The user validates that successfully logged out

        @Sensa_Registration_withoutTobacco_preferences_QA
        Examples:
            | Brand | URL                             | siteurl                  | Streetaddress   | city     | state | Zipcode | Password  | ConfirmPassword | Securityquestion                        | Securityanswer | phonenumber   | filename              | sheetname    | scenarioname                |
            | Sensa | https://aem-stage.sensavape.com | https://rai.veratad.app/ | 950 The Alameda | San Jose | CA    | 95126   | Password1 | Password1       | What was the name of your first school? | school         | (000)000-0000 | aem-mobile-sensa.json | Registration | Validate Registration Pages |

    Scenario Outline: Validate that errors in Tellus About Page  for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        Then The user clicks on the Join Now
        Then The user validates Errors in Tell us Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user enters First Name as <FirstName> Last Name as <LastName> street address as <wrong streetAddr> Zipcode as <zipCode> City as <city> State as <state> and DOB as "<dateOfBirth>"
        Then The user validates Generic Error in Tell us about Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @Sensa_Registration_ErrorStep1_QA
        Examples:
            | Brand | URL                             | Password  | wrongconfirmPassword | ConfirmPassword | FirstName | LastName       | wrong streetAddr | streetAddr    | zipCode | city     | state | dateOfBirth | filename              | sheetname    | scenarioname                |
            | Sensa | https://aem-stage.sensavape.com | Password1 | Password12           | Password1       | Oliver    | PEDROZZIRJSGFB | 1760 NIAGAR BLVD | 81 W 125TH ST | 10027   | New York | NY    | 02-15-1987  | aem-mobile-sensa.json | Registration | Validate Registration Pages |

    Scenario Outline: Validate the errors in Account Setup Page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        Then The user clicks on the Join Now
        When The user enters First Name as <FirstName> Last Name as <LastName> street address as <streetAddr> Zipcode as <zipCode> City as <city> State as <state> and DOB as "<dateOfBirth>"
        Then The user validates Errors in Account Setup Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user enters Password as <Password> and confirm Password <wrongconfirmPassword> and validates error message <errormessage>

        @Sensa_Registration_ErrorStep2_QA
        Examples:
            | Brand | URL                             | Password  | wrongconfirmPassword | ConfirmPassword | FirstName | LastName       | streetAddr    | zipCode | city     | state | dateOfBirth | Securityquestion                        | Securityanswer | errormessage                                         | filename              | sheetname    | scenarioname                |
            | Sensa | https://aem-stage.sensavape.com | Password1 | Password123          | Password1       | Oliver    | PEDROZZIRJSGFB | 81 W 125TH ST | 10027   | New York | NY    | 02-15-1987  | What was the name of your first school? | school         | Oops, that password doesn't match. Please try again. | aem-mobile-sensa.json | Registration | Validate Registration Pages |

