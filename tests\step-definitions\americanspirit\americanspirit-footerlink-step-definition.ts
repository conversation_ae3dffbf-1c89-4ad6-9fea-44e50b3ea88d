import { Then, When } from '@wdio/cucumber-framework';
import footerlinkPage from '../../pages/commonteps/footerlink.page.ts';
import logger from '../../support/utils/logger.util.ts';
import NasFooterlinkPageObject from '../../page-object/americanspirit/americanspirit-footerlink.pageObject.ts';
import sensaRegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';

Then(/^The user Validates Nas FAQ Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await footerlinkPage.NasFAQpageValidation(filepath, sheetname, scenarioname);
    logger.info('User Validates Camel FAQ Page');
});

When(/^The user clicks on The Spirit Circle footerlink$/, async function () {
    await footerlinkPage.clickonTheSpiritCirclefooterlink();
    const url = await browser.getUrl();
    if ((await url).includes('aem')) {
        await expect(NasFooterlinkPageObject.hdrSpiritCircleProgramTerms_nas).toBeDisplayed();
    } else {
        await expect(NasFooterlinkPageObject.prodhdrSpiritCircleProgramTerms_nas).toBeDisplayed();
    }
    logger.info('User Navigates to The Spirit Circle Page');
});
Then(/^The user Validates The Spirit Circle Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await footerlinkPage.NasTheSpiritCirclepageValidation(filepath, sheetname, scenarioname);
    await expect(sensaRegistrationPageObject.lblsignIn_sensa).toBeDisplayed();
    logger.info('User Validates The Spirit Circle Page');
});
When(/^The user clicks on post login FAQ footerlink for Nas$/, async function () {
    await footerlinkPage.clickonfaqpostfooterlink();
    await expect(NasFooterlinkPageObject.postcontactUsHeader).toBeDisplayed();
    await logger.info('Navigated to FAQ Page Successfully');
});