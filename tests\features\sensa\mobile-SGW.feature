Feature: SGW Message Validation for Sensa Website

    Scenario Outline: Validate SGW Message in all Pages for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        Then The user Validates SGW Message with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user login with valid user id <Username> and password <Password>
        Then The user Validates SGW Message with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        And The user click on Flavours link
        Then The user Validates SGW Message with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user clicks on Offers Link
        Then The user Validates SGW Message with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user click on Store Locator from Hamburger menu
        Then The user Validates SGW Message with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user clicks on Text Signup link
        Then The user Validates SGW Message with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user clicks on My device link
        Then The user Validates SGW Message with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        And The user click on MyAccount link
        Then The user Validates SGW Message with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user validates that successfully logged out

        @SensaLSGW_Validation_QA
        Examples:
            | Brand | URL                             | Username                           | Password  | filename              | sheetname | scenarioname         |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | SGW       | Validate SGW Message |

        @SensaLSGW_Validation_PROD
        Examples:
            | Brand | URL                       | Username                                 | Password  | filename              | sheetname | scenarioname         |
            | Sensa | https://www.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | SGW       | Validate SGW Message |
