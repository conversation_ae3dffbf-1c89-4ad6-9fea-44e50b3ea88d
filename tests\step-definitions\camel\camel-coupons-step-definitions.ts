import { Then, When } from '@wdio/cucumber-framework';
import camelCouponsPage from '../../pages/camel/camel-coupons.page.ts';
import sensaOffersPageObject from '../../page-object/sensa/sensa-offers.pageObject.ts';
import logger from '../../support/utils/logger.util.ts';
import camelCouponsPageObject from '../../page-object/camel/camel-coupons.pageObject.ts';

When(/^The user clicks on Coupons Link$/, async function () {
  await camelCouponsPage.couponsPage();
  await expect(sensaOffersPageObject.txtoffertitle_sensa).toBeDisplayed();
  logger.info('Navigated to Coupons Page Successfully');
});

Then(/^The user validates coupons page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
  await camelCouponsPage.couponsPageValidation(filepath, sheetname, scenarioname);
  await expect(sensaOffersPageObject.txtoffertitle_sensa).toBeDisplayed();
  logger.info('User validates coupons page');
});

When(/^The user click on Redeem now button$/, async function () {
  await camelCouponsPage.redeemoffersbutton();
  await expect(camelCouponsPageObject.btnRedeem_camel).toBeDisplayed();
  logger.info('Navigated to SPA Page Successfully');
});