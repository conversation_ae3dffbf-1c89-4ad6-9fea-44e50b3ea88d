import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import sensaFlavorPagObject from '../../page-object/sensa/sensa-flavor.pagObject.ts';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import path from 'path';
import sensaAccountPage from '../commonteps/account.page.ts';
import sensaHomepagePage from './sensa-homepage.page.ts';


class FlavorPage {

    public async validateflavorPage(filename: string, sheetname: string, scenarioname: string) {
        try {
                const SHEET_NAME = sheetname;
                const jsonFilePath = path.join(process.cwd(), 'data', filename);
                const testData = new JsonTestDataHandler(jsonFilePath);
                const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
                console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);

                const zwroedflavor = testData.getCellValue(SHEET_NAME, scenarioname, 'zwroedflavor');
                const zwroedflavorDesc = testData.getCellValue(SHEET_NAME, scenarioname, 'zwroedflavorDesc');
                const excellenttaste = testData.getCellValue(SHEET_NAME, scenarioname, 'excellenttaste');
                const excellenttasteDes = testData.getCellValue(SHEET_NAME, scenarioname, 'excellenttasteDes');
                const darkandripe = testData.getCellValue(SHEET_NAME, scenarioname, 'darkandripe');
                const perfectmatch1 = testData.getCellValue(SHEET_NAME, scenarioname, 'perfectmatch1');
                const Berryfusion = testData.getCellValue(SHEET_NAME, scenarioname, 'Berryfusion');
                const BerryfusionDes = testData.getCellValue(SHEET_NAME, scenarioname, 'BerryfusionDes');
                const ripandfresh = testData.getCellValue(SHEET_NAME, scenarioname, 'ripandfresh');
                const Berrywatermelonfusion = testData.getCellValue(SHEET_NAME, scenarioname, 'Berrywatermelonfusion');
                const BerrywatermelonfusionDes = testData.getCellValue(SHEET_NAME, scenarioname, 'BerrywatermelonfusionDes');
                const freshandJuicy = testData.getCellValue(SHEET_NAME, scenarioname, 'freshandJuicy');

                const watermelonfrost = testData.getCellValue(SHEET_NAME, scenarioname, 'watermelonfrost');
                const watermelonfrostDes = testData.getCellValue(SHEET_NAME, scenarioname, 'watermelonfrostDes');
                const JuicyandBright = testData.getCellValue(SHEET_NAME, scenarioname, 'JuicyandBright');
                const passionfruitfrost = testData.getCellValue(SHEET_NAME, scenarioname, 'passionfruitfrost');
                const passionfruitfrostDes = testData.getCellValue(SHEET_NAME, scenarioname, 'passionfruitfrostDes');
                const BrightandFrosted = testData.getCellValue(SHEET_NAME, scenarioname, 'BrightandFrosted');
                const BlueberryFrost = testData.getCellValue(SHEET_NAME, scenarioname, 'BlueberryFrost');
                const BlueberryFrostDes = testData.getCellValue(SHEET_NAME, scenarioname, 'BlueberryFrostDes');
                const FrostedandRefreshing = testData.getCellValue(SHEET_NAME, scenarioname, 'FrostedandRefreshing');
                const MintFrost = testData.getCellValue(SHEET_NAME, scenarioname, 'MintFrost');
                const MintFrostDes = testData.getCellValue(SHEET_NAME, scenarioname, 'MintFrostDes');
                const Fullyloaded = testData.getCellValue(SHEET_NAME, scenarioname, 'Fullyloaded');

                const FullyloadedDes = testData.getCellValue(SHEET_NAME, scenarioname, 'FullyloadedDes');
                const Fusionseries1 = testData.getCellValue(SHEET_NAME, scenarioname, 'Fusionseries1');
                const Berry = testData.getCellValue(SHEET_NAME, scenarioname, 'Berry');
                const BerryFusiontDes = testData.getCellValue(SHEET_NAME, scenarioname, 'BerryFusiontDes');
                const Berrywatermelonfusion1 = testData.getCellValue(SHEET_NAME, scenarioname, 'Berrywatermelonfusion1');
                const Berrywatermelonfusion1Des = testData.getCellValue(SHEET_NAME, scenarioname, 'Berrywatermelonfusion1Des');
                const FrostSeries1 = testData.getCellValue(SHEET_NAME, scenarioname, 'FrostSeries1');
                const Watermelon = testData.getCellValue(SHEET_NAME, scenarioname, 'Watermelon');
                const WatermelonFrost1Des = testData.getCellValue(SHEET_NAME, scenarioname, 'WatermelonFrost1Des');
                const MintFrost1 = testData.getCellValue(SHEET_NAME, scenarioname, 'MintFrost1');
                const MintFrost1Des = testData.getCellValue(SHEET_NAME, scenarioname, 'MintFrost1Des');
                const BlueberryFrost1 = testData.getCellValue(SHEET_NAME, scenarioname, 'BlueberryFrost1');

                const BlueberryFrost1Des = testData.getCellValue(SHEET_NAME, scenarioname, 'BlueberryFrost1Des');
                const PassionfruitFrost1 = testData.getCellValue(SHEET_NAME, scenarioname, 'PassionfruitFrost1');
                const PassionfruitFrost1Des = testData.getCellValue(SHEET_NAME, scenarioname, 'PassionfruitFrost1Des');
                const Monthlyoffers = testData.getCellValue(SHEET_NAME, scenarioname, 'Monthlyoffers');
                const MonthlyoffersDes = testData.getCellValue(SHEET_NAME, scenarioname, 'MonthlyoffersDes');
                const ZeroNicotine = testData.getCellValue(SHEET_NAME, scenarioname, 'ZeroNicotine');

                await elementActions.assertion(sensaFlavorPagObject.zwroedflavor);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.zwroedflavor, zwroedflavor);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.zwroedflavorDesc, zwroedflavorDesc);
                await elementActions.assertion(sensaFlavorPagObject.Flavorimage);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.excellenttaste, excellenttaste);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.excellenttasteDes, excellenttasteDes);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.darkandripe, darkandripe);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.perfectmatch1, perfectmatch1);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.Berryfusion, Berryfusion);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.BerryfusionDes, BerryfusionDes);
                await elementActions.assertion(sensaFlavorPagObject.grabcoupon1);
                await sensaHomepagePage.clickOnEachButtonAndNaviagteBack(sensaFlavorPagObject.grabcoupon1, sensaFlavorPagObject.offers);
                await elementActions.assertion(sensaFlavorPagObject.BerryfusionImage);

                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.Ripandfresh, ripandfresh);
                await elementActions.clickusingJavascript(sensaFlavorPagObject.Ripandfresh);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.perfectmatch2, perfectmatch1);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.Berrywatermelonfusion, Berrywatermelonfusion);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.BerrywatermelonfusionDes, BerrywatermelonfusionDes);
                await elementActions.assertion(sensaFlavorPagObject.grabcoupon2);
                await sensaHomepagePage.clickOnEachButtonAndNaviagteBack(sensaFlavorPagObject.grabcoupon2, sensaFlavorPagObject.offers);
                await elementActions.assertion(sensaFlavorPagObject.BerrywatermelonfusionImage);

                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.freshandJuicy, freshandJuicy);
                await elementActions.clickusingJavascript(sensaFlavorPagObject.freshandJuicy);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.perfectmatch3, perfectmatch1);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.watermelonfrost, watermelonfrost);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.watermelonfrostDes, watermelonfrostDes);
                await elementActions.assertion(sensaFlavorPagObject.grabcoupon3);
                await sensaHomepagePage.clickOnEachButtonAndNaviagteBack(sensaFlavorPagObject.grabcoupon3, sensaFlavorPagObject.offers);
                await elementActions.assertion(sensaFlavorPagObject.watermelonfrostImage);

                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.JuicyandBright, JuicyandBright);
                await elementActions.clickusingJavascript(sensaFlavorPagObject.JuicyandBright);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.perfectmatch4, perfectmatch1);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.passionfruitfrost, passionfruitfrost);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.passionfruitfrostDes, passionfruitfrostDes);
                await elementActions.assertion(sensaFlavorPagObject.grabcoupon4);
                await sensaHomepagePage.clickOnEachButtonAndNaviagteBack(sensaFlavorPagObject.grabcoupon4, sensaFlavorPagObject.offers);
                await elementActions.assertion(sensaFlavorPagObject.passionfruitfrostImage);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.BrightandFrosted, BrightandFrosted);
                await elementActions.click(await sensaFlavorPagObject.BrightandFrosted);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.perfectmatch5, perfectmatch1);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.BlueberryFrost, BlueberryFrost);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.BlueberryFrostDes, BlueberryFrostDes);
                await elementActions.assertion(sensaFlavorPagObject.grabcoupon5);
                await sensaHomepagePage.clickOnEachButtonAndNaviagteBack(sensaFlavorPagObject.grabcoupon5, sensaFlavorPagObject.offers);
                await elementActions.assertion(sensaFlavorPagObject.BlueberryFrostImage);

                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.FrostedandRefreshing, FrostedandRefreshing);
                await elementActions.click(sensaFlavorPagObject.FrostedandRefreshing);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.perfectmatch6, perfectmatch1);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.MintFrost, MintFrost);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.MintFrostDes, MintFrostDes);
                await elementActions.assertion(sensaFlavorPagObject.grabcoupon6);
                await sensaHomepagePage.clickOnEachButtonAndNaviagteBack(sensaFlavorPagObject.grabcoupon6, sensaFlavorPagObject.offers);
                await elementActions.assertion(sensaFlavorPagObject.MintFrostImage);

                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.Fullyloaded, Fullyloaded);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.FullyloadedDes, FullyloadedDes);

                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.Fusionseries1, Fusionseries1);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.Berry, Berry);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.BerryFusiontDes, BerryFusiontDes);
                await elementActions.assertion(sensaFlavorPagObject.BerryFusiontImg);

                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.Fusionseries2, Fusionseries1);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.Berrywatermelonfusion1, Berrywatermelonfusion1);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.Berrywatermelonfusion1Des, Berrywatermelonfusion1Des);
                await elementActions.assertion(sensaFlavorPagObject.Berrywatermelonfusion1Img);

                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.FrostSeries1, FrostSeries1);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.Watermelon, Watermelon);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.WatermelonFrost1Des, WatermelonFrost1Des);
                await elementActions.assertion(sensaFlavorPagObject.WatermelonFrost1Img);

                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.FrostSeries2, FrostSeries1);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.MintFrost1, MintFrost1);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.MintFrost1Des, MintFrost1Des);
                await elementActions.assertion(sensaFlavorPagObject.MintFrost1Img);

                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.FrostSeries3, FrostSeries1);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.BlueberryFrost1, BlueberryFrost1);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.BlueberryFrost1Des, BlueberryFrost1Des);
                await elementActions.assertion(sensaFlavorPagObject.BlueberryFrost1Img);

                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.FrostSeries4, FrostSeries1);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.PassionfruitFrost1, PassionfruitFrost1);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.PassionfruitFrost1Des, PassionfruitFrost1Des);
                await elementActions.assertion(sensaFlavorPagObject.PassionfruitFrost1Img);

                await elementActions.assertion(sensaFlavorPagObject.Findastore);
                await sensaHomepagePage.clickOnEachButtonAndNaviagteBack(sensaFlavorPagObject.Findastore, sensaFlavorPagObject.Findastore1);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.Monthlyoffers, Monthlyoffers);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.MonthlyoffersDes, MonthlyoffersDes);
                await elementActions.assertion(sensaFlavorPagObject.Claimoffer);
                await sensaHomepagePage.clickOnEachButtonAndNaviagteBack(sensaFlavorPagObject.Claimoffer, sensaFlavorPagObject.offers);
                await sensaAccountPage.mssgcomparision(sensaFlavorPagObject.ZeroNicotine, ZeroNicotine);

                console.log('Validated the Flavor page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Flavor page Successfully', { error });
            throw error;
        }

    }

}
export default new FlavorPage();