Feature: Coupons Page Validation for Camel Website

    Scenario Outline: Updating Coupons Page Details for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        When The user clicks on Coupons Link
        Then The user validates coupons page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user click on Redeem now button
        Then The user validates that successfully logged out of camel brand

        @CamelCoupons_Update_QA
        Examples:
            | Brand | URL                         | Username                           | Password  | filename              | sheetname | scenarioname          |
            | camel | https://aem-stage.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Coupons   | Validate Coupons Page |

        @CamelCoupons_Update_PROD
        Examples:
            | Brand | URL                   | Username                             | Password  | filename              | sheetname | scenarioname          |
            | camel | https://www.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Coupons   | Validate Coupons Page |
