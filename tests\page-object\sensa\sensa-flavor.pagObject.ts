class FlavorPageObject {


    get Flavor() { return $('a[title="Flavors"]'); }
    get zwroedflavor() { return $('//*[contains(text(),"Zeroed in")]'); }
    get zwroedflavorDesc() { return $('//*[contains(text(),"<PERSON><PERSON>’s specialty is exper")]'); }
    get Flavorimage() { return $('[alt="Sensa flavor lineup"]'); }
    get excellenttaste() { return $('[class="cmp-title__text"]'); }
    get excellenttasteDes() { return $('//*[contains(text(),"Explore the full")]'); }
    get darkandripe() { return $('//*[contains(text(),"Dark & Ripe")]'); }
    get perfectmatch1() { return $('(//*[contains(text(),"Your perfect match is…")])[1]'); }
    get perfectmatch2() { return $('(//*[contains(text(),"Your perfect match is…")])[2]'); }
    get perfectmatch3() { return $('(//*[contains(text(),"Your perfect match is…")])[3]'); }
    get perfectmatch4() { return $('(//*[contains(text(),"Your perfect match is…")])[4]'); }
    get perfectmatch5() { return $('(//*[contains(text(),"Your perfect match is…")])[5]'); }
    get perfectmatch6() { return $('(//*[contains(text(),"Your perfect match is…")])[6]'); }
    get grabcoupon1() { return $('(//*[contains(text(),"Grab coupon")])[1]'); }
    get grabcoupon2() { return $('(//*[contains(text(),"Grab coupon")])[2]'); }
    get grabcoupon3() { return $('(//*[contains(text(),"Grab coupon")])[3]'); }
    get grabcoupon4() { return $('(//*[contains(text(),"Grab coupon")])[4]'); }
    get grabcoupon5() { return $('(//*[contains(text(),"Grab coupon")])[5]'); }
    get grabcoupon6() { return $('(//*[contains(text(),"Grab coupon")])[6]'); }
    get Berryfusion() { return $('//*[contains(text(),"Berry Fusion")]'); }
    get BerryfusionDes() { return $('//*[contains(text(),"Berry Fusion")]//following::p[1]'); }
    get BerryfusionImage() { return $('[src="/content/dam/sensa/website/product_page/refresh/vtab/Sensa_Flavor-VCarousel_01-BerryFusion_960X880_2x_desktop.png"]'); }
    get Ripandfresh() { return $('//*[contains(text(),"Ripe & Fresh")]'); }

    get Berrywatermelonfusion() { return $('(//*[contains(text(),"Berry Watermelon Fusion")])[1]'); }
    get BerrywatermelonfusionDes() { return $('(//*[contains(text(),"Berry Watermelon Fusion")])[1]//following::p[1]'); }
    get BerrywatermelonfusionImage() { return $('[src="/content/dam/sensa/website/product_page/refresh/vtab/Sensa_Flavor-VCarousel_02-BerryWatermelonFusion_960X880_2x_desktop.png"]'); }

    get freshandJuicy() { return $('(//*[contains(text(),"Fresh & Juicy ")])'); }
    get watermelonfrost() { return $('(//*[contains(text(),"Watermelon Frost")])'); }
    get watermelonfrostDes() { return $('(//*[contains(text(),"Watermelon Frost")])//following::p[1]'); }
    get watermelonfrostImage() { return $('[src="/content/dam/sensa/website/product_page/refresh/vtab/Sensa_Flavor-VCarousel_03-WatermelonFrost_960X880_2x_desktop.png"]'); }

    get JuicyandBright() { return $('(//*[contains(text(),"Juicy & Bright")])[1]'); }
    get passionfruitfrost() { return $('(//*[contains(text(),"Passionfruit Frost")])'); }
    get passionfruitfrostDes() { return $('(//*[contains(text(),"Passionfruit Frost")])//following::p[1]'); }
    get passionfruitfrostImage() { return $('[src="/content/dam/sensa/website/product_page/refresh/vtab/Sensa_Flavor-VCarousel_06-PassionfruitFrost_960X880_2x_desktop.png"]'); }

    get BrightandFrosted() { return $('(//*[contains(text(),"Bright & Frosted")])[1]'); }
    get BlueberryFrost() { return $('(//*[contains(text(),"Blueberry Frost")])'); }
    get BlueberryFrostDes() { return $('(//*[contains(text(),"Blueberry Frost")])//following::p[1]'); }
    get BlueberryFrostImage() { return $('[src="/content/dam/sensa/website/product_page/refresh/vtab/Sensa_Flavor-VCarousel_05-BlueberryFrost_960X880_2x_desktop.png"]'); }

    get FrostedandRefreshing() { return $('(//*[contains(text(),"Frosted & Refreshing")])[1]'); }
    get MintFrost() { return $('(//*[contains(text(),"Mint Frost")])'); }
    get MintFrostDes() { return $('(//*[contains(text(),"Mint Frost")])//following::p[1]'); }
    get MintFrostImage() { return $('[src="/content/dam/sensa/website/product_page/refresh/vtab/Sensa_Flavor-VCarousel_04-MintFrost_960X880_2x_desktop.png"]'); }
    get Fullyloaded() { return $('(//*[contains(text(),"Fully")])[2]'); }
    get FullyloadedDes() { return $('(//*[contains(text(),"With Sensa, zero nicoti")])'); }

    get Fusionseries1() { return $('(//*[contains(text(),"Fusion Series ")])[1]'); }
    get Berry() { return $('(//*[contains(text(),"Berry")])[3]'); }
    get BerryFusiontDes() { return $('(//*[contains(text()," A rich, dark blend of strawb")])'); }
    get BerryFusiontImg() { return $('[src="/content/dam/sensa/website/product_page/refresh/Sensa_Flavor-01-BerryFusion_764X960_2x_desktop.png"]'); }

    get Fusionseries2() { return $('(//*[contains(text(),"Fusion Series ")])[2]'); }
    get Berrywatermelonfusion1() { return $('(//*[contains(text(),"Berry Watermelon Fusion")])[2]'); }
    get Berrywatermelonfusion1Des() { return $('(//*[contains(text()," The flavor of ripe red berries")])'); }
    get Berrywatermelonfusion1Img() { return $('[src="/content/dam/sensa/website/product_page/refresh/Sensa_Flavor-02_BerryWatermelonFusion_764X960_2x_desktop.png"]'); }

    get FrostSeries1() { return $('(//*[contains(text(),"Frost Series ")])[1]'); }
    get Watermelon() { return $('(//*[contains(text(),"Watermelon")])[4]'); }
    get WatermelonFrost1Des() { return $('(//*[contains(text()," Ripe, juicy wat")])'); }
    get WatermelonFrost1Img() { return $('[src="/content/dam/sensa/website/product_page/refresh/Sensa_Flavor-03_WatermelonFrost_764X960_2x_desktop.png"]'); }

    get FrostSeries2() { return $('(//*[contains(text(),"Frost Series ")])[2]'); }
    get MintFrost1() { return $('(//*[contains(text(),"Mint")])[2]'); }
    get MintFrost1Des() { return $('(//*[contains(text()," Bold peppermint flavor with")])'); }
    get MintFrost1Img() { return $('[src="/content/dam/sensa/website/product_page/refresh/Sensa_Flavor-04_MintFrost_764X960_2x_desktop.png"]'); }

    get FrostSeries3() { return $('(//*[contains(text(),"Frost Series ")])[3]'); }
    get BlueberryFrost1() { return $('(//*[contains(text(),"Blueberry")])[2]'); }
    get BlueberryFrost1Des() { return $('(//*[contains(text()," A bright burst of blueberry")])'); }
    get BlueberryFrost1Img() { return $('[src="/content/dam/sensa/website/product_page/refresh/Sensa_Flavor-05_BlueberryFrost_764X960_2x_desktop.png"]'); }

    get FrostSeries4() { return $('(//*[contains(text(),"Frost Series ")])[4]'); }
    get PassionfruitFrost1() { return $('(//*[contains(text(),"Passionfruit")])[2]'); }
    get PassionfruitFrost1Des() { return $('(//*[contains(text()," Zesty, tropical passionfruit")])'); }
    get PassionfruitFrost1Img() { return $('[src="/content/dam/sensa/website/product_page/refresh/Sensa_Flavor-06_PassionFrost_764X960_2x_desktop.png"]'); }
    get Findastore() { return $('(//*[contains(text(),"Find a Store")])'); }
    get Findastore1() { return $('(//*[contains(text(),"Find A Store")])'); }
    get Monthlyoffers() { return $('(//*[contains(text(),"Monthly Offers")])'); }
    get MonthlyoffersDes() { return $('(//*[contains(text(),"Get exclusive monthly offers de")])'); }
    get Claimoffer() { return $('(//*[contains(text(),"Claim Offer")])'); }
    get ZeroNicotine() { return $('(//*[contains(text(),"All flavor. Zero nicotine*.")])[2]'); }
    get offers() { return $('//*[contains(text(),"offers available for you")]'); }

}
export default new FlavorPageObject();