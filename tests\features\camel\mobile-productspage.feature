Feature: Products Page Validation for Camel Website

  Scenario Outline: Validation of Products page for <Brand>
    Given The user is on the login page for <Brand> with login <URL>
    When The user login with valid user id <Username> and password <Password>
    When The user clicks on Products
    Then The user validates Products Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
    Then The user validates that successfully logged out of camel brand

    @CamelProductsPage_Validation_QA
    Examples:
      | Brand | URL                         | Username                           | Password  | filename              | sheetname | scenarioname           |
      | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Products  | Validate Products Page |

    @CamelProductsPage_Validation_PROD
    Examples:
      | Brand | URL                   | Username                             | Password  | filename              | sheetname | scenarioname           |
      | Camel | https://www.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Products  | Validate Products Page |

  Scenario Outline: Validation of Crush Products page for <Brand>
    Given The user is on the login page for <Brand> with login <URL>
    When The user login with valid user id <Username> and password <Password>
    When The user clicks on Crush link from products page
    Then The user validates Crush Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
    Then The user validates that successfully logged out of camel brand

    @CamelCrushProductsPage_Validation_QA 
    Examples:
      | Brand | URL                         | Username                           | Password  | filename              | sheetname | scenarioname           |
      | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Products  | Validate Products Page |

    @CamelCrushProductsPage_Validation_PROD
    Examples:
      | Brand | URL                   | Username                             | Password  | filename              | sheetname | scenarioname           |
      | Camel | https://www.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Products  | Validate Products Page |

  Scenario Outline: Validation of Classic Products page for <Brand>
    Given The user is on the login page for <Brand> with login <URL>
    When The user login with valid user id <Username> and password <Password>
    When The user clicks on Classic link from products page
    Then The user validates Classic Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
    Then The user validates that successfully logged out of camel brand

    @CamelClassicProductsPage_Validation_QA
    Examples:
      | Brand | URL                         | Username                           | Password  | filename              | sheetname | scenarioname           |
      | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Products  | Validate Products Page |

    @CamelClassicProductsPage_Validation_PROD
    Examples:
      | Brand | URL                   | Username                             | Password  | filename              | sheetname | scenarioname           |
      | Camel | https://www.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Products  | Validate Products Page |

  Scenario Outline: Validation of Turkish Products page for <Brand>
    Given The user is on the login page for <Brand> with login <URL>
    When The user login with valid user id <Username> and password <Password>
    When The user clicks on Turkish link from products page
    Then The user validates Turkish Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
    Then The user validates that successfully logged out of camel brand

    @CamelTurkishProductsPage_Validation_QA 
    Examples:
      | Brand | URL                         | Username                           | Password  | filename              | sheetname | scenarioname           |
      | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Products  | Validate Products Page |

    @CamelTurkishProductsPage_Validation_PROD
    Examples:
      | Brand | URL                   | Username                             | Password  | filename              | sheetname | scenarioname           |
      | Camel | https://www.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Products  | Validate Products Page |

  Scenario Outline: Validation of No9 Products page for <Brand>
    Given The user is on the login page for <Brand> with login <URL>
    When The user login with valid user id <Username> and password <Password>
    When The user clicks on No9 link from products page
    Then The user validates No9 Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
    Then The user validates that successfully logged out of camel brand

    @CamelNo9ProductsPage_Validation_QA
    Examples:
      | Brand | URL                         | Username                           | Password  | filename              | sheetname | scenarioname           |
      | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Products  | Validate Products Page |

    @CamelNo9ProductsPage_Validation_PROD
    Examples:
      | Brand | URL                   | Username                             | Password  | filename              | sheetname | scenarioname           |
      | Camel | https://www.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Products  | Validate Products Page |

  Scenario Outline: Validation of Red Kamel Products page for <Brand>
    Given The user is on the login page for <Brand> with login <URL>
    When The user login with valid user id <Username> and password <Password>
    When The user clicks on Red Kamel link from products page
    Then The user validates Red Kamel Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
    Then The user validates that successfully logged out of camel brand

    @CamelRedKamelProductsPage_Validation_QA
    Examples:
      | Brand | URL                         | Username                           | Password  | filename              | sheetname | scenarioname           |
      | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Products  | Validate Products Page |

    @CamelRedKamelProductsPage_Validation_PROD
    Examples:
      | Brand | URL                   | Username                             | Password  | filename              | sheetname | scenarioname           |
      | Camel | https://www.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Products  | Validate Products Page |