/**
 * Custom Cucumber Reporter for WebDriverIO
 * Displays individual scenario names and results in the console output
 */

import { EventEmitter } from 'events';

interface TestResult {
  state: 'passed' | 'failed' | 'skipped';
  title: string;
  fullTitle: string;
  duration: number;
  error?: Error;
  retries?: number;
}

interface SuiteResult {
  title: string;
  tests: TestResult[];
  suites: SuiteResult[];
}

interface ReporterOptions {
  outputDir?: string;
  filename?: string;
  [key: string]: unknown;
}

interface TestEvent {
  title?: string;
  fullTitle?: string;
  duration?: number;
  error?: Error;
  _retries?: number;
}

interface SuiteEvent {
  title?: string;
  file?: string;
}

export default class CustomCucumberReporter extends EventEmitter {
  private results: Map<string, SuiteResult> = new Map();
  private currentSuite: string = '';

  constructor(_options: ReporterOptions) {
    super();

    // Suite and test events
    this.on('suite:start', this.onSuiteStart.bind(this));
    this.on('suite:end', this.onSuiteEnd.bind(this));

    // Test events
    this.on('test:start', this.onTestStart.bind(this));
    this.on('test:pass', this.onTestPass.bind(this));
    this.on('test:fail', this.onTestFail.bind(this));
    this.on('test:skip', this.onTestSkip.bind(this));
    this.on('test:retry', this.onTestRetry.bind(this));

    // Runner events
    this.on('runner:end', this.onRunnerEnd.bind(this));
  }

  onSuiteStart(suite: SuiteEvent) {
    this.currentSuite = suite.title || suite.file || 'Unknown Suite';
    
    if (!this.results.has(this.currentSuite)) {
      this.results.set(this.currentSuite, {
        title: this.currentSuite,
        tests: [],
        suites: [],
      });
    }
  }

  onTestStart(test: TestEvent) {
    // Enhanced test start logging with feature and scenario information
    const featureName = this.currentSuite;
    const scenarioName = test.title || test.fullTitle || 'Unknown Test';

    console.log('\n' + '='.repeat(80));
    console.log(`🎬 FEATURE: ${featureName}`);
    console.log(`🎯 SCENARIO: ${scenarioName}`);
    console.log('='.repeat(80));
    console.log('🚀 Starting scenario execution...');
  }

  onTestPass(test: TestEvent) {
    const testResult: TestResult = {
      state: 'passed',
      title: test.title || 'Unknown Test',
      fullTitle: test.fullTitle || test.title || 'Unknown Test',
      duration: test.duration || 0,
      retries: test._retries || 0,
    };

    this.addTestResult(testResult);

    console.log('\n' + '✅'.repeat(40));
    console.log(`✅ SCENARIO PASSED: ${testResult.fullTitle}`);
    console.log(`⏱️  Duration: ${testResult.duration ? `${testResult.duration}ms` : 'N/A'}`);
    if (testResult.retries && testResult.retries > 0) {
      console.log(`🔄 Retries: ${testResult.retries}`);
    }
    console.log('✅'.repeat(40) + '\n');
  }

  onTestFail(test: TestEvent) {
    const testResult: TestResult = {
      state: 'failed',
      title: test.title || 'Unknown Test',
      fullTitle: test.fullTitle || test.title || 'Unknown Test',
      duration: test.duration || 0,
      error: test.error,
      retries: test._retries || 0,
    };

    this.addTestResult(testResult);

    console.log('\n' + '❌'.repeat(40));
    console.log(`❌ SCENARIO FAILED: ${testResult.fullTitle}`);
    console.log(`⏱️  Duration: ${testResult.duration ? `${testResult.duration}ms` : 'N/A'}`);
    if (testResult.retries && testResult.retries > 0) {
      console.log(`🔄 Retries: ${testResult.retries}`);
    }
    if (testResult.error) {
      console.log(`💥 Error: ${testResult.error.message}`);
      if (testResult.error.stack) {
        console.log(`📍 Stack: ${testResult.error.stack.split('\n')[0]}`);
      }
    }
    console.log('❌'.repeat(40) + '\n');
  }

  onTestSkip(test: TestEvent) {
    const testResult: TestResult = {
      state: 'skipped',
      title: test.title || 'Unknown Test',
      fullTitle: test.fullTitle || test.title || 'Unknown Test',
      duration: test.duration || 0,
    };

    this.addTestResult(testResult);
    console.log(`⏭️  SKIPPED: ${testResult.fullTitle}`);
  }

  onTestRetry(test: TestEvent) {
    console.log(`🔄 RETRY: ${test.title || test.fullTitle || 'Unknown Test'} (Attempt ${(test._retries || 0) + 1})`);
  }

  onSuiteEnd(_suite: SuiteEvent) {
    // Suite ended - could add summary here if needed
  }

  onRunnerEnd() {
    this.printSummary();
  }

  private addTestResult(testResult: TestResult) {
    const suite = this.results.get(this.currentSuite);
    if (suite) {
      suite.tests.push(testResult);
    }
  }

  private printSummary() {
    console.log('\n' + '='.repeat(80));
    console.log('📊 TEST EXECUTION SUMMARY');
    console.log('='.repeat(80));

    let totalTests = 0;
    let passedTests = 0;
    let failedTests = 0;
    let skippedTests = 0;

    for (const [suiteName, suite] of this.results.entries()) {
      if (suite.tests.length > 0) {
        console.log(`\n📁 ${suiteName}:`);
        
        suite.tests.forEach(test => {
          const icon = test.state === 'passed' ? '✅' : test.state === 'failed' ? '❌' : '⏭️';
          const retryInfo = test.retries && test.retries > 0 ? ` (${test.retries} retries)` : '';
          console.log(`   ${icon} ${test.fullTitle}${retryInfo}`);
          
          totalTests++;
          if (test.state === 'passed') passedTests++;
          else if (test.state === 'failed') failedTests++;
          else skippedTests++;
        });
      }
    }

    console.log('\n' + '-'.repeat(80));
    console.log(`📈 RESULTS: ${totalTests} total | ${passedTests} passed | ${failedTests} failed | ${skippedTests} skipped`);
    console.log('='.repeat(80) + '\n');
  }
}
