class FooterlinkPageObject {

  // Contact Us Footerlinks
  get hdrfaq_ContactUs_sensa() { return $('//*[text()="CONTACT US"]'); }
  get lblfurtherassistance() { return $('//p[contains(text(),"Click here for ")]//parent::div'); }
  get lblfrequentQuestions() { return $('//h2[normalize-space()="FREQUENTLY ASKED QUESTIONS"]'); }
  get lblonthisPage_sensa() { return $('//p[contains(text(),"On this page:")]//parent::div'); }


  // FAQ Footerlinks
  get hdrfaq_warningInstructions_sensa() { return $('//h3[normalize-space()="WARNINGS & INSTRUCTIONS"]'); }
  get lblwarningQ1_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[1]'); }
  get lblwarningQ2_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[2]'); }
  get lblwarningQ3_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[3]'); }
  get lblwarningQ4_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[4]'); }
  get lblwarningQ5_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[5]'); }
  get lblwarningQ6_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[6]'); }
  get lblwarningQ7_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[7]'); }
  get lblwarningQ8_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[8]'); }
  get lblwarningQ9_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[9]'); }
  get lblwarningQ10_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[10]'); }
  get lblwarningQ11_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[11]'); }
  get lblwarningA1_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[1]'); }
  get lblwarningA2_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[2]'); }
  get lblwarningA3_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[3]'); }
  get lblwarningA4_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[4]'); }
  get lblwarningA5_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[5]'); }
  get lblwarningA6_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[6]'); }
  get lblwarningA7_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[7]'); }
  get lblwarningA8_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[8]'); }
  get lblwarningA82_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//ol)'); }
  get lblwarningA91_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[9]'); }
  get lblwarningA92_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[10]'); }
  get lblwarningA93_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[11]'); }
  get lblwarningA94_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[12]'); }
  get lblwarningA95_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[13]'); }
  get lblwarningA96_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[14]'); }
  get lblwarningA10_1_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[15]'); }
  get lblwarningA10_2_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[16]'); }
  get lblwarningA10_3_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[17]'); }
  get lblwarningA10_4_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[18]'); }
  get lblwarningA11_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[19]'); }
  get lblwarningA11_2_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//ul)[1]'); }
  get lblwarningA11_3_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//ul)[2]'); }

  get hdrProduct_sensa() { return $('//h3[normalize-space()="PRODUCT INFORMATION"]'); }
  get lblProductQ1_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[12]'); }
  get lblproductQ2_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[13]'); }
  get lblproductQ3_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[14]'); }
  get lblproductQ3plusicon_sensa() { return $('(//*[@class="cmp-accordion-item__icon-plus"])[14]'); }
  get lblproductQ4_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[15]'); }
  get lblproductQ5_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[16]'); }
  get lblproductQ6_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[17]'); }
  get lblproductQ7_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[18]'); }
  get lblproductQ8_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[19]'); }
  get lblproductQ9_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[20]'); }
  get lblproductQ10_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[21]'); }
  get lblproductQ11_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[22]'); }
  get lblproductQ12_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[23]'); }
  get lblproductQ13_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[24]'); }
  get lblproductQ14_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[25]'); }
  get lblproductQ15_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[26]'); }
  get lblproductQ16_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[27]'); }
  get lblproductQ17_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[28]'); }
  get lblproductQ18_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[29]'); }
  get lblproductQ19_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[30]'); }
  get lblproductQ20_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[31]'); }
  get lblproductQ21_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[32]'); }
  get lblproductQ22_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[33]'); }
  get lblproductQ23_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[34]'); }
  get lblproductQ24_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[35]'); }
  get lblproductQ25_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[36]'); }
  get lblproductQ26_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[37]'); }
  get lblproductQ27_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[38]'); }
  get lblproductQ28_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[39]'); }
  get lblproductA1_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[20]'); }
  get lblproductA2_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[21]'); }
  get lblproductA3_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[22]'); }
  get lblproductA4_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[23]'); }
  get lblproductA5_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[24]'); }
  get lblproductA6_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[25]'); }
  get lblproductA7_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[26]'); }
  get lblproductA8_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[27]'); }
  get lblproductA9_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[28]'); }
  get lblproductA10_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[29]'); }
  get lblproductA11_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[30]'); }
  get lblproductA12_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[31]'); }
  get lblproductA13_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[32]'); }
  get lblproductA14_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[33]'); }
  get lblproductA15_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[34]'); }
  get lblproductA16_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[35]'); }
  get lblproductA17_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[36]'); }
  get lblproductA18_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[37]'); }
  get lblproductA19_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[38]'); }
  get lblproductA20_1_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[39]'); }
  get lblproductA20_2_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[40]'); }
  get lblproductA21_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[41]'); }
  get lblproductA22_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[42]'); }
  get lblproductA23_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[43]'); }
  get lblproductA23_prod_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[44]'); }
  get lblproductA24_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//ul)[3]'); }
  get lblproductA25_1_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//ul)[4]'); }
  get lblproductA25_2_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//ul)[5]'); }
  get lblproductA26_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[48]'); }
  get lblproductA27_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[49]'); }
  get lblproductA28_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[50]'); }

  get hdrwebsite_sensa() { return $('//h3[normalize-space()="WEBSITE INFORMATION"]'); }
  get lblwebsiteQ1_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[40]'); }
  get lblwebsiteQ2_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[41]'); }
  get lblwebsiteQ3_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[42]'); }
  get lblwebsiteQ4_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[43]'); }
  get lblwebsiteQ5_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[44]'); }
  get lblwebsiteQ6_sensa() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[45]'); }
  get lblwebsiteA1_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[51]'); }
  get lblwebsiteA2_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[52]'); }
  get lblwebsiteA3_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[53]'); }
  get lblwebsiteA4_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[54]'); }
  get lblwebsiteA5_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[55]'); }
  get lblwebsiteA6_sensa() { return $('(//*[@class="cmp-accordion-item__content"]//p)[56]'); }
  get hdrneedfurther_sensa() { return $('//h3[normalize-space()="Need Further Assistance?"]'); }
  get lblourcontact_sensa() { return $('//p[contains(text(),"Our contact center hours of operation are Monday t")]'); }
  get lblpleasecall_sensa() { return $('(//*[contains(text(),"Please call 1-833-54-SENSA (73672) and we\'ll gladl")])[1]'); }

  // Site Requirements footerlinks
  get hdrsitereq_sensa() { return $('//*[@class="cmp-title__text"]'); }
  get lbloptimal_sensa() { return $('(//*[@class="cmp-section__container"]//p)[1]'); }
  get hdrmobilereq_sensa() { return $('(//*[@class="cmp-section__container"]//u)[1]'); }
  get hdrdesktopreq_sensa() { return $('(//*[@class="cmp-section__container"]//u)[2]'); }
  get hdrimportant_sensa() { return $('(//*[@class="cmp-section__container"]//u)[3]'); }
  get hdreqdesktopandmobile_sensa() { return $('(//*[@class="cmp-section__container"]//u)[4]'); }
  get lblmobilebrowser_sensa() { return $('(//*[@class="cmp-section__container"]//h4)[1]'); }
  get lblmobileos_sensa() { return $('(//*[@class="cmp-section__container"]//h4)[2]'); }
  get lbldesktopbrowser_sensa() { return $('(//*[@class="cmp-section__container"]//h4)[3]'); }
  get lbldesktopOS_sensa() { return $('(//*[@class="cmp-section__container"]//h4)[4]'); }
  get hdrjavascript_sensa() { return $('(//*[@class="cmp-section__container"]//h4)[5]'); }
  get hdrcookies_sensa() { return $('(//*[@class="cmp-section__container"]//h4)[6]'); }
  get lblmobilebrowserName_sensa() { return $('(//*[@class="cmp-section__container"]//ul)[1]'); }
  get lblmobileOSName_sensa() { return $('(//*[@class="cmp-section__container"]//ul)[2]'); }
  get lbldesktopbrowserName_sensa() { return $('(//*[@class="cmp-section__container"]//ul)[3]'); }
  get lbldesktopOSName_sensa() { return $('(//*[@class="cmp-section__container"]//ul)[4]'); }
  get lblimportantdesc_sensa() { return $('(//*[@class="cmp-section__container"]//p)[2]'); }
  get lbljavscriptdesc_sensa() { return $('(//*[@class="cmp-section__container"]//p)[3]'); }
  get lblcookiesdesc_sensa() { return $('(//*[@class="cmp-section__container"]//p)[4]'); }

  // Footerlinks on each page
  get lnkcontactus_sensa() { return $('(//*[@class="cmp-navigation__group"]//li)[1]'); }
  get lnkfaq_sensa() { return $('(//*[@class="cmp-navigation__group"]//li)[2]'); }
  get lnksitereq_sensa() { return $('(//*[@class="cmp-navigation__group"]//li)[3]'); }
  get lnktermsofuse_sensa() { return $('(//*[@class="cmp-navigation__group"]//li)[4]'); }
  get lnkprivacypolicy_sensa() { return $('(//*[@class="cmp-navigation__group"]//li)[5]'); }
  get lnktextmessaging_sensa() { return $('(//*[@class="cmp-navigation__group"]//li)[6]'); }
  get lnkpatents_sensa() { return $('(//*[@class="cmp-navigation__group"]//li)[7]'); }
  get lnksustainability_sensa() { return $('(//*[@class="cmp-navigation__group"]//li)[8]'); }
  get lnkcamelpoints_sensa() { return $('(//*[@class="cmp-navigation__group"]//li)[9]'); }

  // Terms of Use footerlinks

  get hdrtermsOfUse_sensa() { return $('//b[normalize-space()="TERMS AND CONDITIONS OF USE"]'); }
  get lnkrestrictionson_sensa() { return $('(//*[@class="cmp-grid-column__content"]//p)[1]'); }
  get lnkaccount_sensa() { return $('(//*[@class="cmp-grid-column__content"]//p)[2]'); }
  get lnkstuff_sensa() { return $('(//*[@class="cmp-grid-column__content"]//p)[3]'); }
  get lnkconduct_sensa() { return $('(//*[@class="cmp-grid-column__content"]//p)[4]'); }
  get lnkcontenton_sensa() { return $('(//*[@class="cmp-grid-column__content"]//p)[5]'); }
  get lnkourPrivacy_sensa() { return $('(//*[@class="cmp-grid-column__content"]//p)[6]'); }
  get lnkintellectual_sensa() { return $('(//*[@class="cmp-grid-column__content"]//p)[7]'); }
  get lnksiteAvailable_sensa() { return $('(//*[@class="cmp-grid-column__content"]//p)[8]'); }
  get lnkchoiceof_sensa() { return $('(//*[@class="cmp-grid-column__content"]//p)[9]'); }
  get lnkdisclaimerof_sensa() { return $('(//*[@class="cmp-grid-column__content"]//p)[10]'); }
  get lnkindemnity_sensa() { return $('(//*[@class="cmp-grid-column__content"]//p)[11]'); }
  get lnkiresolvingDispute_sensa() { return $('(//*[@class="cmp-grid-column__content"]//p)[12]'); }
  get lnkspecialNotice_sensa() { return $('(//*[@class="cmp-grid-column__content"]//p)[13]'); }
  get lnkelectronicSignature_sensa() { return $('(//*[@class="cmp-grid-column__content"]//p)[14]'); }
  get lnkmiscellaneous_sensa() { return $('(//*[@class="cmp-grid-column__content"]//p)[15]'); }

  get lblthefollowingterms_sensa() { return $('((//*[@class="cmp-grid-column__content"])[3]//p)[2]'); }
  get lblaccessanduse_sensa() { return $('((//*[@class="cmp-grid-column__content"])[3]//p)[3]'); }
  get lblrestrictionson_sensa() { return $('(//*[@id="restrictions"])'); }
  get lblaccount_sensa() { return $('(//*[@id="your_account"])'); }
  get lblstuff_sensa() { return $('(//*[@id="your_stuff"])'); }
  get lblconduct_sensa() { return $('(//*[@id="your_conduct"])'); }
  get lblcontenton_sensa() { return $('(//*[@id="content_on_the_site"])'); }
  get lblourPrivacy_sensa() { return $('(//*[@id="privacy_policy"])'); }
  get lblintellectual_sensa() { return $('(//*[@id="intellectual_property_issues"])'); }
  get lblsiteAvailable_sensa() { return $('(//*[@id="site_availability_and_changes"])'); }
  get lblchoiceof_sensa() { return $('(//*[@id="choice_of_law"])'); }
  get lbldisclaimerof_sensa() { return $('(//*[@id="disclaimer"])'); }
  get lblindemnity_sensa() { return $('(//*[@id="indemnity"])'); }
  get lbliresolvingDispute_sensa() { return $('(//*[@id="resolving_disputes"])'); }
  get lblspecialNotice_sensa() { return $('(//*[@id="notice"])'); }
  get lblelectronicSignature_sensa() { return $('(//*[@id="electronic_signature"])'); }
  get lblmiscellaneous_sensa() { return $('(//*[@id="miscellaneous"])'); }

  // Privacy Policy
  get hdrprivacyPolicy_sensa() { return $('//b[contains(text(),"PRIVACY POLICY AND YOUR STATE PRIVACY RIGHTS")]'); }
  get lnkrestrictionsonaccess_sensa() { return $('(//*[@class="cmp-grid-column"]//p)[1]'); }
  get lnkwhywecollect_sensa() { return $('(//*[@class="cmp-grid-column"]//p)[2]'); }
  get lnkwhatwecollect_sensa() { return $('(//*[@class="cmp-grid-column"]//p)[3]'); }
  get lnkwwemayshare_sensa() { return $('(//*[@class="cmp-grid-column"]//p)[4]'); }
  get lnkhowweprotect_sensa() { return $('(//*[@class="cmp-grid-column"]//p)[5]'); }
  get lnkhowcanyoumanage_sensa() { return $('(//*[@class="cmp-grid-column"]//p)[6]'); }
  get lnklinkstothird_sensa() { return $('(//*[@class="cmp-grid-column"]//p)[7]'); }
  get lnkdataretension_sensa() { return $('(//*[@class="cmp-grid-column"]//p)[8]'); }
  get lnknoticetocalifornia_sensa() { return $('(//*[@class="cmp-grid-column"]//p)[9]'); }
  get lnkchangesto_sensa() { return $('(//*[@class="cmp-grid-column"]//p)[10]'); }
  get lnkcontact_sensa() { return $('(//*[@class="cmp-grid-column"]//p)[11]'); }

  get lblprivacypolicy_sensa() { return $('//p[contains(text(),"This Privacy Policy applies to the information tha")]'); }
  get lblwhywecollect_sensa() { return $('(//*[@id="why_we_collect"])'); }
  get lblwwemayshare_sensa() { return $('(//*[@id="share_information"])'); }
  get lblwhatwecollect_sensa() { return $('(//*[@id="what_we_collect"])'); }
  get lblhowweprotect_sensa() { return $('(//*[@id="protect_your_privacy"])'); }
  get lblhowcanyoumanage_sensa() { return $('(//*[@id="control_your_privacy"])'); }
  get lbllinkstothird_sensa() { return $('(//*[@id="third_party_websites"])'); }
  get lbldataretension_sensa() { return $('(//*[@id="data_retention"]//p)[1]'); }
  get lbldataretensiondesc_sensa() { return $('(//*[@id="data_retention"]//p)[2]'); }
  get lblnoticetocalifornia_sensa() { return $('(//*[@id="state_rights"])//p[2]'); }
  get lblchangesto_sensa() { return $('(//*[@id="privacy_policy"])'); }
  get lblcontact_sensa() { return $('(//*[@id="contact_us"])'); }

  // Text Messaging footerlink

  get hdrtextMessaging_sensa() { return $('//h2[contains(text(),"TEXT MESSAGING TERMS AND CONDITIONS AND TEXT MESSA")]'); }
  get lbleachOfRJR_sensa() { return $('(//*[@class="cmp-section "]//p)[2]'); }
  get lblconsenttoReceipt_sensa() { return $('(//*[@class="cmp-section "]//p)[3]'); }
  get lblmodificationofTerms_sensa() { return $('(//*[@class="cmp-section "]//p)[4]'); }
  get lbluserOptIn_sensa() { return $('(//*[@class="cmp-section "]//p)[5]'); }
  get lbluserOptOut_sensa() { return $('(//*[@class="cmp-section "]//p)[6]'); }
  get lbldutytoNotify_sensa() { return $('(//*[@class="cmp-section "]//p)[7]'); }
  get lblmobileandAT_sensa() { return $('(//*[@class="cmp-section "]//p)[8]'); }
  get lblyouAgree_sensa() { return $('(//*[@class="cmp-section "]//p)[9]'); }
  get lblprogramDescription_sensa() { return $('(//*[@class="cmp-section "]//p)[10]'); }
  get lblcostandFrequency_sensa() { return $('(//*[@class="cmp-section "]//p)[11]'); }
  get lblsupportInstructions_sensa() { return $('(//*[@class="cmp-section "]//p)[12]'); }
  get lblmmsDisclosure_sensa() { return $('(//*[@class="cmp-section "]//p)[13]'); }
  get lblourDisclaimerof_sensa() { return $('(//*[@class="cmp-section "]//p)[14]'); }
  get lbladultTobacco_sensa() { return $('(//*[@class="cmp-section "]//p)[15]'); }
  get lbltruthfuland_sensa() { return $('(//*[@class="cmp-section "]//p)[16]'); }
  get lbldisputeResolution_sensa() { return $('(//*[@class="cmp-section "]//p)[17]'); }
  get lblthepartiesagree_sensa() { return $('(//*[@class="cmp-section "]//p)[18]'); }
  get lblMiscellaneous_sensa() { return $('(//*[@class="cmp-section "]//p)[19]'); }

  // Post-login Contact us

  get hdrpostloginContactus_sensa() { return $('//h1[@class="cmp-title__text"]'); }
  get lblourHours_sensa() { return $('(//*[@class="cmp-grid-column"]//p)[1]'); }
  get lblmondayToSaturday_sensa() { return $('(//*[@class="cmp-grid-column"]//p)[2]'); }
  get lblsendAQuestion_sensa() { return $('(//*[@class="cmp-grid-column"]//p)[3]'); }
  get hdrchat_sensa() { return $('//*[@class="chat-headline"]'); }
  get hdremail_sensa() { return $('//*[@class="email-headline"]'); }
  get hdrcall_sensa() { return $('//*[@class="phone-headline"]'); }
  get btnchat_sensa() { return $('//*[@aria-label="Chat"]'); }
  get btncall_sensa() { return $('//*[@class="cmp-button call-button"]'); }
  get btnsubmit_sensa() { return $('//*[@class="cmp-button cmp-form__submit-btn"]'); }
  get txtdropdown_sensa() { return $('//*[@id="topic"]'); }
  get txtmessage_sensa() { return $('//*[@id="message"]'); }
  get lblmessage_sensa() { return $('//label[@for="message"]'); }

}
export default new FooterlinkPageObject();