import { Then, When } from '@wdio/cucumber-framework';
import logger from '../../support/utils/logger.util.ts';
import NasOurDifferencepage from '../../pages/americanspirit/americanspirit-ourdifference.ts';
import NasOurDiffernecPageObject from '../../page-object/americanspirit/americanspirit-ourdifference.pageObject.ts';

When(/^The user clicks on Discover$/, async function () {
    await NasOurDifferencepage.clickonDiscover();
    await expect(NasOurDiffernecPageObject.Nas_ourDifferenceImg).toBeDisplayed();
    logger.info('Navigated to Discover Page Successfully');
});

Then(/^The user Validates Nas Discover Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await NasOurDifferencepage.DiscoverPageValidation(filepath, sheetname, scenarioname);
    logger.info('User Validates Nas Discover Page succsessfully');
});

When(/^The user clicks on Our Partner$/, async function () {
    await NasOurDifferencepage.clickonOurPartner();
    await expect(NasOurDiffernecPageObject.Nas_partnersHeading).toBeDisplayed();
    logger.info('Navigated to Our Partner Page Successfully');
});

Then(/^The user Validates Nas Our Partner Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await NasOurDifferencepage.OurPartnersPageValidation(filepath, sheetname, scenarioname);
    logger.info('User Validates Nas Our Partner Page succsessfully');
});

When(/^The user clicks on Our Tobacco$/, async function () {
    await NasOurDifferencepage.clickonOurTobacco();
    await expect(NasOurDiffernecPageObject.Nas_uniquelyCrafted).toBeDisplayed();
    logger.info('Navigated to Our Tobacco Page Successfully');
});

Then(/^The user Validates Nas Our Tobacco Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await NasOurDifferencepage.OurPartnersPageValidation(filepath, sheetname, scenarioname);
    logger.info('User Validates Nas Our Tobacco Page succsessfully');
});