import { join } from 'path';
import type { Options } from '@wdio/types';
import allure from '@wdio/allure-reporter';
import * as fs from 'fs';
import * as path from 'path';
import { ExcelToJsonConverter } from '../../tests/support/utils/ExcelToJsonConverter.ts';
import { EnhancedScenarioRetry } from '../support/utils/EnhancedScenarioRetry.ts';
import { TestExecutionTracker } from '../support/utils/TestExecutionTracker.ts';
import {
  isHealthCheckOrServiceUrl,
  isServiceInitializationScenario,
  isSessionValid,
} from '../support/utils/healthCheckDetector.js';

// Initialize the enhanced retry system
const retryManager = new EnhancedScenarioRetry();

// Track all scenario results for final summary
type ScenarioResult = {
  name: string;
  feature: string;
  status: 'passed' | 'failed' | 'skipped';
  attempts: number; // attempt number of the final result
  totalAttempts?: number; // total number of attempts for this scenario
  tags?: string[];
  hadFailedAttempt?: boolean;
  executionId?: string; // Track execution ID for duplicate detection
};

const allScenarioResults: ScenarioResult[] = [];
let currentExecutionId: string | undefined;

export const config: Options.Testrunner = {
  //
  // ====================
  // Runner Configuration
  // ====================
  // WebdriverIO supports running e2e tests as well as unit and component tests.

  runner: 'local',
  autoCompileOpts: {
    autoCompile: true,
    tsNodeOpts: {
      project: './tsconfig.json',
      transpileOnly: true,
    },
  },
  //
  // =================
  // Service Providers
  // =================
  // WebdriverIO supports Sauce Labs, Browserstack, Testing Bot and LambdaTest (other cloud providers
  // should work too though). These services define specific user and key (or access key)
  // values you need to put in here in order to connect to these services.
  //
  //    =======================================================
  //    See wdio.saucelabs.shared.conf.ts for more information.
  //    =======================================================
  //
  // ==================
  // Specify Test Files
  // ==================
  // Define which test specs should run. The pattern is relative to the directory
  // of the configuration file being run.
  //
  // The specs are defined as an array of spec files (optionally using wildcards
  // that will be expanded). The test for each spec file will be run in a separate
  // worker process. In order to have a group of spec files run in the same worker
  // process simply enclose them in an array within the specs array.
  //
  // If you are calling `wdio` from an NPM script (see https://docs.npmjs.com/cli/run-script),
  // then the current working directory is where your `package.json` resides, so `wdio`
  // will be called from there.
  //

  // Patterns to exclude.
  exclude: [
    // 'path/to/excluded/files'
  ],
  //
  // ============
  // Capabilities
  // ============
  // Define your capabilities here. WebdriverIO can run multiple capabilities at the same
  // time. Depending on the number of capabilities, WebdriverIO launches several test
  // sessions. Within your capabilities you can overwrite the spec and exclude options in
  // order to group specific specs to a specific capability.
  //
  // First, you can define how many instances should be started at the same time. Let's
  // say you have 3 different capabilities (Chrome, Firefox, and Safari) and you have
  // set maxInstances to 1; wdio will spawn 3 processes. Therefore, if you have 10 spec
  // files and you set maxInstances to 10, all spec files will get tested at the same time
  // and 30 processes will get spawned. The property handles how many capabilities
  // from the same test should run tests.
  //
  maxInstances: 10,
  //
  // If you have trouble getting all important capabilities together, check out the
  // Sauce Labs platform configurator - a great tool to configure your capabilities:
  // https://saucelabs.com/platform/platform-configurator
  //
  //    =================================
  //    For capabilities see:
  //    - wdio.saucelabs.desktop.conf.ts
  //    =================================
  //
  capabilities: [],
  //
  // ===================
  // Test Configurations
  // ===================
  // Define all options that are relevant for the WebdriverIO instance here
  //
  // Level of logging verbosity: trace | debug | info | warn | error | silent
  logLevel: 'trace',
  //
  // Set specific log levels per logger
  // loggers:
  // - webdriver, webdriverio
  // - @wdio/browserstack-service, @wdio/devtools-service, @wdio/sauce-service
  // - @wdio/mocha-framework, @wdio/jasmine-framework
  // - @wdio/local-runner
  // - @wdio/sumologic-reporter
  // - @wdio/cli, @wdio/config, @wdio/utils
  // Level of logging verbosity: trace | debug | info | warn | error | silent
  // logLevels: {
  //     webdriver: 'info',
  //     '@wdio/appium-service': 'info'
  // },
  //
  // If you only want to run your tests until a specific amount of tests have failed use
  // bail (default is 0 - don't bail, run all tests).
  bail: 0,
  //
  // Set a base URL in order to shorten url command calls. If your `url` parameter starts
  // with `/`, the base url gets prepended, not including the path portion of your baseUrl.
  // If your `url` parameter starts without a scheme or `/` (like `some/path`), the base url
  // gets prepended directly.
  baseUrl: 'https://saucedemo.com',
  //
  // Default timeout for all waitFor* commands.
  waitforTimeout: 10000,
  //
  // Default timeout in milliseconds for request
  // if browser driver or grid doesn't send response
  connectionRetryTimeout: 120000,
  //
  // Default request retries count
  connectionRetryCount: 3,
  //
  // Test runner services
  // Services take over a specific job you don't want to take care of. They enhance
  // your test setup with almost no effort. Unlike plugins, they don't add new
  // commands. Instead, they hook themselves up into the test process.
  //
  //    ==============================================================
  //    For implementing Sauce Labs, see wdio.saucelabs.shared.conf.ts
  //    ==============================================================
  //
  services: [],

  // Framework you want to run your specs with.
  // The following are supported: Mocha, Jasmine, and Cucumber
  // see also: https://webdriver.io/docs/frameworks
  //
  // Make sure you have the wdio adapter package for the specific framework installed
  // before running any tests.
  framework: 'cucumber',
  //
  // The number of times to retry the entire specfile when it fails as a whole
  // specFileRetries: 1,
  //
  // Delay in seconds between the spec file retry attempts
  // specFileRetriesDelay: 0,
  //
  // Whether or not retried spec files should be retried immediately or deferred to the end of the queue
  // specFileRetriesDeferred: false,
  //
  // Test reporter for stdout.
  // The only one supported by default is 'dot'
  // see also: https://webdriver.io/docs/dot-reporter
  reporters: [
    'spec',
    ['cucumber', {
      json: {
        enabled: true,
        outputDir: './reports/cucumber-json',
      },
      summary: {
        enabled: true,
        showTotal: true,
        overviewTable: true,
        failureMessages: true,
      },
    }],
    ['allure', {
      outputDir: './reports/allure-results',
      disableWebdriverStepsReporting: true,
      useCucumberStepReporter: false,
      disableWebdriverScreenshotsReporting: false,
      disableMochaHooks: true,
      addConsoleLogs: false,
    }],
    ['./tests/support/reporters/EnhancedCucumberReporter.ts', {}],
  ],

  // If you are using Cucumber you need to specify the location of your step definitions.
  cucumberOpts: {
    // <string[]> (file/dir) require files before executing features
    require: [join(process.cwd(), './tests/step-definitions/**/*.ts')],
    // <boolean> show full backtrace for errors
    backtrace: false,
    // <string[]> ("extension:module") require files with the given EXTENSION after requiring MODULE (repeatable)
    requireModule: [],
    // <boolean> invoke formatters without executing steps
    dryRun: false,
    // <boolean> abort the run on first failure
    failFast: false,
    // <boolean> hide step definition snippets for pending steps
    snippets: true,
    // <boolean> hide source uris
    source: true,
    // <boolean> fail if there are any undefined or pending steps
    strict: false,
    // <string> (expression) only execute the features or scenarios with tags matching the expression
    tagExpression: '',
    // <number> timeout for step definitions
    timeout: 60000,
    // <boolean> Enable this config to treat undefined definitions as warnings.
    ignoreUndefinedDefinitions: false,
    // FIXED: Set consistent retry configuration
    retry: 2, // 2 retries = 3 total attempts maximum
  },
  //
  // =====
  // Hooks
  // =====
  // WebdriverIO provides several hooks you can use to interfere with the test process in order to enhance
  // it and to build services around it. You can either apply a single function or an array of
  // methods to it. If one of them returns with a promise, WebdriverIO will wait until that promise got
  // resolved to continue.
  /**
   * Gets executed once before all workers get launched.
   * Performs one-time setup including Excel to JSON conversion and retry system initialization
   */
  onPrepare: async function () {
    console.log('🚀 Preparing test environment...');

    // Extract cucumberOpts.retry from config if present and pass to retryManager
    let cucumberRetry = 0;
    if (config.cucumberOpts && typeof config.cucumberOpts.retry === 'number') {
      cucumberRetry = config.cucumberOpts.retry;
      console.log(`🔍 [onPrepare] cucumberOpts.retry found in config: ${cucumberRetry}`);
    } else {
      console.log('🔍 [onPrepare] No cucumberOpts.retry found in config, using default retry logic');
    }

    // Initialize the enhanced retry system with config value if present
    retryManager.initialize({ maxRetries: cucumberRetry });
    console.log(`🔄 Enhanced Scenario Retry System initialized (maxRetries: ${cucumberRetry})`);

    // Perform Excel to JSON conversion once at the start
    const excelFilePath = path.join(process.cwd(), 'data', 'aem-mobile-camel.xlsx');
    const jsonFilePath = path.join(process.cwd(), 'data', 'aem-mobile-camel.json');

    if (fs.existsSync(excelFilePath)) {
      // Only convert if JSON doesn't exist or Excel is newer than JSON
      let shouldConvert = !fs.existsSync(jsonFilePath);

      if (!shouldConvert) {
        const excelStats = fs.statSync(excelFilePath);
        const jsonStats = fs.statSync(jsonFilePath);
        shouldConvert = excelStats.mtime > jsonStats.mtime;
      }

      if (shouldConvert) {
        console.log('📊 Converting Excel to JSON (one-time setup)...');
        const converter = new ExcelToJsonConverter(excelFilePath);
        await converter.convertToJson();
        console.log('✅ Excel test data conversion complete.');
      } else {
        console.log('✅ Using existing JSON file (up to date).');
      }

      console.log('✅ Excel to JSON conversion completed successfully.');
    } else {
      console.warn(`⚠️  Excel file not found at ${excelFilePath}. Using existing JSON if available.`);
    }
  },

  beforeScenario: async function (world) {
    // DEBUG: Track scenario execution to detect duplicates
    const currentScenarioName = world.pickle.name;
    
    // Use the feature name from the Gherkin document if available, else fallback to filename
    let featureName = 'Unknown Feature';
    if (world.gherkinDocument && world.gherkinDocument.feature && world.gherkinDocument.feature.name) {
      featureName = world.gherkinDocument.feature.name.trim();
    } else if (world.pickle.uri) {
      featureName = world.pickle.uri.split(/[\\/]/).pop()?.replace('.feature', '') || 'Unknown Feature';
    }

    // Get current attempt information from retry manager before starting tracking
    retryManager.detectCucumberOptsRetryIfAvailable();
    const currentAttempt = retryManager.getCurrentAttemptFromWorld(world);
    
    // Start execution tracking with attempt information
    let sessionId: string | undefined;
    try {
      const session = await browser.getSession();
      sessionId = session.sessionId as string;
    } catch {
      sessionId = 'N/A';
    }
    
    currentExecutionId = TestExecutionTracker.startScenario(
      currentScenarioName,
      featureName,
      currentAttempt,
      sessionId,
    );
    
    const configLog = retryManager.getConfig();
    console.log('🔁 [BeforeScenario] Effective Retry Config:', JSON.stringify(configLog));
    
    // Use consistent variable names throughout the function
    const scenarioName = currentScenarioName;

    // Filter out health checks and service initialization scenarios from SauceLabs reporting
    if (isServiceInitializationScenario(scenarioName, featureName)) {
      console.log(`🔧 Skipping SauceLabs reporting for service initialization: ${scenarioName}`);
      return; // Skip SauceLabs reporting for health checks and service initialization
    }

    // Check if this is a health check URL scenario
    try {
      const currentUrl = await browser.getUrl();
      if (isHealthCheckOrServiceUrl(currentUrl)) {
        console.log(`🔧 Skipping SauceLabs reporting for health check URL: ${currentUrl}`);
        return; // Skip SauceLabs reporting for health check URLs
      }
    } catch {
      // If we can't get URL, continue with normal processing
      console.log('⚠️  Could not check URL for health check filtering, proceeding normally');
    }

    // Use the enhanced retry system's beforeScenario handler
    await retryManager.handleBeforeScenario(world);

    // FIXED: Only force new session for first attempt or service initialization scenarios
    // Retries should preserve session context for better stability
    if (currentAttempt === 1 || isServiceInitializationScenario(scenarioName, featureName)) {
      console.log(`🆕 Creating FRESH SESSION for: ${scenarioName} (First attempt or service initialization)`);
      
      try {
        console.log('🔄 Forcing new session creation...');
        await browser.reloadSession();
        console.log('✅ New session created successfully');

        // Give extra time for new session initialization
        await new Promise(resolve => setTimeout(resolve, 3000));
      } catch (reloadError) {
        console.log('⚠️  Session reload failed:', reloadError instanceof Error ? reloadError.message : String(reloadError));
        console.log('🔄 Continuing with existing session...');
      }
    } else {
      console.log(`🔄 Retry attempt ${currentAttempt} - REUSING existing session for: ${scenarioName}`);
      
      // For retries, just validate session is still active
      try {
        await browser.getUrl(); // Simple session validation
        console.log('✅ Existing session validated for retry');
      } catch (sessionError) {
        console.log('⚠️  Session validation failed, creating new session for retry:', sessionError instanceof Error ? sessionError.message : String(sessionError));
        await browser.reloadSession();
      }
    }

    // Allow SauceLabs to complete health checks and session initialization
    console.log('⏳ Waiting for SauceLabs session to be fully ready...');
    await new Promise(resolve => setTimeout(resolve, 3000)); // Give SauceLabs extra time for fresh sessions

    // Verify we have a valid session and set up keep-alive
    try {
      const sessionId = browser.sessionId;
      console.log(`✅ Session ready: ${sessionId} for ${scenarioName} (Attempt ${currentAttempt})`);

      // Get device and platform information to verify fresh allocation
      const capabilities = browser.capabilities as Record<string, unknown>;
      const deviceName = (capabilities['appium:deviceName'] as string) ||
        (capabilities.deviceName as string) ||
        'Unknown Device';
      const platformVersion = (capabilities['appium:platformVersion'] as string) ||
        (capabilities.platformVersion as string) ||
        'Unknown Version';

      console.log(`📱 Device allocated: ${deviceName} (${platformVersion}) for ${scenarioName} attempt ${currentAttempt}`);

      // Send a keep-alive command to prevent session timeout
      await browser.execute('sauce:context=Session initialized and ready for test execution');
      console.log('🔄 Session keep-alive sent');
    } catch (sessionError) {
      console.log('⚠️  Session verification failed:', sessionError instanceof Error ? sessionError.message : String(sessionError));
    }

    try {
      if (await isSessionValid()) {
        console.log('✅ Session is ready, proceeding with test...');
      } else {
        console.log('⚠️  Session validation failed, but proceeding...');
      }
    } catch (error) {
      console.log('Session validation error, proceeding anyway:', error instanceof Error ? error.message : String(error));
    }

    // Set clean test name for SauceLabs reporting with session isolation info, now including feature name
    const retryInfo = currentAttempt > 1 ? ` - Retry ${currentAttempt}` : '';
    const sessionInfo = ` [Session: ${browser.sessionId.substring(0, 8)}]`;
    const testName = `${featureName} - ${scenarioName}${retryInfo}${sessionInfo}`;
    await browser.execute('sauce:job-name=' + testName);

    // Add comprehensive context for scenario-level session reporting
    await browser.execute('sauce:context=Feature: ' + featureName);
    await browser.execute('sauce:context=Scenario: ' + scenarioName);
    await browser.execute('sauce:context=Session Type: Scenario-Level Isolation');
    await browser.execute('sauce:context=Attempt: ' + currentAttempt);
    await browser.execute('sauce:context=Retry Configuration: ' + JSON.stringify(retryManager.getConfig()));

    // Add device allocation information
    const capabilities = browser.capabilities as Record<string, unknown>;
    const deviceName = (capabilities['appium:deviceName'] as string) ||
      (capabilities.deviceName as string) ||
      'Unknown Device';
    const platformVersion = (capabilities['appium:platformVersion'] as string) ||
      (capabilities.platformVersion as string) ||
      'Unknown Version';
    await browser.execute('sauce:context=Device: ' + deviceName + ' (' + platformVersion + ')');

    // Add tags as context if available
    if (world.pickle.tags && world.pickle.tags.length > 0) {
      const tags = world.pickle.tags.map(tag => tag.name).join(', ');
      await browser.execute('sauce:context=Tags: ' + tags);
    }

    // Mark test as in progress
    await browser.execute('sauce:context=Status: Running');
    allure.addLabel('feature', world.pickle.name);

    // Use already declared capabilities, deviceName, and platformVersion variables
    allure.addArgument('Device', deviceName);

    // Extract platform information
    const platformName = (capabilities.platformName as string) || 'Unknown Platform';
    allure.addArgument('Platform', `${platformName} ${platformVersion}`.trim());

    // Extract browser information
    const browserName = (capabilities.browserName as string) || 'Unknown Browser';
    const browserVersion = (capabilities.browserVersion as string) || '';
    allure.addArgument('Browser', `${browserName} ${browserVersion}`.trim());

    // Add additional environment information if available
    if (capabilities['sauce:options']) {
      const sauceOptions = capabilities['sauce:options'] as Record<string, unknown>;
      if (sauceOptions.build) {
        allure.addArgument('Build', sauceOptions.build as string);
      }
    }

    // Add retry information to Allure
    allure.addArgument('Retry Attempt', currentAttempt.toString());
    allure.addArgument('Max Retries', retryManager.getConfig().maxRetries.toString());
    allure.addArgument('Retry Source', retryManager.getConfig().source);
  },

  /**
   * Hook that gets executed before each step execution
   * This provides step-level tracking with Sauce Labs integration
   */
  beforeStep: async function (step, _scenario) {
    const stepText = step.text || 'Unknown Step';
    console.log(`      📝 Starting: ${stepText}`);
    
    // Store step start time for duration calculation
    (step as unknown as Record<string, unknown>)._stepStartTime = Date.now();
    
    // Add step context to Sauce Labs if available
    try {
      if (typeof browser !== 'undefined' && browser.execute) {
        await browser.execute(`sauce:context=Step: ${stepText}`);
      }
    } catch {
      // Ignore Sauce Labs context errors to avoid interrupting test flow
    }
  },

  /**
   * Hook that gets executed after each step execution
   * This provides comprehensive step result tracking and error handling
   */
  afterStep: async function (step, _scenario, result) {
    const stepText = step.text || 'Unknown Step';
    const startTime = (step as unknown as Record<string, unknown>)._stepStartTime as number || Date.now();
    const duration = Date.now() - startTime;
    
    // Determine step status from result
    let status: 'passed' | 'failed' | 'pending' = 'passed';
    let error: string | undefined = undefined;
    
    if (result) {
      if (result.passed === true) {
        status = 'passed';
      } else if (result.passed === false) {
        status = 'failed';
        if (result.error) {
          if (typeof result.error === 'string') {
            error = result.error;
          } else {
            // Handle error object or other types
            const errorObj = result.error as Record<string, unknown>;
            error = (errorObj.message as string) || String(result.error);
          }
        } else {
          error = 'Step failed';
        }
      } else {
        status = 'pending';
      }
    }
    
    // Enhanced console logging with status icons
    const statusIcon = status === 'passed' ? '✅' : status === 'failed' ? '❌' : '⏸️';
    console.log(`      ${statusIcon} ${stepText} (${duration}ms)`);
    
    if (error) {
      console.log(`         💥 ${error.substring(0, 100)}...`);
    }
    
    // Handle step failure - capture screenshot if needed
    if (status === 'failed') {
      try {
        if (typeof browser !== 'undefined' && browser.takeScreenshot) {
          // Capture screenshot with step context
          const timestamp = new Date().toISOString().replace(/[^0-9]/g, '');
          const screenshotName = `step-failure-${timestamp}`;
          const screenshot = await browser.takeScreenshot();
          
          // Save screenshot to file system
          try {
            const screenshotDir = path.join(process.cwd(), 'reports', 'screenshots', 'steps');
            fs.mkdirSync(screenshotDir, { recursive: true });
            const screenshotPath = path.join(screenshotDir, `${screenshotName}.png`);
            fs.writeFileSync(screenshotPath, Buffer.from(screenshot, 'base64'));
            console.log(`         📸 Step failure screenshot: ${screenshotPath}`);
          } catch {
            console.log('         ⚠️  Could not save step screenshot');
          }
          
          // Attach to Allure report if available
          try {
            allure.addAttachment(
              `Step Failure: ${stepText}`,
              Buffer.from(screenshot, 'base64'),
              'image/png',
            );
          } catch {
            // Allure not available, skip attachment
          }
          
          // Add failure context to Sauce Labs
          if (browser.execute) {
            await browser.execute(`sauce:context=STEP FAILED: ${stepText}`);
            if (error) {
              await browser.execute(`sauce:context=Step Error: ${error.substring(0, 200)}`);
            }
          }
        }
      } catch {
        console.log('         ⚠️  Could not capture step failure screenshot');
      }
    }
    
    // Add step result to Sauce Labs if available
    try {
      if (typeof browser !== 'undefined' && browser.execute) {
        await browser.execute(`sauce:context=Step Result: ${status.toUpperCase()} - ${stepText} (${duration}ms)`);
      }
    } catch {
      // Ignore Sauce Labs context errors
    }
  },

  /**
   * Function to be executed after a test (in Mocha/Jasmine only)
   * @param {object}  test             test object
   * @param {object}  _context         scope object the test was executed with (unused)
   * @param {Error}   result.error     error object in case the test fails, otherwise `undefined`
   * @param {*}       result.result    return object of test function
   * @param {number}  result.duration  duration of test
   * @param {boolean} result.passed    true if test has passed, otherwise false
   * @param {object}  result.retries   informations to spec related retries, e.g. `{ attempts: 0, limit: 0 }`
   */
  afterTest: async function (test, _context, { error, result, duration, passed, retries }) {
    // Add test status to Allure report
    allure.addLabel('status', passed ? 'passed' : 'failed');

    // Add test duration information
    allure.addArgument('duration', `${duration}ms`);

    // Add retry information
    allure.addArgument('retries', `${retries.attempts} of ${retries.limit}`);

    // Add test result information if available
    if (result) {
      allure.addArgument('result', typeof result === 'object' ? JSON.stringify(result) : String(result));
    }

    // Handle failed tests - capture screenshots and error messages
    if (!passed) {
      const timestamp = new Date().toISOString().replace(/[^0-9]/g, '');
      const screenshot = await browser.takeScreenshot();
      const screenshotPath = `./reports/screenshots/failure-${test.parent}-${test.title}-${timestamp}.png`;

      // Ensure the directory exists
      fs.mkdirSync(path.dirname(screenshotPath), { recursive: true });

      // Save screenshot to file
      fs.writeFileSync(screenshotPath, Buffer.from(screenshot, 'base64'));

      // Attach screenshot to Allure report
      allure.addAttachment(
        `Failure-Screenshot-${test.title}`,
        Buffer.from(screenshot, 'base64').toString('base64'),
        'image/png',
      );

      // Also attach error message if there is one
      if (error) {
        allure.addAttachment(
          'Error Message',
          error.message,
          'text/plain',
        );
      }
    }
  },

  /**
   * Hook that runs before the test session starts
   * Excel conversion is now handled in onPrepare hook
   */
  beforeSession: async function () {
    console.log('📋 Setting up worker session for test execution...');

    // Verify JSON file exists (should be created by onPrepare hook)
    const jsonFilePath = path.join(process.cwd(), 'data', 'aem-mobile-camel.json');
    if (fs.existsSync(jsonFilePath)) {
      console.log('✅ Test data JSON file found. Worker ready for test execution.');
    } else {
      console.warn('⚠️  Test data JSON file not found. Tests may fail if they require test data.');
    }
  },

  afterScenario: async function (world, result) {
    console.log(`Cleaning up after: ${world.pickle.name}`);
    const scenarioName = world.pickle.name;
    
    // Use the feature name from the Gherkin document if available, else fallback to filename
    let featureName = 'Unknown Feature';
    if (world.gherkinDocument && world.gherkinDocument.feature && world.gherkinDocument.feature.name) {
      featureName = world.gherkinDocument.feature.name.trim();
    } else if (world.gherkinDocument && world.gherkinDocument.uri) {
      featureName = world.gherkinDocument.uri.split(/[\\/]/).pop()?.replace('.feature', '') || 'Unknown Feature';
    } else if (world.pickle.uri) {
      featureName = world.pickle.uri.split(/[\\/]/).pop()?.replace('.feature', '') || 'Unknown Feature';
    }

    // Filter out health checks and service initialization scenarios from SauceLabs reporting
    if (isServiceInitializationScenario(scenarioName, featureName)) {
      console.log(`🔧 Skipping SauceLabs cleanup for service initialization: ${scenarioName}`);
      return; // Skip SauceLabs cleanup for health checks and service initialization
    }

    // Check if this is a health check URL scenario
    try {
      const currentUrl = await browser.getUrl();
      if (isHealthCheckOrServiceUrl(currentUrl)) {
        console.log(`🔧 Skipping SauceLabs cleanup for health check URL: ${currentUrl}`);
        return; // Skip SauceLabs cleanup for health check URLs
      }
    } catch {
      // If we can't get URL, continue with normal processing
      console.log('⚠️  Could not check URL for health check cleanup filtering, proceeding normally');
    }

    // Use the enhanced retry system's afterScenario handler
    const startTime = Date.now();
    retryManager.handleAfterScenario(world, result, Date.now() - startTime);

    // End execution tracking
    if (currentExecutionId) {
      TestExecutionTracker.endScenario(currentExecutionId, result.passed ? 'passed' : 'failed');
    }

    // Get current metrics from retry manager
    const currentAttempt = retryManager.getCurrentAttemptFromWorld(world);
    // const retryMetrics = retryManager.getRetryMetrics(scenarioName, featureName); // Removed unused variable
    const config = retryManager.getConfig();

    // Enhanced logging for scenario results
    const statusIcon = result.passed ? '✅' : '❌';
    console.log(`${statusIcon} SCENARIO RESULT: "${scenarioName}" in "${featureName}" - ${result.passed ? 'PASSED' : 'FAILED'} (Attempt ${currentAttempt})`);

    // Add tags information if available
    if (world.pickle.tags && world.pickle.tags.length > 0) {
      const tags = world.pickle.tags.map(tag => tag.name).join(', ');
      console.log(`   📋 Tags: ${tags}`);
    }

    try {
      // Write scenario result to a file for aggregation in onComplete
      const scenarioResult = {
        name: scenarioName,
        feature: featureName,
        status: result.passed ? 'passed' : 'failed' as 'passed' | 'failed',
        attempts: currentAttempt,
        tags: world.pickle.tags?.map(tag => tag.name),
      };
      try {
        const resultsDir = path.join(process.cwd(), 'reports', 'scenario-results');
        if (!fs.existsSync(resultsDir)) {
          fs.mkdirSync(resultsDir, { recursive: true });
        }
        const fileSafeName = scenarioName.replace(/[^a-zA-Z0-9-_]/g, '_').substring(0, 60);
        const filePath = path.join(resultsDir, `${fileSafeName}_${Date.now()}.json`);
        fs.writeFileSync(filePath, JSON.stringify(scenarioResult, null, 2), 'utf-8');
        console.log(`[DEBUG] Scenario result written to: ${filePath}`);
        // Extra debug: confirm file content for first-attempt pass
        if (result.passed && currentAttempt === 1) {
          const written = fs.readFileSync(filePath, 'utf-8');
          console.log(`[DEBUG] First-attempt pass file content: ${written}`);
        }
      } catch (fileErr) {
        console.log('[DEBUG] Failed to write scenario result file:', fileErr);
      }

      if (result.passed) {
        console.log(`✅ Scenario PASSED: ${scenarioName} (Attempt ${currentAttempt})`);

        // Track scenario result for final summary
        allScenarioResults.push({
          name: scenarioName,
          feature: featureName,
          status: 'passed',
          attempts: currentAttempt,
          tags: world.pickle.tags?.map(tag => tag.name),
        });
        console.log(`🔍 Debug: Added PASSED scenario to results. Total count: ${allScenarioResults.length}`);

        // Update test name to show final PASSED status, now including feature name
        const finalTestName = `${featureName} - ${scenarioName} - PASSED`;
        await browser.execute('sauce:job-name=' + finalTestName);

        // For passed scenarios, immediately set the final status
        await browser.execute('sauce:job-result=passed');
        await browser.execute('sauce:context=Final Result: PASSED after ' + currentAttempt + ' attempt(s)');

        // Add final confirmation context
        await browser.execute('sauce:context=Test completed successfully');

        console.log('✅ Scenario passed - session will be cleaned up naturally by WebDriverIO');

      } else {
        console.log(`❌ Scenario FAILED: ${featureName} - ${scenarioName} (Attempt ${currentAttempt})`);

        // For failed scenarios, we need to determine if this is the final attempt
        await browser.execute('sauce:context=Attempt ' + currentAttempt + ': FAILED');

        // Add failure reason if available
        if (result.error) {
          const errorMessage = typeof result.error === 'string'
            ? result.error
            : (result.error as Error).message || String(result.error);
          await browser.execute('sauce:context=Error: ' + errorMessage.substring(0, 200));
        }

        // Check if this scenario will be retried by looking at the retry configuration
        // If this is the final attempt, set the final failed status
        console.log(`📊 Retry info: Current attempt ${currentAttempt} of max ${config.maxRetries} for ${scenarioName}`);

        if (currentAttempt >= config.maxRetries) {
          console.log(`🔴 Final attempt reached for ${scenarioName}. Setting final FAILED status.`);

          // Track scenario result for final summary
          allScenarioResults.push({
            name: scenarioName,
            feature: featureName,
            status: 'failed',
            attempts: currentAttempt,
            tags: world.pickle.tags?.map(tag => tag.name),
          });

          // Update test name to show final FAILED status, now including feature name
          const finalTestName = `${featureName} - ${scenarioName} - FAILED`;
          await browser.execute('sauce:job-name=' + finalTestName);

          await browser.execute('sauce:job-result=failed');
          await browser.execute('sauce:context=Final Result: FAILED after ' + currentAttempt + ' attempt(s)');

        } else {
          console.log(`🔄 Scenario ${scenarioName} will be retried. Not setting final status yet.`);
          // For non-final attempts, don't set job-result to avoid premature status setting
          await browser.execute('sauce:context=Will retry - attempt ' + currentAttempt + ' of ' + config.maxRetries);

          // Update test name to show retry status, now including feature name
          const retryTestName = `${featureName} - ${scenarioName} - Retrying (${currentAttempt}/${config.maxRetries})`;
          await browser.execute('sauce:job-name=' + retryTestName);
        }
      }

      // Determine session cleanup strategy using retry manager configuration
      const isFinalAttempt = currentAttempt >= config.maxRetries;
      const shouldTerminateSession = result.passed || isFinalAttempt;

      if (shouldTerminateSession) {
        console.log(`🔄 Final cleanup for ${scenarioName} (Final: ${isFinalAttempt ? 'Yes' : 'No'}, Passed: ${result.passed ? 'Yes' : 'No'})`);

        // Quick session validation with timeout
        let sessionValid = false;
        try {
          // Use a quick command with short timeout to check session
          await Promise.race([
            browser.getUrl(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Session check timeout')), 5000)),
          ]);
          sessionValid = true;
          console.log('✅ Session is active for final cleanup');
        } catch {
          console.log('⚠️  Session appears inactive, skipping cleanup operations');
        }

        // Skip cookie cleanup for passed scenarios to avoid delays
        if (sessionValid && !result.passed) {
          try {
            console.log('🔄 Performing cookie cleanup for failed scenario');
            await Promise.race([
              browser.deleteAllCookies(),
              new Promise((_, reject) => setTimeout(() => reject(new Error('Cookie cleanup timeout')), 10000)),
            ]);
            console.log(`✅ Cookie cleanup completed for ${scenarioName}`);
          } catch (cleanupError) {
            console.log(`⚠️  Cookie cleanup skipped due to timeout or error: ${cleanupError instanceof Error ? cleanupError.message : String(cleanupError)}`);
          }
        } else if (result.passed) {
          console.log('✅ Scenario passed - session will be cleaned up naturally by WebDriverIO');
          // Let WebDriverIO handle session termination naturally to avoid conflicts
          // The session isolation is achieved through maxInstances: 1 configuration
        }

        // Let WebDriverIO handle session termination naturally for passed tests
        if (!result.passed && sessionValid) {
          try {
            console.log(`🔄 Terminating session for failed scenario: ${scenarioName}`);
            await browser.deleteSession();
            console.log(`✅ Session terminated successfully for ${scenarioName}`);
          } catch (sessionTerminationError) {
            console.log(`⚠️  Session termination failed (session may have auto-terminated): ${sessionTerminationError instanceof Error ? sessionTerminationError.message : String(sessionTerminationError)}`);
          }
        }
      } else {
        console.log(`🔄 Keeping session alive for retry. Current attempt: ${currentAttempt}/${config.maxRetries}`);
      }

    } catch (error) {
      console.log(`Session cleanup failed for ${scenarioName}:`, error instanceof Error ? error.message : String(error));
      // Don't throw error here as it might interfere with retry mechanism
    }
  },

  /**
   * Hook that gets executed before test execution begins
   * Configure element highlighting based on environment variable
   */
  before: function () {
    // Set up element highlighting configuration
    browser.highlightElements = process.env.HIGHLIGHT_ELEMENTS === 'true';

    // Configure highlight appearance
    if (browser.highlightElements) {
      console.log('Element highlighting is ENABLED');
    } else {
      console.log('Element highlighting is DISABLED - using scrollIntoView as fallback');
    }
  },

  /**
   * Hook that gets executed after all tests are completed
   * This runs after the SauceLabs service has set its automatic status
   * We use this to override with our manually controlled status
   */
  after: async function () {
    // Use retry manager to get final results and override any automatic SauceLabs status setting
    try {
      const allMetrics = retryManager.getAllRetryMetrics();
      
      for (const [scenarioId, metrics] of allMetrics.entries()) {
        console.log(`🔧 Final status override for ${scenarioId}: ${metrics.passed ? 'PASSED' : 'FAILED'}`);

        if (metrics.passed) {
          await browser.execute('sauce:job-result=passed');
          await browser.execute('sauce:context=Final Override: PASSED - Enhanced Retry Management');
        } else {
          await browser.execute('sauce:job-result=failed');
          await browser.execute('sauce:context=Final Override: FAILED - Enhanced Retry Management');
        }
      }

    } catch (error) {
      console.log('⚠️  Could not override final SauceLabs status:', error instanceof Error ? error.message : String(error));
    }
  },

  /**
   * Hook that gets executed after all tests are completed
   * Clean up retry tracking and handle final failed scenarios
   */
  onComplete: async function () {
    console.log('Test execution completed. Processing final results...');

    // Print execution summary to detect duplicates
    TestExecutionTracker.getExecutionSummary();

    // Export execution tracking data
    try {
      const executionData = TestExecutionTracker.exportExecutionData();
      const executionPath = path.join(process.cwd(), 'reports', 'execution-tracking.json');
      fs.mkdirSync(path.dirname(executionPath), { recursive: true });
      fs.writeFileSync(executionPath, JSON.stringify(executionData, null, 2), 'utf-8');
      console.log(`🔍 Execution tracking data exported to: ${executionPath}`);
    } catch (exportError) {
      console.log('⚠️  Failed to export execution tracking data:', exportError instanceof Error ? exportError.message : String(exportError));
    }

    // Use the enhanced retry manager to print comprehensive summary
    retryManager.printRetrySummary();

    // Export detailed metrics for further analysis
    const metricsData = retryManager.exportMetrics();
    try {
      const metricsPath = path.join(process.cwd(), 'reports', 'retry-metrics.json');
      fs.mkdirSync(path.dirname(metricsPath), { recursive: true });
      fs.writeFileSync(metricsPath, JSON.stringify(metricsData, null, 2), 'utf-8');
      console.log(`📊 Detailed retry metrics exported to: ${metricsPath}`);
    } catch (exportError) {
      console.log('⚠️  Failed to export retry metrics:', exportError instanceof Error ? exportError.message : String(exportError));
    }

    // Aggregate scenario results from files
    const resultsDir = path.join(process.cwd(), 'reports', 'scenario-results');
    const scenarioResults: ScenarioResult[] = [];
    
    if (fs.existsSync(resultsDir)) {
      const files = fs.readdirSync(resultsDir).filter(f => f.endsWith('.json'));
      for (const file of files) {
        try {
          const content = fs.readFileSync(path.join(resultsDir, file), 'utf-8');
          scenarioResults.push(JSON.parse(content));
        } catch (e) {
          console.log('[DEBUG] Failed to read scenario result file:', file, e);
        }
      }
    }

    // Only keep the final result for each scenario (highest attempt number)
    // Group all attempts for each scenario
    // Group all attempts for each scenario (by name + feature)
    const scenarioAttemptsMap = new Map<string, ScenarioResult[]>();
    for (const result of scenarioResults) {
      const key = `${result.feature}||${result.name}`;
      if (!scenarioAttemptsMap.has(key)) scenarioAttemptsMap.set(key, []);
      scenarioAttemptsMap.get(key)!.push(result);
    }

    // For each scenario, determine the final result and if it had any failed attempt before the last
    const finalResults: ScenarioResult[] = [];
    for (const [_key, attemptsArr] of scenarioAttemptsMap.entries()) {
      // Sort attempts by attempt number (lowest to highest)
      const sortedAttempts = attemptsArr.slice().sort((a, b) => a.attempts - b.attempts);
      const finalAttempt = sortedAttempts[sortedAttempts.length - 1];
      // Check if any previous attempt failed
      const hadFailedAttempt = sortedAttempts.slice(0, -1).some(a => a.status === 'failed');
      // Set attempts to the true number of executions
      finalResults.push({ ...finalAttempt, hadFailedAttempt, attempts: sortedAttempts.length });
    }

    // Display enhanced final scenario summary with retry information
    if (finalResults.length > 0) {

      console.log('\n' + '='.repeat(100));
      console.log('📊 ENHANCED SCENARIO EXECUTION SUMMARY');
      console.log('='.repeat(100));

      const passedScenarios = finalResults.filter(s => s.status === 'passed');
      const failedScenarios = finalResults.filter(s => s.status === 'failed');
      const skippedScenarios = finalResults.filter(s => s.status === 'skipped');
      // Only count as retried if there was at least one failed attempt before the final result
      const retriedAndPassed = finalResults.filter(s => s.status === 'passed' && s.hadFailedAttempt);
      const retriedAndFailed = finalResults.filter(s => s.status === 'failed' && s.hadFailedAttempt);
      const retriedScenarios = [...retriedAndPassed, ...retriedAndFailed];

      console.log(`\n📈 TOTALS: ${finalResults.length} scenarios | ${passedScenarios.length} passed | ${failedScenarios.length} failed | ${skippedScenarios.length} skipped | ${retriedScenarios.length} retried (${retriedAndPassed.length} passed, ${retriedAndFailed.length} failed)\n`);

      // Enhanced retry statistics (use only the final attempt for each scenario)
      const avgAttempts = (1).toFixed(2); // Each scenario counted once, so always 1.00
      const retryRate = ((retriedScenarios.length / finalResults.length) * 100).toFixed(1);

      console.log('🔄 RETRY STATISTICS:');
      console.log(`   Total Scenarios: ${finalResults.length}`);
      console.log(`   Average Attempts per Scenario: ${avgAttempts}`);
      console.log(`   Retry Rate: ${retryRate}%`);
      console.log(`   Configuration Source: ${retryManager.getConfig().source}`);
      console.log(`   Max Retries Configured: ${retryManager.getConfig().maxRetries}`);
      console.log(`   Retry Delay: ${retryManager.getConfig().retryDelayMs}ms\n`);

      // Group by feature with retry information
      const byFeature = finalResults.reduce((acc: Record<string, ScenarioResult[]>, scenario) => {
        if (!acc[scenario.feature]) acc[scenario.feature] = [];
        acc[scenario.feature].push(scenario);
        return acc;
      }, {} as Record<string, ScenarioResult[]>);

      for (const [feature, scenarios] of Object.entries(byFeature)) {
        // Count retried-and-passed and retried-and-failed for this feature
        const featureRetriedAndPassed = scenarios.filter((s) => s.status === 'passed' && s.hadFailedAttempt).length;
        const featureRetriedAndFailed = scenarios.filter((s) => s.status === 'failed' && s.hadFailedAttempt).length;
        const featureRetries = featureRetriedAndPassed + featureRetriedAndFailed;
        console.log(`📁 ${feature} (${featureRetries} retried: ${featureRetriedAndPassed} passed, ${featureRetriedAndFailed} failed):`);
        scenarios.forEach((scenario) => {
          const icon = scenario.status === 'passed' ? '✅' : scenario.status === 'failed' ? '❌' : '⏭️';
          let retryInfo = ` (${scenario.attempts} attempt${scenario.attempts > 1 ? 's' : ''})`;
          if (scenario.hadFailedAttempt) {
            retryInfo = ` (retried, ${scenario.attempts} attempts)`;
          }
          const tagsInfo = scenario.tags && scenario.tags.length > 0 ? ` [${scenario.tags.join(', ')}]` : '';
          console.log(`   ${icon} ${scenario.name}${retryInfo}${tagsInfo}`);
        });
        console.log('');
      }

      console.log('='.repeat(100));

      // Clean up tracking after displaying the summary
      retryManager.clear();
      allScenarioResults.length = 0; // Clear the array

      // Optionally, clean up scenario result files
      try {
        for (const file of fs.readdirSync(resultsDir)) {
          fs.unlinkSync(path.join(resultsDir, file));
        }
        fs.rmdirSync(resultsDir);
        console.log('🧹 Temporary scenario result files cleaned up');
      } catch (cleanupErr) {
        console.log('[DEBUG] Failed to clean up scenario result files:', cleanupErr);
      }
    } else {
      console.log('⚠️  No scenario results found for summary display');
      // Still clean up even if no results
      retryManager.clear();
    }

    console.log('🏁 Enhanced test execution completed with comprehensive retry management');
  },
};