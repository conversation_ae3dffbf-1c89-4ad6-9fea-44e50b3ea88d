class CamelProductsPageObject {

    get btnfindmyblend_camel() { return $$('a[aria-label="FIND YOUR BLEND"]')[2]; }
    get hdrproductsdesc_camel() { return $('(//*[contains(@class,"cmp-hero-banner__description cmp-hero-ban")])[3]'); }

    get imgcrush_camel() { return $('a[aria-label="Camel Crush"] img'); }
    get imgclassic_camel() { return $$('a[aria-label="Camel Classics"] img')[1]; }
    get imgturkish_camel() { return $$('a[aria-label="Camel Turkish"] img')[1]; }
    get imgno9_camel() { return $$('a[aria-label="Camel No. 9"] img')[1]; }
    get imgredkamel_camel() { return $$('a[aria-label="Camel Red Kamel"] img')[1]; }
    get imgviewall_camel() { return $$('a[aria-label="View All"] img')[1]; }
    get lblcrush_camel() { return $('//*[text()="CRUSH"]'); }
    get lblclassic_camel() { return $('(//*[text()="CLASSICS"])[2]'); }
    get lblturkish_camel() { return $('(//*[text()="TURKISH"])[2]'); }
    get lblno9_camel() { return $('(//*[text()="NO. 9"])[2]'); }
    get lblredkamel_camel() { return $('(//*[text()="RED KAMEL"])[2]'); }
    get lblviewall_camel() { return $('(//*[text()="VIEW ALL"])[2]'); }

    get hdrcrush_camel() { return $('.cmp-hero-banner__body img'); }
    get hdrcrushdesc1_camel() { return $('.headline_wrapper'); }
    get hdrcrushdesc2_camel() { return $('.cmp-hero-banner__description'); }
    get imgcrushnonmentholproduct() { return $('a[aria-label="crush non-menthol"] img'); }
    get imgcrushmentholproduct_camel() { return $('a[aria-label="crush menthol"] img'); }
    get imgcrushsilverproduct_camel() { return $('a[aria-label="crush menthol silver"] img'); }
    get imgcrushrichproduct_camel() { return $('a[aria-label="crush rich"] img'); }
    get imgcrushsmoothproduct_camel() { return $('a[aria-label="crush smooth"] img'); }
    get imgcrushsmoothsilverproduct_camel() { return $('a[aria-label="crush smooth silver"] img'); }
    get lblcrushnonmentholproduct_camel() { return $('//h4[text()="Crush"]'); }
    get lblcrushmentholproduct_camel() { return $('//*[text()="crush menthol"]'); }
    get lblcrushsilverproduct_camel() { return $('//*[text()="Crush Menthol Silver"]'); }
    get lblcrushrichproduct_camel() { return $('//*[text()="crush rich"]'); }
    get lblcrushsmoothproduct_camel() { return $('//*[text()="crush smooth"]'); }
    get lblcrushsmoothsilverproduct_camel() { return $('//*[text()="crush smooth silver"]'); }
    get lblcrushtitle_camel() { return $('.cmp-product-detail__heroTitle'); }
    get lblproductimg_camel() { return $('.cmp-product-detail__packimg'); }
    get lbltakesyouto_camel() { return $('//*[contains(@class, "cmp-product-detail__description-mo")]//h2'); }
    get imgproductdesc_camel() { return $('.cmp-product-detail__description-mobile-img'); }
    get lblrelatedproduct_camel() { return $('.cmp-related-products__title'); }
    get imgrelatedproduct1_camel() { return $$('.cmp-related-products__content-item-link'); }
    get lbldecproductpage_camel() { return $('.cmp-product-detail__description-detail'); }
    get lnkbacktoall_camel() { return $('.cmp-product-detail__backBtn.cmp-product-detail__backBtn--logo-active'); }


    get imgclassicgoldproduct() { return $('a[aria-label="Classics Gold"] img'); }
    get imgclassicblueproduct() { return $('a[aria-label="Classic Blue"] img'); }
    get imgclassicfiltersproduct() { return $('a[aria-label="Classic Filters"] img'); }
    get imgclassicplatinumproduct() { return $('a[aria-label="Classics Platinum"] img'); }
    get imgclassicmentholproduct() { return $('a[aria-label="Classic Menthol"] img'); }
    get imgclassicwidesblueproduct() { return $('a[aria-label="Classic Wides Blue"] img'); }
    get imgclassicwidesfilterproduct() { return $('a[aria-label="Classic Wides Filters"] img'); }
    get imgclassicwidesmentholproduct() { return $('a[aria-label="Classic Wides Menthol"] img'); }
    get imgclassicwidesmentholsilverproduct() { return $('a[aria-label="Classic Wides Menthol Silver"] img'); }
    get lblclassicgoldproduct_camel() { return $('//*[text()="CLASSIC GOLD"]'); }
    get lblclassicblueproduct_camel() { return $('//*[text()="CLASSIC BLUE"]'); }
    get lblclassicfiltersproduct_camel() { return $('//*[text()="CLASSIC FILTERS"]'); }
    get lblclassicplatinumproduct_camel() { return $('//*[text()="CLASSIC PLATINUM"]'); }
    get lblclassicmentholproduct_camel() { return $('//*[text()="CLASSIC MENTHOL"]'); }
    get lblclassicwidesblueproduct_camel() { return $('//*[text()="CLASSIC WIDES BLUE"]'); }
    get lblclassicwidesfilterproduct_camel() { return $('//*[text()="CLASSIC WIDES FILTERS"]'); }
    get lblclassicwidesmentholproduct_camel() { return $('//*[text()="CLASSIC WIDES MENTHOL"]'); }
    get lblclassicwidesmentholsilverproduct_camel() { return $('//*[text()="CLASSIC WIDES MENTHOL SILVER"]'); }

    get imgturkishloyalproduct() { return $('a[aria-label="Turkish Royal"] img'); }
    get imgturkishgoldproduct() { return $('a[aria-label="Turkish Gold"] img'); }
    get imgturkishsilverproduct() { return $('a[aria-label="Turksish Silver"] img'); }
    get imgturkishjadeproduct() { return $('a[aria-label="Turkish Jade"] img'); }
    get imgturkishjadesilverproduct() { return $('a[aria-label="Turkish Jade Silver"] img'); }
    get lblturkishloyalproduct_camel() { return $('//*[text()="TURKISH ROYAL"]'); }
    get lblturkishgoldproduct_camel() { return $('//*[text()="TURKISH GOLD"]'); }
    get lblturkishsilverproduct_camel() { return $('//*[text()="TURKISH SILVER"]'); }
    get lblturkishjadeproduct_camel() { return $('//*[text()="TURKISH JADE"]'); }
    get lblturkishjadesilverproduct_camel() { return $('//*[text()="TURKISH JADE SILVER"]'); }
    get lnkbacktoallturkish_camel() { return $('.cmp-product-detail__backBtn'); }

    get imgno9product() { return $('img[alt="No. 9"]'); }
    get imgtno9mentheproduct() { return $('img[alt="No. 9 Menthe"]'); }
    get lblno9product_camel() { return $$('.cmp-product-list__product-title')[0]; }
    get lbltno9mentheproduct_camel() { return $$('.cmp-product-list__product-title')[1]; }


    get imgredkamelproduct() { return $('[alt="Red Kamel"]'); }
    get imgtredkamelsmoothproduct() { return $('[alt="Red Kamel Smooth"]'); }
    get lblredkamelproduct_camel() { return $('//h4[text()="Red Kamel"]'); }
    get lblredkamelsmoothproduct_camel() { return $('//h4[text()="Red Kamel Smooth"]'); }

}
export default new CamelProductsPageObject();