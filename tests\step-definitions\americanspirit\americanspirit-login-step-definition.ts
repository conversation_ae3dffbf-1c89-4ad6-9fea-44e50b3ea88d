import { Then } from '@wdio/cucumber-framework';
import NasLoginPage from '../../pages/americanspirit/americanspirit-loginpage.ts';
import logger from '../../support/utils/logger.util.ts';

Then(/^The user Validates Signin Page for Nas with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await NasLoginPage.loginPageValidation(filepath, sheetname, scenarioname);
    logger.info('Validated Login Page Successfully');
});