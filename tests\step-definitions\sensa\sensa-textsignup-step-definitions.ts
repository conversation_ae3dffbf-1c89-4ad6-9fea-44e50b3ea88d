import { When, Then } from '@wdio/cucumber-framework';
import sensaTextsignupPage from '../../pages/sensa/sensa-textsignup.page.ts';
import logger from '../../support/utils/logger.util.ts';
import sensaTextsignupPageObject from '../../page-object/sensa/sensa-textsignup.pageObject.ts';
import camelPromotionsPageObject from '../../page-object/camel/camel-promotions.pageObject.ts';

When(/^The user clicks on Text Signup link$/, async function () {
    await sensaTextsignupPage.clickontextSignUpLink();
    const url = browser.getUrl();
    if ((await url).includes('aem')) {
        await expect(sensaTextsignupPageObject.lblsignupTitle_sensa).toBeDisplayed();
    }
    logger.info('Navigated to Text Signup Page Successfully');
});

Then(/^The user Validates Text Sign-Up Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaTextsignupPage.signUpPageValidation(filepath, sheetname, scenarioname);
    logger.info('Validated to Text Signup Page Successfully');
});

Then(/The user enters valid Mobile Number as (.*) and validates success message as (.*)$/, async function (mobileNumber: string, success: string) {
    await sensaTextsignupPage.entervalidMobileNumberandvalidatesuccessmssg(mobileNumber, success);
    const url = browser.getUrl();
    if ((await url).includes('camel')) {
        const actualsuccessmessage = (await (await camelPromotionsPageObject.lblsuccess_camel.getText()).replace(/\s+/g, ' ').trim());
        await expect(actualsuccessmessage).toEqual(success.trim());
         await browser.execute(() => window.history.back());
    } else {
        await expect(sensaTextsignupPageObject.lblsuccessmssg_sensa).toHaveText(success);
    }
    logger.info('Validated Success Message Successfully');

});

Then(/The user enters invalid Mobile Number as (.*) and validates error message as (.*)$/, async function (mobileNumber: string, error: string) {
    await sensaTextsignupPage.enterinvalidMobileNumberandvalidateerror(mobileNumber, error);
    await expect(sensaTextsignupPageObject.lblvalidationError_sensa).toHaveText(error);
    logger.info('Validated Error Message Successfully');
});