/**
 * Test Execution Tracker - Helps identify duplicate test executions
 * This utility tracks and logs test execution details to help debug 
 * scenarios where tests run multiple times
 */

interface ExecutionRecord {
  executionId: string;
  scenarioName: string;
  featureName: string;
  environment: string;
  processId: number;
  startTime: number;
  endTime?: number;
  status?: 'passed' | 'failed' | 'running';
  attempt: number;
  sessionId?: string;
}

export class TestExecutionTracker {
  private static executions: Map<string, ExecutionRecord[]> = new Map();
  private static currentExecution: Map<string, ExecutionRecord> = new Map();

  /**
   * Start tracking a scenario execution
   */
  static startScenario(
    scenarioName: string,
    featureName: string,
    attempt: number = 1,
    sessionId?: string,
  ): string {
    const executionId = `${process.pid}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const environment = process.env.TEST_ENV || 'QA';
    
    const record: ExecutionRecord = {
      executionId,
      scenarioName,
      featureName,
      environment,
      processId: process.pid,
      startTime: Date.now(),
      status: 'running',
      attempt,
      sessionId,
    };

    // Store by scenario name for duplicate detection
    const scenarioKey = `${featureName}::${scenarioName}`;
    const executions = this.executions.get(scenarioKey) || [];
    executions.push(record);
    this.executions.set(scenarioKey, executions);
    
    // Track current execution
    this.currentExecution.set(executionId, record);

    // Check for potential duplicates
    this.checkForDuplicates(scenarioKey, record);

    console.log(`🎬 EXECUTION STARTED [${executionId}]:`);
    console.log(`   Scenario: ${scenarioName}`);
    console.log(`   Feature: ${featureName}`);
    console.log(`   Environment: ${environment}`);
    console.log(`   Attempt: ${attempt}`);
    console.log(`   Process: ${process.pid}`);
    console.log(`   Session: ${sessionId || 'N/A'}`);
    console.log(`   Total executions for this scenario: ${executions.length}`);

    return executionId;
  }

  /**
   * End tracking a scenario execution
   */
  static endScenario(
    executionId: string,
    status: 'passed' | 'failed',
  ): void {
    const record = this.currentExecution.get(executionId);
    if (!record) {
      console.warn(`⚠️  No execution record found for ID: ${executionId}`);
      return;
    }

    record.endTime = Date.now();
    record.status = status;
    
    const duration = record.endTime - record.startTime;
    const statusIcon = status === 'passed' ? '✅' : '❌';

    console.log(`${statusIcon} EXECUTION ENDED [${executionId}]:`);
    console.log(`   Status: ${status.toUpperCase()}`);
    console.log(`   Duration: ${duration}ms`);
    console.log(`   Attempt: ${record.attempt}`);

    // Remove from current executions
    this.currentExecution.delete(executionId);
  }

  /**
   * Check for potential duplicate executions
   */
  private static checkForDuplicates(scenarioKey: string, currentRecord: ExecutionRecord): void {
    const executions = this.executions.get(scenarioKey) || [];
    
    // Filter executions from the last 10 minutes
    const recentExecutions = executions.filter(
      exec => (Date.now() - exec.startTime) < 10 * 60 * 1000,
    );

    if (recentExecutions.length > 1) {
      console.warn('🚨 POTENTIAL DUPLICATE EXECUTION DETECTED:');
      console.warn(`   Scenario: ${currentRecord.scenarioName}`);
      console.warn(`   Feature: ${currentRecord.featureName}`);
      console.warn(`   Recent executions (last 10 minutes): ${recentExecutions.length}`);
      
      recentExecutions.forEach((exec, index) => {
        const timeAgo = Math.round((Date.now() - exec.startTime) / 1000);
        const statusText = exec.status || 'running';
        console.warn(`     ${index + 1}. [${exec.executionId}] ${statusText} - ${timeAgo}s ago (PID: ${exec.processId}, Env: ${exec.environment}, Attempt: ${exec.attempt})`);
      });

      // Check for same environment duplicates
      const sameEnvExecutions = recentExecutions.filter(
        exec => exec.environment === currentRecord.environment,
      );
      
      if (sameEnvExecutions.length > 1) {
        console.error(`❌ DUPLICATE EXECUTION IN SAME ENVIRONMENT (${currentRecord.environment}):`);
        console.error('   This indicates a potential issue with test execution logic!');
      }
    }
  }

  /**
   * Get execution summary for debugging
   */
  static getExecutionSummary(): void {
    console.log('\n📊 EXECUTION SUMMARY:');
    console.log('='.repeat(80));
    
    for (const [scenarioKey, executions] of this.executions.entries()) {
      const [featureName, scenarioName] = scenarioKey.split('::');
      console.log(`\n🎭 ${scenarioName} (${featureName}):`);
      
      const groupedByEnv = executions.reduce((acc, exec) => {
        const env = exec.environment;
        if (!acc[env]) acc[env] = [];
        acc[env].push(exec);
        return acc;
      }, {} as Record<string, ExecutionRecord[]>);

      for (const [env, envExecutions] of Object.entries(groupedByEnv)) {
        console.log(`   ${env}: ${envExecutions.length} executions`);
        
        envExecutions.forEach(exec => {
          const duration = exec.endTime ? `${exec.endTime - exec.startTime}ms` : 'running';
          const statusIcon = exec.status === 'passed' ? '✅' : exec.status === 'failed' ? '❌' : '🔄';
          console.log(`     ${statusIcon} Attempt ${exec.attempt} - ${duration} (PID: ${exec.processId})`);
        });
      }
    }
    
    console.log('\n' + '='.repeat(80));
  }

  /**
   * Clear execution history (useful for testing)
   */
  static clear(): void {
    this.executions.clear();
    this.currentExecution.clear();
    console.log('🧹 Execution tracker cleared');
  }

  /**
   * Export execution data for analysis
   */
  static exportExecutionData(): Record<string, ExecutionRecord[]> {
    return Object.fromEntries(this.executions.entries());
  }
}
