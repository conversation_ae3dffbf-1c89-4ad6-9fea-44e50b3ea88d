import { Then, When } from '@wdio/cucumber-framework';
import camelHomePage from '../../pages/camel/camel-home.page.ts';
import camelLoginPageObject from '../../page-object/camel/camel-login.pageObject.ts';
import logger from '../../support/utils/logger.util.ts';
import camelHomepagePageObject from '../../page-object/camel/camel-homepage.pageObject.ts';

Then(/^The user Validates Camel Homepage Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await camelHomePage.homePageValidation(filepath, sheetname, scenarioname);
    await camelHomePage.fedbannerValidation(filepath, sheetname, scenarioname);
    await expect(camelLoginPageObject.lbllogo_camel).toBeDisplayed();
    logger.info('User Validates Camel Tobacco Rights Page');
});



When(/^The user clicks on Products$/, async function () {
    await camelHomePage.navigatetoProductspage();
    await expect(camelHomepagePageObject.lblproductpageheader_camel).toBeDisplayed();
    logger.info('Navigated to Products Page Successfully');
});
