import { Then, When } from '@wdio/cucumber-framework';
import AccountPage from '../../pages/commonteps/account.page.ts';
import AccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';
import logger from '../../support/utils/logger.util.ts';

When(/^The user login with valid user id (.*) and password (.*)$/, async function (username: string, password: string) {
    await AccountPage.loginwithcredential(username, password);
    logger.info('Logged In Successfully');
});

Then(/^The user click on MyAccount link$/, async function () {
    await AccountPage.clickonaccountlinkfromheader();
    await expect(AccountPageObject.lblaccountheader_sensa).toBeDisplayed();
    logger.info('Navigated to Account Page Successfully');

});
When(/^The user click on Contact Info link$/, async function () {
    await AccountPage.clickoncontactinfo();
    await expect(AccountPageObject.lbllegalName_sensa).toBeDisplayed();
    logger.info('Navigated to Contact Info Page Successfully');

});
When(/^The user enters Mobile Number (.*) Current Address (.*) ZipCode (.*) in ContactInfo page$/, async function (mobile: string, currentaddr: string, zipcode: string) {
    await AccountPage.contactinfoUpdate(mobile, currentaddr, zipcode);
    await expect(AccountPageObject.txtcurrentAddr_sensa).toHaveValue(currentaddr);
    await expect(AccountPageObject.txtzipCode_sensa).toHaveValue(zipcode);
    await expect(AccountPageObject.txtmobile_sensa).toHaveValue(mobile);
    logger.info('Entered Details Successfully');

});

Then(/^The user validates the successfull message (.*)$/, async function (successmessage: string) {
    await AccountPage.contactinfoUpdateMessage(successmessage);
    await expect(AccountPageObject.txtsuccessmessage_sensa).toHaveText(successmessage);
    logger.info('Success Message is Displayed Successfully');

});
Then(/^The user validates the contact info page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await AccountPage.contactinfoValidation(filepath, sheetname, scenarioname);
    await expect(AccountPageObject.btnupdate_sensa).toBeDisplayed();
    logger.info('Validated Contact Info Page Successfully');

});
Then(/^The user click on Email link$/, async function () {
    await AccountPage.cickonemailpagelink();
    await expect(AccountPageObject.lblcurrentEmail_sensa).toBeDisplayed();
    logger.info('Navigated to Email Page Successfully');

});
When(/^The user enters with Username (.*) and Newemail (.*) and validate error message (.*)$/, async function (newEmail: string, confirmEmail: string, errormssg: string) {
    await AccountPage.emailerrorMssg(newEmail, confirmEmail, errormssg);
    await expect(AccountPageObject.lblemailerrormssg_sensa).toHaveText(errormssg);
    logger.info('Validated Error Message Successfully');
});
Then(/^The user enters Newemail (.*) and validate success message (.*)$/, async function (newEmail: string, successmssg: string) {
    await AccountPage.emailupdatesuccessfull(newEmail, successmssg);
    await expect(AccountPageObject.lblemailsuccessmssg_sensa).toHaveText(successmssg);
    logger.info('Email Updated Successfully');

});
Then(/^The user validates Email Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await AccountPage.emailPageValidation(filepath, sheetname, scenarioname);
    logger.info('Validated Email Page Successfully');

});

When(/^The user selects following brands:$/, async function (dataTable) {
    const brands = dataTable.raw().flat().slice(1);
    for (const brand of brands) {
        const Checkbox = await AccountPageObject.gettxtcheckbox_sensa(brand);
        Checkbox.scrollIntoView();
        await browser.execute('arguments[0].click();', Checkbox);
    }
    logger.info('Selected Preffered Subscription Successfully');
});
Then(/^The user validates success message (.*)$/, async function (successmessage: string) {
    await AccountPage.subscriptionupdatesuccessfull(successmessage);
    await expect(AccountPageObject.lblsubscriptionsuccessmssg_sensa).toHaveText(successmessage);
    logger.info('Subscriptions Updated Successfully');

});
Then(/^The user validates login error message (.*)$/, async function (errormessage: string) {
    await AccountPage.loginErrorMessage(errormessage);
    await expect(AccountPageObject.lblloginerror_sensa).toBeDisplayed();
    logger.info('Validated Error Message Successfully');

});

When(/^The user click on Security link$/, async function () {
    await AccountPage.clickonsecuritypagelink();
    await expect(AccountPageObject.txtcurrentPassword_sensa).toBeDisplayed();
    logger.info('Navigated to Security Page Successfully');

});

Then(/^The user validates Security Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await AccountPage.securityPageValidation(filepath, sheetname, scenarioname);
    logger.info('Validated Security Page Successfully');

});

Then(/^The user enters Current Password (.*) New Password (.*) and Confirm Password (.*) and validates error message(.*)$/, async function (currentpassword: string, newpassword: string, confirmpassword: string, errormessage: string) {
    await AccountPage.passworderrorMssg(currentpassword, newpassword, confirmpassword, errormessage);
    await expect(AccountPageObject.lblpassworderrormssg_sensa).toHaveText(errormessage);
    logger.info('Validated Error Message Successfully');

});
Then(/^The user enters Current Password (.*) and New Password same as current password and validates error message(.*)$/, async function (currentpassword: string, errormessage: string) {
    await AccountPage.samepassworderrorMssg(currentpassword, errormessage);
    await expect(AccountPageObject.lblsamepassworderrormssg_sensa).toHaveText(errormessage);
    logger.info('Validated Error Message Successfully');


});

Then(/^The user enters Current Password (.*) New Password (.*) and Confirm Password and validates success message(.*)$/, async function (currentpassword: string, newpassword: string, successmssg: string) {
    await AccountPage.passwordsuccessMssg(currentpassword, newpassword, successmssg);
    await expect(AccountPageObject.lblpasswordsuccessmssg_sensa).toHaveText(successmssg);
    logger.info('Password Updated Successfully');

});

When(/^The user selects Security Question (.*) and enters Answer (.*) and validates success message (.*)$/, async function (securityquestion: string, answer: string, successmssg: string) {
    await AccountPage.securityquestionupdate(securityquestion, answer, successmssg);
    await expect(AccountPageObject.lblsecuritysuccessmssg_sensa).toHaveText(successmssg);
    logger.info('Security Question Updated Successfully');

});


When(/^The user click on Tobacco Preferences link$/, async function () {
    await AccountPage.clickontobaccoPreferencesPagelink();
    await expect(AccountPageObject.lbltellus_sensa).toBeDisplayed();
    logger.info('Navigated to Tobacco Preferences Successfully');

});
When(/^The user click on (.*) product checkbox$/, async function (product: string) {
    await AccountPage.tobaccoprferenceproductselection(product);
    await expect(AccountPageObject.gettxtproductcheckbox_sensa(product)).toBeDisplayed();
    logger.info('Selected Tobacco Products');
});

When(/^The user selects brand (.*) flavour (.*) last purchases (.*) and prefer nicotine (.*) and regular cur (.*) if present based on (.*)$/, async function (brand: string, flavour: string, purchases: string, preferednicotine: string, regularcut: string, product: string) {
    await AccountPage.tobaccoprferenceupdate(brand, flavour, purchases, preferednicotine, regularcut, product);
    logger.info('Updated Tobacco Preferences');

});

Then(/^The user validates the success message (.*)$/, async function (successmssg: string) {
    await AccountPage.tobaccoSuccessMessage(successmssg);
    await expect(AccountPageObject.lbltobaccoSurveymssg_sensa).toHaveText(successmssg);
    logger.info('validated the success message Successfully');

});

When(/^The user click on Retailer Account$/, async function () {
    await AccountPage.clickonretailAccountpagelink();
    await expect(AccountPageObject.txtrewardscheckbox_sensa).toBeDisplayed();
    logger.info('Navigated to Retailer Account Page Successfully');

});
Then(/^The user links the Retailer loyalty and verifies the success message (.*)$/, async function (successmssg: string) {
    await AccountPage.retailAccountoptin(successmssg);
    await expect(AccountPageObject.lbllinksuccessmssg_sensa).toHaveText(successmssg);
    logger.info('Opted in  Successfully');

});
Then(/^The user unlinks the Retailer loyalty and verifies the unlinked message (.*)$/, async function (successmssg: string) {
    await AccountPage.retailAccountoptout(successmssg);
    await expect(AccountPageObject.lblunlinksuccessmssg_sensa).toHaveText(successmssg);
    logger.info('Opted out Successfully');

});

When(/^The user click on Coupons link$/, async function () {
    await AccountPage.clickoncouponsPagelink();
    await expect(AccountPageObject.lblavailableCoupons_sensa).toBeDisplayed();
    logger.info('Navigated to Coupons Page Successfully');

});

When(/^The user validates Coupons Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await AccountPage.couponsPageValidation(filepath, sheetname, scenarioname);
    logger.info('Validated to Coupons Page Successfully');

});
Then(/^The user validates the page with filepath (.*) sheet name (.*) and scenario name (.*) based on (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string, product: string) {
    await AccountPage.tobaccoPreferencesValidation(filepath, sheetname, scenarioname, product);
    logger.info('Validated to tobacco Preferences Successfully');

});



