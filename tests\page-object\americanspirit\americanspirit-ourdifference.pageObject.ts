class NasOurDiffernecPageObject {

    get Nas_ourDifferenceImg() { return $('[src="/content/dam/nas/website/our-difference-2025/this-is-our-difference-d.png"]'); }
    get Nas_mobileSubMenue() { return $('.cmp-custom-navigation__mobile-toggle'); }
    get Nas_discoverTitle() { return $('[title="Discover"]'); }
    get Nas_ourPartners() { return $('[title="Our Partners"]'); }
    get Nas_ourTobaccoTitle() { return $('[title="Our Tobacco"]'); }
    get Nas_ourProcessTitle() { return $('[title="Our Process"]'); }
    get Nas_buttPouchesTitle() { return $('[title="Butt Pouches"]'); }
    get Nas_earthDayTitle() { return $('[title="Earth Day"]'); }
    get Nas_ourUncompromisingPara() { return $('(//p[contains(text(),\'Our uncompromising\')])[2]'); }
    get Nas_andTheTobacco() { return $('(//h5[contains(text(),\'AND THE TOBACCO WE ARE\')])[2]'); }
    get Nas_CraftingOrganic() { return $('(//h5[@style="text-align: center;"])[4]'); }
    get Nas_Zerowaste() { return $('(//h5[@style="text-align: center;"])[6]'); }
    get Nas_DifferenceForest() { return $('(//h5[@style="text-align: center;"])[8]'); }
    get Nas_learnMore_1() { return $('(//span[text()=\'LEARN MORE\'])[1]'); }
    get Nas_learnMore_2() { return $('(//span[text()=\'LEARN MORE\'])[2]'); }
    get Nas_learnMore_3() { return $('(//span[text()=\'LEARN MORE\'])[3]'); }
    get Nas_learnMore_4() { return $('(//span[text()=\'LEARN MORE\'])[4]'); }
    get Nas_learnMore_5() { return $('(//span[text()=\'LEARN MORE\'])[5]'); }
    get Nas_learnMore_6() { return $('(//span[text()=\'LEARN MORE\'])[6]'); }
    get Nas_learnMore_7() { return $('(//span[text()=\'LEARN MORE\'])[7]'); }
    get Nas_learnMore_8() { return $('(//span[text()=\'LEARN MORE\'])[8]'); }
    get Nas_exploreLike_2() { return $('(//*[contains(text(),\'Explore like \')])[2]'); }
    get Nas_wantToKnowCraft_2() { return $('(//*[contains(text(),\'Want to know exactly how we craft\')])[2]'); }
    get Nas_checkItOut_2() { return $('(//*[contains(text(),\'Check it Out\')])[2]'); }
    get Nas_catchUpHeadlineDesktopImg() { return $('[src="/content/dam/nas/website/our-difference-2025/NAS-DOD-CatchUpHeadline-Desktop.png"]'); }
    get Nas_learnMoreAboutWhat() { return $('(//*[contains(text(),\'Learn more about what\')])'); }
    get Nas_ourTobaccoText() { return $('(//*[contains(text(),\'OUR TOBACCO\')])'); }
    get Nas_uniquelyCrafted() { return $('(//*[contains(text(),\'Uniquely Crafted\')])'); }
    get Nas_fromLandToYourHand() { return $('(//*[contains(text(),\'From the land to your hand\')])'); }
    get Nas_doesOrganicTobaccoTasteDiffere() { return $('(//*[contains(text(),\'DOES ORGANIC TOBACCO TASTE DIFFERE\')])'); }
    get Nas_organicTobaccoDeliversTrue() { return $('(//*[contains(text(),\'Organic tobacco delivers a true\')])'); }
    get Nas_ourProcess_1() { return $('(//*[contains(text(),\'OUR PROCESS\')])[1]'); }
    get Nas_sustainability_1() { return $('(//*[contains(text(),\'Sustainability\')])[1]'); }
    get Nas_exploreOurInitiatives_1() { return $('(//*[contains(text(),\'Explore our initiatives to\')])[1]'); }

    get Nas_cardFourFront() { return $('[alt="Card four - FRONT"]'); }
    get Nas_cardOneFront() { return $('[src="/content/dam/nas/website/our-difference-2025/NAS-DOD-ButtPouchTile-Desktop.png"]'); }

    get Nas_extraTobaccoDustText() { return $('//*[contains(text(),\'Our extra tobacco dust is composted t\')]'); }
    get Nas_exploreOurProcessText() { return $('//*[contains(text(),\'EXPLORE OUR PROCESS\')]'); }
    get Nas_ourPartnersText() { return $('//*[contains(text(),\'OUR PARTNERS\')]'); }
    get Nas_togetherForNatureText() { return $('//*[contains(text(),\'Together for Nature\')]'); }
    get Nas_weCantDoThisAloneText() { return $('//*[contains(text(),\'We can’t do this alone\')]'); }
    get Nas_partnerTerraCycleText() { return $('//*[contains(text(),\'Our partner TerraCycle will sort and\')]'); }

    get Nas_uniquelyCraftedImg() { return $('[alt="Uniquely crafted"]'); }
    get Nas_partnersHeading() { return $('//*[text()=\'Partners\']'); }
    get Nas_findYourFavoritePack() { return $('//*[text()=\'Find Your Favorite Pack\']'); }
    get Nas_cleanUpWithButtPouches() { return $('//*[text()=\'Clean Up With Butt Pouches\']'); }

    //OurPartners

    get Nas_teamingUpCare_2() { return $('(//*[contains(text(),\'TEAMING UP TO CARE FOR\')])[2]'); }
    get Nas_textAlignCenter_5() { return $('(//*[@style="text-align: center;"])[5]'); }

    get Nas_keepAmericaBeautiful() { return $('(//*[contains(text(),\'KEEP AMERICA BEAUTIFUL\')])'); }

    get Nas_toggleArrow_1() { return $$('[class="cmp-reveal-hide__toggle-arrow"]')[0]; }
    get Nas_toggleArrow_2() { return $$('[class="cmp-reveal-hide__toggle-arrow"]')[1]; }
    get Nas_toggleArrow_3() { return $$('[class="cmp-reveal-hide__toggle-arrow"]')[2]; }

    get Nas_imageIndex_2() { return $$('[class="cmp-image__img"]')[1]; }
    get Nas_imageIndex_3() { return $$('[class="cmp-image__img"]')[2]; }
    get Nas_imageIndex_4() { return $$('[class="cmp-image__img"]')[3]; }
    get Nas_imageIndex_5() { return $$('[class="cmp-image__img"]')[4]; }
    get Nas_imageIndex_6() { return $$('[class="cmp-image__img"]')[5]; }
    get Nas_imageIndex_7() { return $$('[class="cmp-image__img"]')[6]; }
    get Nas_imageIndex_8() { return $$('[class="cmp-image__img"]')[7]; }
    get Nas_imageIndex_9() { return $$('[class="cmp-image__img"]')[8]; }

    get Nas_leadingNationalText() { return $('(//*[contains(text(),\'A leading national n\')])'); }
    get Nas_keyInitiatives_1() { return $('(//*[contains(text(),\'KEY INITIATIVES\')])[1]'); }
    get Nas_greatAmericanCleanup_2() { return $('(//*[contains(text(),\'Great American Cleanup\')])[2]'); }
    get Nas_americaRecyclesDay_2() { return $('(//*[contains(text(),\'America Recycles Day\')])[2]'); }
    get Nas_cigarettePrevention_2() { return $('(//*[contains(text(),\'The Cigarette Prevention\')])[2]'); }
    get Nas_visitPartner_2() { return $('(//*[contains(text(),\'VISIT PARTNER\')])[2]'); }

    get Nas_keepAmericaBeautiful_3() { return $('(//*[contains(text(),\'Keep America Beautiful\')])[3]'); }
    get Nas_rodaleInstitute() { return $('(//*[contains(text(),\'RODALE INSTITUTE\')])'); }
    get Nas_furtheringOrganic_1() { return $('(//*[contains(text(),\'Furthering the organic m\')])[1]'); }
    get Nas_keyInitiatives_2() { return $('(//*[contains(text(),\'KEY INITIATIVES\')])[2]'); }
    get Nas_helpingFarmers_2() { return $('(//*[contains(text(),\'Helping farmers go organic\')])[2]'); }
    get Nas_researchingFarming_2() { return $('(//*[contains(text(),\'Researching organic farming methods\')])[2]'); }
    get Nas_educatingConsumers_2() { return $('(//*[contains(text(),\'Educating consumers on the benefits of organic\')])[2]'); }
    get Nas_visitPartnerRodale_2() { return $('(//*[contains(text(),\'VISIT PARTNER\')])[2]'); }

    get Nas_furtheringOrganic_3() { return $('(//*[contains(text(),\'Furthering the organic moveme\')])[3]'); }
    get Nas_terracycle() { return $('(//*[contains(text(),\'TERRACYCLE\')])'); }
    get Nas_recyclingPioneer() { return $('(//*[contains(text(),\'A recycling pioneer that began in\')])'); }
    get Nas_keyInitiatives_3() { return $('(//*[contains(text(),\'KEY INITIATIVES\')])[3]'); }
    get Nas_recyclingPlatforms_2() { return $('(//*[contains(text(),\'Recycling platforms that allow people to\')])[2]'); }
    get Nas_pioneersOfLoop_2() { return $('(//*[contains(text(),\'Pioneers of Loop, a\')])[2]'); }
    get Nas_visitPartner_3() { return $('(//*[contains(text(),\'VISIT PARTNER\')])[3]'); }
    get Nas_terraCyclePrograms_3() { return $('(//*[contains(text(),\'TerraCycle offers free programs\')])[3]'); }

    get Nas_ourTobacco_bold() { return $('//b[text()=\'OUR TOBACCO\']'); }
    get Nas_ourProcess_bold() { return $('//b[text()=\'OUR process\']'); }
    get Nas_ourPartners_bold() { return $('//b[text()=\'OUR partners\']'); }
    get Nas_thanksForDisposing_2() { return $('(//*[text()=\'Thanks for disposing of your Cigarette butts properly.\'])[2]'); }





}
export default new NasOurDiffernecPageObject();