import { When, Then, Given } from '@wdio/cucumber-framework';
import Registration from '../../pages/commonteps/registration.page.ts';
import logger from '../../support/utils/logger.util.ts';
import sensaRegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';
import sensaForgotusernamePageObject from '../../page-object/sensa/sensa-forgotusername.pageObject.ts';


Given(/^The user is on the login page for (.*) with login (.*)$/, async function (brand: string, url: string) {
    await Registration.navigateToUrl(url);
    await expect(sensaRegistrationPageObject.lblsignIn_sensa).toBeDisplayed();
    logger.info('Entered URl Successfully');
});

Then(/^The user clicks on the Join Now$/, async function () {
    await Registration.clickonjoinNowButton();
    await expect(sensaRegistrationPageObject.lblregistrationPage_sensa).toBeDisplayed();
    logger.info('Entered URl Successfully');
});

When(/^The user enters the valid details added in verated data and click continue button$/, async function () {
    await Registration.enterDetailsintellUsaboutpage();
    await expect(sensaRegistrationPageObject.lblaccountSetup_sensa).toBeDisplayed();
    logger.info('Entered the valid details added in verated data');
});

Then(/^The user enters user details in the Account Set up page with Password (.*) Confirm Password (.*) Security Question (.*) Security answer (.*) in the Account setup page$/, async function (password: string, Cpassword: string, securityquestion: string, securityanswer: string) {
    await Registration.enterDetailsinaccountSetupPage(password, Cpassword, securityquestion, securityanswer);
    logger.info('Entered the valid details in Account Setup Page');
});

When(/^The user validates and enter the phone number (.*) in the popup$/, async function (mobileNumber: string) {
    await Registration.enterMobileNumberinverifyIdentityPage(mobileNumber);
    logger.info('Entered the Mobile Number in Identity Page');
});

Then(/^The user enters valid details Verify your identity page$/, async function () {
    await Registration.enterSSNinverifyssnPage();
    logger.info('Entered the SSN in Verify Your Identity Page');
});

Then(/^The user validate signup page page$/, async function () {
    await Registration.navigatetoSignupPageandClickOnNoThanksLink();
    await expect(sensaRegistrationPageObject.lblcongratulations_sensa).toBeDisplayed();
    logger.info('User validate signup page');
});

Then(/^The user validate congratulation message page$/, async function () {
    await Registration.navigateTocongratulationsPageandClickOnTakemeToSiteLink();
    await expect(sensaRegistrationPageObject.lbllogo_sensa).toBeDisplayed();
    logger.info('Navigated to Home page');
});

Then(/^The user should be able to login to the application successfully$/, async function () {
    await Registration.NavigatehomepageandverifySensaLogo();
    await expect(sensaRegistrationPageObject.lbllogo_sensa).toBeDisplayed();
    logger.info('User Logged In Successfully');
});

Then(/^The user validates that successfully logged out$/, async function () {
    await Registration.successfullLogoutfromSensasite();
    await expect(sensaRegistrationPageObject.lblsignIn_sensa).toBeDisplayed();
    logger.info('User Logged Out Successfully');
});

When(/^The user enters email as (.*)$/, async function (email: string) {
    await Registration.entersEMailinemailField(email);
    logger.info('User Entered Email Successfully');
});

Then(/^The user navigates to signin page and validates error message (.*)$/, async function (mssg: string) {
    await Registration.verifyRegistrationerrorinLoginPage(mssg);
    await expect(sensaRegistrationPageObject.lblregistrationError_sensa).toHaveText(mssg);
    logger.info('User Validated Error Message Successfully');
});

Then(/^The user navigates to signin page and validates message (.*)$/, async function (mssg: string) {
    await Registration.verifyifaccountexistandvalidatemessage(mssg);
    await expect(sensaRegistrationPageObject.lblregistrationsameBrand_sensa).toHaveText(mssg);
    logger.info('User Validated Error Message Successfully');
});

Then(/^The user validates Tell us Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await Registration.tellUsaboutpageValidationinRegistration(filepath, sheetname, scenarioname);
    await expect(sensaRegistrationPageObject.txtcontinuetoNextStep_sensa).toBeDisplayed();
    logger.info('User Validated Tell Us Page Successfully');
});

Then(/^The user validates Account Setup Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await Registration.accountsetupPagevalidation(filepath, sheetname, scenarioname);
    await expect(sensaRegistrationPageObject.txtcontinuetonextStep_sensa).toBeDisplayed();
    logger.info('User Validated Account Set Up Page Successfully');
});
Then(/^The user validates Verify your identity page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await Registration.verifyssnpageValidation(filepath, sheetname, scenarioname);
    logger.info('User Validated SSN Page Successfully');
});
Then(/^The user validates Signup with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await Registration.signuppageValidation(filepath, sheetname, scenarioname);
    await expect(sensaRegistrationPageObject.lnknoThanks_sensa).toBeDisplayed();
    logger.info('User Validated Signup Page Successfully');
});
Then(/^The user validates Errors in Tell us Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await Registration.tellUsaboutpageAllErrorsvalidation(filepath, sheetname, scenarioname);
    await expect(sensaForgotusernamePageObject.lblerrorYear_sensa).toBeDisplayed();
    logger.info('User Validated Errors in Tell us Page Successfully');
});
Then(/^The user validates Errors in Account Setup Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await Registration.accountsetuperrorvalidation(filepath, sheetname, scenarioname);
    await expect(sensaRegistrationPageObject.lblsecurityAnswerError_sensa).toBeDisplayed();
    logger.info('User Validated Errors in Account Setup Page Successfully');
});
Then(/^The user enters Password as (.*) and confirm Password (.*) and validates error message (.*)$/, async function (password: string, confirmpassword: string, errormssg: string) {
    await Registration.passwordFieldErrorValidation(password, confirmpassword, errormssg);
    await expect(sensaRegistrationPageObject.lblconfirmpasswordError_sensa).toHaveText(errormssg);
    logger.info('Validated Error Message Successfully');
});

When(/^The user enters first Name as (.*) Last Name as (.*) street address as (.*) Zipcode as (.*) City as (.*) and DOB as (.*)$/, async function (firstname: string, lastname: string, address: string, zipcode: string, city: string, dob: string) {
    await Registration.tellUsaboutpageDetailsindcamflow(firstname, lastname, address, zipcode, city, dob);
    logger.info('Entered Details Successfully');
});

