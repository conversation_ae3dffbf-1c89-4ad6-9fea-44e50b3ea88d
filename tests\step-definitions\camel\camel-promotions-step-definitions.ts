import { When, Then } from '@wdio/cucumber-framework';
import camelPromotionsPage from '../../pages/camel/camel-promotions.page.ts';
import camelPromotionsPageObject from '../../page-object/camel/camel-promotions.pageObject.ts';
import logger from '../../support/utils/logger.util.ts';

When(/^The user clicks on Promotions$/, async function () {
    await camelPromotionsPage.promotionsPage();
    await expect(camelPromotionsPageObject.btnsignup_camel).toBeDisplayed();
    logger.info('Navigated to Coupons Page Successfully');
});

Then(/^The user Validates Promotions Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await camelPromotionsPage.promotionsPageValidation(filepath, sheetname, scenarioname);
    await expect(camelPromotionsPageObject.hdrcomments_camel).toBeDisplayed();
   logger.info('Validated to Coupons Page Successfully');
});

Then(/^The user enters valid comments as (.*) and verify last comment timestap as (.*)$/, async function (comment: string, timestamp: string) {
    await camelPromotionsPage.commentverification(comment, timestamp);
    await expect(camelPromotionsPageObject.lblcommentmssg_camel).toHaveText(comment);
    logger.info('Validated to Comments Successfully');

});

