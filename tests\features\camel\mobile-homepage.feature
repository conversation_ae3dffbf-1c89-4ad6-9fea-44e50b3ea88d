Feature: Home Page Validation for Camel Website

    Scenario Outline: Validate Home Page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        Then The user Validates Camel Homepage Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user validates that successfully logged out of camel brand

        @CamelHomePage_Validation_QA
        Examples:
            | Brand | URL                         | Username                           | Password  | filename              | sheetname | scenarioname       |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | HomePage  | Validate Home Page |

        @CamelHomePage_Validation_PROD
        Examples:
            | Brand | URL                   | Username                                 | Password  | filename              | sheetname | scenarioname       |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | HomePage  | Validate Home Page |