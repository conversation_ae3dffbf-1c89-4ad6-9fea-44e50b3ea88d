class HumpPageObject {

    get lnkhump_camel() { return $('//*[@title="The Hump"]'); }
    get lnkall_camel() { return $('(//*[@class="cmp-article-list__filter-list"]//button)[1]'); }
    get lnknightout_camel() { return $('(//*[@class="cmp-article-list__filter-list"]//button)[2]'); }
    get lnkdiscoversound_camel() { return $('(//*[@class="cmp-article-list__filter-list"]//button)[3]'); }
    get lnkadventurescale_camel() { return $('(//*[@class="cmp-article-list__filter-list"]//button)[4]'); }
    get lnkorigins_camel() { return $('(//*[@class="cmp-article-list__filter-list"]//button)[5]'); }
    get lnkdarelist_camel() { return $('(//*[@class="cmp-article-list__filter-list"]//button)[6]'); }
    get lnkterritory_camel() { return $('(//*[@class="cmp-article-list__filter-list"]//button)[7]'); }
    get lnknewbienatural_camel() { return $('(//*[@class="cmp-article-list__filter-list"]//button)[8]'); }

    getlbllike_camel(i: number) { return $(`(//*[@class="article-teaser__like-comment-container"]//p[1])[${i}]`); }
    get lbltitle_camel() { return $$('(//*[@class="article-teaser__title-link"])'); }
    get lnkalllinks_camel() { return $$('(//*[@class="cmp-article-list__filter-list"]//button)'); }
    get lblbuttons_camel() { return $$('(//*[@class="article-teaser__action-container"])//a'); }
    getlblcomment_camel(i: number) { return $(`(//*[@class="article-teaser__like-comment-container"]//p[2])[${i}]`); }
    getlbltitle_camel(i: number) { return $(`(//*[@class="article-teaser__title-link"])[${i}]`); }
    getlbltitledesc_camel(i: number) { return $(`(//*[@class="article-teaser__description"])[${i}]`); }
    getlblbuttons_camel(i: number) { return $(`(//*[@class="article-teaser__action-container"]//a)[${i}]`); }
    get lbleachPagetitle_camel() { return $('(//*[@class="cmp-title__text"])[1]'); }
    get btnloadmore_camel() { return $('(//*[@class="cmp-button__text cmp-article-list__load-more-text"])'); }


}
export default new HumpPageObject();