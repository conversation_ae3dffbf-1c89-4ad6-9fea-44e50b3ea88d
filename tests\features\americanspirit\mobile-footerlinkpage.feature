Feature: Footerlink Pages Validation for Nas Website

    Scenario Outline: Vaidate FAQ footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on FAQ footerlink
        Then The user Validates Nas FAQ Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @NasPreLoginFAQFooterlink_Validation_QA
        Examples:
            | Brand | URL                                  | filename            | sheetname             | scenarioname            |
            | Nas   | https://aem-stage.americanspirit.com | aem-mobile-nas.json | Pre-login Footerlinks | Validate FAQ footerlink |

        @NasPreLoginFAQFooterlink_Validation_PROD
        Examples:
            | Brand | URL                            | filename            | sheetname             | scenarioname            |
            | Nas   | https://www.americanspirit.com | aem-mobile-nas.json | Pre-login Footerlinks | Validate FAQ footerlink |

    Scenario Outline: Vaidate Contact us footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on Contact Us footerlink
        Then The user Validates contact Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @NasPreLoginContactUsFooterlink_Validation_QA
        Examples:
            | Brand | URL                                  | filename            | sheetname             | scenarioname            |
            | Nas   | https://aem-stage.americanspirit.com | aem-mobile-nas.json | Pre-login Footerlinks | Validate FAQ footerlink |

        @NasPreLoginContactUsFooterlink_Validation_PROD
        Examples:
            | Brand | URL                            | filename            | sheetname             | scenarioname            |
            | Nas   | https://www.americanspirit.com | aem-mobile-nas.json | Pre-login Footerlinks | Validate FAQ footerlink |

    Scenario Outline: Vaidate Site Requirement footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on Site Requirement footerlink
        Then The user Validates Site Requirement Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @NasPreLoginSiteRequirementFooterlink_Validation_QA
        Examples:
            | Brand | URL                                  | filename            | sheetname             | scenarioname                         |
            | Nas   | https://aem-stage.americanspirit.com | aem-mobile-nas.json | Pre-login Footerlinks | Validate SiteRequirements footerlink |

        @NasPreLoginSiteRequirementFooterlink_Validation_PROD
        Examples:
            | Brand | URL                            | filename            | sheetname             | scenarioname                         |
            | Nas   | https://www.americanspirit.com | aem-mobile-nas.json | Pre-login Footerlinks | Validate SiteRequirements footerlink |

    Scenario Outline: Vaidate Terms Of Use footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on Terms Of Use footerlink
        Then The user Validates Terms Of Use Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @NasPreLoginTermsOfUseFooterlink_Validation_QA
        Examples:
            | Brand | URL                                  | filename            | sheetname             | scenarioname                     |
            | Nas   | https://aem-stage.americanspirit.com | aem-mobile-nas.json | Pre-login Footerlinks | Validate Terms of Use footerlink |

        @NasPreLoginTermsOfUseFooterlink_Validation_PROD
        Examples:
            | Brand | URL                            | filename            | sheetname             | scenarioname                     |
            | Nas   | https://www.americanspirit.com | aem-mobile-nas.json | Pre-login Footerlinks | Validate Terms of Use footerlink |

    Scenario Outline: Vaidate Privacy Policy footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on Privacy Policy footerlink
        Then The user Validates Privacy Policy Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @NasPreLoginPrivacyPolicyFooterlink_Validation_QA
        Examples:
            | Brand | URL                                  | filename            | sheetname             | scenarioname                       |
            | Nas   | https://aem-stage.americanspirit.com | aem-mobile-nas.json | Pre-login Footerlinks | Validate Privacy Policy footerlink |

        @NasPreLoginPrivacyPolicyFooterlink_Validation_PROD
        Examples:
            | Brand | URL                                  | filename            | sheetname             | scenarioname                       |
            | Nas   | https://aem-stage.americanspirit.com | aem-mobile-nas.json | Pre-login Footerlinks | Validate Privacy Policy footerlink |

    Scenario Outline: Vaidate Text Messaging footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on Text Messaging footerlink
        Then The user Validates Text Messaging Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @NasPreLoginTextMessagingFooterlink_Validation_QA
        Examples:
            | Brand | URL                                  | filename            | sheetname             | scenarioname                       |
            | Nas   | https://aem-stage.americanspirit.com | aem-mobile-nas.json | Pre-login Footerlinks | Validate Text Messaging footerlink |

        @NasPreLoginTextMessagingFooterlink_Validation_PROD
        Examples:
            | Brand | URL                            | filename            | sheetname             | scenarioname                       |
            | Nas   | https://www.americanspirit.com | aem-mobile-nas.json | Pre-login Footerlinks | Validate Text Messaging footerlink |

    Scenario Outline: Vaidate Tobacco Rights footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on Tobacco Rights footerlink
        Then The user Validates Tobacco Rights Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @NasPreLoginTobaccoRightsFooterlink_Validation_QA
        Examples:
            | Brand | URL                                  | filename            | sheetname             | scenarioname                       |
            | Nas   | https://aem-stage.americanspirit.com | aem-mobile-nas.json | Pre-login Footerlinks | Validate Tobacco Rights footerlink |

        @NasPreLoginTobaccoRightsFooterlink_Validation_PROD
        Examples:
            | Brand | URL                            | filename            | sheetname             | scenarioname                       |
            | Nas   | https://www.americanspirit.com | aem-mobile-nas.json | Pre-login Footerlinks | Validate Tobacco Rights footerlink |

    Scenario Outline: Vaidate The Spirit Circle footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on The Spirit Circle footerlink
        Then The user Validates The Spirit Circle Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @NasPreLoginTheSpiritCircleFooterlink_Validation_QA
        Examples:
            | Brand | URL                                  | filename            | sheetname             | scenarioname                      |
            | Nas   | https://aem-stage.americanspirit.com | aem-mobile-nas.json | Pre-login Footerlinks | Validate Spirit Circle footerlink |

        @NasPreLoginTheSpiritCircleFooterlink_Validation_PROD
        Examples:
            | Brand | URL                            | filename            | sheetname             | scenarioname                      |
            | Nas   | https://www.americanspirit.com | aem-mobile-nas.json | Pre-login Footerlinks | Validate Spirit Circle footerlink |

    Scenario Outline: Vaidate Post Login FAQ footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the nas application successfully
        When The user clicks on post login FAQ footerlink for Nas
        Then The user Validates Nas FAQ Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user validates that successfully logged out of nas brand

        @NasPostLoginFAQFooterlink_Validation_QA
        Examples:
            | Brand | URL                                  | Username                           | Password  | filename            | sheetname              | scenarioname            |
            | Nas   | https://aem-stage.americanspirit.com | <EMAIL> | Password1 | aem-mobile-nas.json | Post-login Footerlinks | Validate FAQ footerlink |

        @NasPostLoginFAQFooterlink_Validation_PROD
        Examples:
            | Brand | URL                            | Username                             | Password  | filename            | sheetname              | scenarioname            |
            | Nas   | https://www.americanspirit.com | <EMAIL> | Password1 | aem-mobile-nas.json | Post-login Footerlinks | Validate FAQ footerlink |
