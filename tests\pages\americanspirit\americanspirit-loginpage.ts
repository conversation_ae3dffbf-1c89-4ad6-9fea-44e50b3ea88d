
import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import RegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';
import path from 'path';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import sensaLoginPageObject from '../../page-object/sensa/sensa-login.pageObject.ts';
import AccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';
import accountPage from '../../pages/commonteps/account.page.ts';

class NasLoginPage {

    async loginPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const signin = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsignin');
            const alreadyhave = testData.getCellValue(SHEET_NAME, scenarioname, 'lblhaveanaccount');
            const forgot = testData.getCellValue(SHEET_NAME, scenarioname, 'lblforgot');
            const loginemail = testData.getCellValue(SHEET_NAME, scenarioname, 'lblloginemail');
            const loginremember = testData.getCellValue(SHEET_NAME, scenarioname, 'lblrememberme');
            const loginpassword = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpassword');
            const join = testData.getCellValue(SHEET_NAME, scenarioname, 'lbljoin');
            const donthaveaccount = testData.getCellValue(SHEET_NAME, scenarioname, 'lblnewaccount');
            const joinemail = testData.getCellValue(SHEET_NAME, scenarioname, 'lbljoinemail');
            const legalnotice = testData.getCellValue(SHEET_NAME, scenarioname, 'lbllegalNotice');
            const faq = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfaq');
            const siterequirement = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsiterequirement');
            const privacypolicy = testData.getCellValue(SHEET_NAME, scenarioname, 'lblprivacypolicy');
            const contactus = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcontactus');
            const termsofuse = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltermsuse');
            const textmessaging = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltextmessage');
            const copyright = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcopyright');
            const lblregister = testData.getCellValue(SHEET_NAME, scenarioname, 'lblregister');
            const lblregisterdes = testData.getCellValue(SHEET_NAME, scenarioname, 'lblregisterdes');
            await accountPage.mssgcomparision(RegistrationPageObject.lblsignIn_sensa, signin);
            await accountPage.mssgcomparision(sensaLoginPageObject.lblalreadyhaveaccount_sensa, alreadyhave);
            await accountPage.mssgcomparision(sensaLoginPageObject.lblforgot_sensa, forgot);
            await accountPage.mssgcomparision(sensaLoginPageObject.lblloginemail_sensa, loginemail);
            await elementActions.assertion(AccountPageObject.txtusername_sensa);
            await accountPage.mssgcomparision(sensaLoginPageObject.lblloginrememberme_sensa, loginremember);
            await accountPage.mssgcomparision(sensaLoginPageObject.lblloginpassword_sensa, loginpassword);
            await elementActions.assertion(AccountPageObject.txtpassword_sensa);
            await elementActions.assertion(AccountPageObject.btnlogin_sensa);
            await accountPage.mssgcomparision(sensaLoginPageObject.lblregister_nas, lblregister);
            await accountPage.mssgcomparision(sensaLoginPageObject.lblregisterdes_nas, lblregisterdes);
            await accountPage.mssgcomparision(sensaLoginPageObject.lbljoin_nas, join);
            await accountPage.mssgcomparision(sensaLoginPageObject.lbldonthaveaccount_sensa, donthaveaccount);
            await accountPage.mssgcomparision(sensaLoginPageObject.lbljoinemail_sensa, joinemail);
            await elementActions.assertion(RegistrationPageObject.txtregemail_sensa);
            await elementActions.assertion(RegistrationPageObject.txtjoinnow_sensa);
            await accountPage.mssgcomparision(sensaLoginPageObject.lbllegalnotice_sensa, legalnotice);
            await accountPage.mssgcomparision(sensaLoginPageObject.lnkfaq_sensa, faq);
            await accountPage.mssgcomparision(sensaLoginPageObject.lnksiteRequirement_sensa, siterequirement);
            await accountPage.mssgcomparision(sensaLoginPageObject.lnkprivacyPolicy_sensa, privacypolicy);
            await accountPage.mssgcomparision(sensaLoginPageObject.lnkcontactUs_sensa, contactus);
            await accountPage.mssgcomparision(sensaLoginPageObject.lnktermsofUse_sensa, termsofuse);
            await accountPage.mssgcomparision(sensaLoginPageObject.lnktextMessaging_sensa, textmessaging);
            await accountPage.mssgcomparision(sensaLoginPageObject.lblcopyright_sensa, copyright);
            console.log('Validated the Content in Login page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Login page', { error });
            throw error;
        }
    }
}
export default new NasLoginPage();