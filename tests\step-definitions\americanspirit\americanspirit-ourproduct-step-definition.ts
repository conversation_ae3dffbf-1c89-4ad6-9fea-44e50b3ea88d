import { Then, When } from '@wdio/cucumber-framework';
import NasProductPageObject from '../../page-object/americanspirit/americanspirit-product.pageObject.ts';
import logger from '../../support/utils/logger.util.ts';
import NasproductPage from '../../pages/americanspirit/americanspirit-product.page.ts';

Then(/^The user Validates Nas OurProduct Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await NasproductPage.OurProductPageValidation(filepath, sheetname, scenarioname);
    logger.info('User Validates Nas OurProduct Page');
});



When(/^The user clicks on Our Products$/, async function () {
    await NasproductPage.clickonOurproduct();
    await expect(NasProductPageObject.Nas_productHeroImage).toBeDisplayed();
    logger.info('Navigated to Our Products Page Successfully');
});