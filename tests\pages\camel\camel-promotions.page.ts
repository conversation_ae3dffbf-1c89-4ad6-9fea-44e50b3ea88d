import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import path from 'path';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import sensaTextsignupPageObject from '../../page-object/sensa/sensa-textsignup.pageObject.ts';
import sensaAccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';
import camelPromotionsPageObject from '../../page-object/camel/camel-promotions.pageObject.ts';
import sensaAccountPage from '../commonteps/account.page.ts';


class Promotions {
    async promotionsPage() {
        try {
            await elementActions.waitForDisplayed(sensaAccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.clickusingJavascript(sensaAccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.click(camelPromotionsPageObject.hdrpromotions_camel);
            await elementActions.assertion(camelPromotionsPageObject.btnsignup_camel);
            console.log('Navigated to Promotions Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Promotions Page', { error });
            throw error;
        }
    }

    async promotionsPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const lbltextsignupdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltextsignupdesc');
            const lbltextsignuprules = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltextsignuprules');
            const lblentermobnum = testData.getCellValue(SHEET_NAME, scenarioname, 'lblentermobnum');
            const mobileNumber = testData.getCellValue(SHEET_NAME, scenarioname, 'lblmobileNumber');
            const certify = testData.getCellValue(SHEET_NAME, scenarioname, 'lblCertify');
            const hdrcomment = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrcomment');
            await elementActions.clickusingJavascript(camelPromotionsPageObject.btnsignup_camel);
            await sensaAccountPage.mssgcomparision(camelPromotionsPageObject.lblsignupfor_camel, lbltextsignupdesc);
            await sensaAccountPage.mssgcomparision(camelPromotionsPageObject.lblsignuprules_camel, lbltextsignuprules);
            await sensaAccountPage.mssgcomparision(camelPromotionsPageObject.lblentermobnum_camel, lblentermobnum);
            await sensaAccountPage.mssgcomparision(sensaTextsignupPageObject.lblmobile_sensa, mobileNumber);
            await sensaAccountPage.mssgcomparision(sensaTextsignupPageObject.txtiCertify_sensa, certify);
            await elementActions.assertion(sensaTextsignupPageObject.txtsubmit_sensa);
            await browser.execute(() => window.history.back());
            await sensaAccountPage.mssgcomparision(camelPromotionsPageObject.hdrcomments_camel, hdrcomment);
            await elementActions.assertion(camelPromotionsPageObject.txtcommentbox_camel);
            await elementActions.assertion(camelPromotionsPageObject.lblcharactremaining_camel);
            await elementActions.assertion(camelPromotionsPageObject.lblsubmissionguide_camel);
            await elementActions.assertion(camelPromotionsPageObject.btnshare_camel);
            console.log('Validated the Content in  Promotions page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Promotions page', { error });
            throw error;
        }
    }

    async commentverification(comment: string, timestamp: string) {
        try {
            await elementActions.waitForDisplayed(camelPromotionsPageObject.txtcommentbox_camel);
            await elementActions.setValue(camelPromotionsPageObject.txtcommentbox_camel, comment);
            await elementActions.click(camelPromotionsPageObject.btnshare_camel);
            await elementActions.waitForDisplayed(camelPromotionsPageObject.lblcommentmssg_camel);
            await sensaAccountPage.mssgcomparision(camelPromotionsPageObject.lblcommentmssg_camel, comment);
            await sensaAccountPage.mssgcomparision(camelPromotionsPageObject.lblcommenttime_camel, timestamp);

            console.log('Verified Comment Functionality Successfully');
        } catch (error) {
            logger.error('Failed to Verify Comment Functionality', { error });
            throw error;
        }
    }

}
export default new Promotions();
