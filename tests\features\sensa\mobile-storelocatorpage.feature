Feature:  Store Loactor Validation for Sensa Brand

    Scenario Outline: Validate the available stores in Store list page by using Use My Location and Zipcode for <Brand>

        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the application successfully
        Then The user click on Store Locator from Hamburger menu
        Then The user enter valid zipcode <Zipcode> and click on Find stores
        When The user validate the Store locator page
        When click on Load more and validate more 10 closest stores
        Then The user validates that successfully logged out

        @Sensa_Storelocator_UseMyLocationAndZipcode_Validation_QA
        Examples:
            | Brand | URL                             | Username                            | Password  | Zipcode | Latitude  | Longitude  |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | Password1 | 15204   | 36.099861 | -80.244217 |

        @Sensa_Storelocator_UseMyLocationAndZipcode_Validation_PROD
        Examples:
            | Brand | URL                       | Username                             | Password  | Zipcode | Latitude  | Longitude  |
            | Sensa | https://www.sensavape.com | <EMAIL> | Password1 | 15204   | 36.099861 | -80.244217 |


    Scenario Outline: Validate the available stores in Store list page by using Use My Location for <Brand>

        Given The user is on the login page for <Brand> with login <URL>
        When I set my location to "<Latitude>" and "<Longitude>"
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the application successfully
        Then The user click on Store Locator from Hamburger menu
        Then The user click on Use My Location
        When The user validate the Store locator page
        When click on Load more and validate more 10 closest stores
        Then The user validates that successfully logged out

        @Sensa_Storelocator_UseMyLocation_Validation_QA
        Examples:
            | Brand | URL                             | Username                            | Password  | Zipcode | Latitude | Longitude |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | Password1 | 20715   | 38.9864  | -76.7397  |

        @Sensa_Storelocator_UseMyLocation_Validation_PROD
        Examples:
            | Brand | URL                       | Username                             | Password  | Zipcode | Latitude | Longitude |
            | Sensa | https://www.sensavape.com | <EMAIL> | Password1 | 20715   | 38.9864  | -76.7397  |

    Scenario Outline: Validate the available stores in Store list page by FilterByProduct for <Brand>

        Given The user is on the login page for <Brand> with login <URL>
        When I set my location to "<Latitude>" and "<Longitude>"
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the application successfully
        Then The user click on Store Locator from Hamburger menu
        Then The user enter valid zipcode <Zipcode> and click on Find stores
        Then The user click on Filter by Product and validate all Product from filter by Product
        Then The user validates that successfully logged out


        @Sensa_Storelocator_FilterByProduct_Validation_QA
        Examples:
            | Brand | URL                             | Username                            | Password  | Zipcode | Latitude  | Longitude  |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | Password1 | 15204   | 36.099861 | -80.244217 |

        @Sensa_Storelocator_FilterByProduct_Validation_PROD
        Examples:
            | Brand | URL                       | Username                             | Password  | Zipcode | Latitude  | Longitude  |
            | Sensa | https://www.sensavape.com | <EMAIL> | Password1 | 15204   | 36.099861 | -80.244217 |