import type { Options } from '@wdio/types';
import { join } from 'path';
import { config as sauceSharedConfig } from './wdio.saucelabs.shared.conf.ts';
import { getStepDefinitionFiles } from '../support/helpers/step-definition-finder.ts';

const build = `WebdriverIO - Cucumber - Demo - ${new Date().getTime()}`;

// Environment-specific configuration
const environment = process.env.TEST_ENV || 'QA';
const isProduction = environment.toUpperCase() === 'PROD';

// Environment-specific tags
const environmentTags = isProduction 
  ? '@SensaContactInfo_Update_PROD or @SensaSecurityPage_Update_PROD or @SensaTobaccoPreferences_Update_PROD or @SensaRetaileAccount_Update_PROD or @SensaCouponsPage_Validation_PROD or @Sensa_flavorpage_Validation_PROD or @SensaPreLoginFAQFooterlink_Validation_PROD or @SensaPreLoginContactUsFooterlink_Validation_PROD or @SensaPreLoginSiteRequirementFooterlink_Validation_PROD or @SensaPreLoginTermsOfUseFooterlink_Validation_PROD or @SensaPreLoginPrivacyPolicyFooterlink_Validation_PROD or @SensaPreLoginTextMessagingFooterlink_Validation_PROD or @SensaPostLoginContactUsFooterlink_Validation_PROD or @SensaPostLoginFAQFooterlink_Validation_PROD or @SensaPostLoginSiteRequirementFooterlink_Validation_PROD or @SensaPostLoginTermsOfUseFooterlink_Validation_PROD or @SensaPostLoginPrivacyPolicyFooterlink_Validation_PROD or @SensaPostLoginextMessagingFooterlink_Validation_PROD or @SensaHomePage_Validation_PROD or @SensaLoginPage_Validation_PROD or @SensaInValidData_Validation_PROD or @SensaRememberMe_Validation_PROD or @SensaDevice_Validation_PROD or @SensaOffersPage_Validation_PROD or @SensaLSGW_Validation_PROD or @Sensa_Storelocator_UseMyLocationAndZipcode_Validation_PROD or @Sensa_Storelocator_UseMyLocation_Validation_PROD or @Sensa_Storelocator_FilterByProduct_Validation_PROD'
  : '@SensaContactInfo_Update_QA or @SensaSecurityPage_Update_QA or @SensaTobaccoPreferences_Update_QA or @SensaRetaileAccount_Update_QA or @SensaCouponsPage_Validation_QA or @Sensa_flavorpage_Validation_QA or @SensaPreLoginFAQFooterlink_Validation_QA or @SensaPreLoginContactUsFooterlink_Validation_QA or @SensaPreLoginSiteRequirementFooterlink_Validation_QA or @SensaPreLoginTermsOfUseFooterlink_Validation_QA or @SensaPreLoginPrivacyPolicyFooterlink_Validation_QA or @SensaPreLoginTextMessagingFooterlink_Validation_QA or @SensaPostLoginContactUsFooterlink_Validation_QA or @SensaPostLoginFAQFooterlink_Validation_QA or @SensaPostLoginSiteRequirementFooterlink_Validation_QA or @SensaPostLoginTermsOfUseFooterlink_Validation_QA or @SensaPostLoginPrivacyPolicyFooterlink_Validation_QA or @SensaPostLoginextMessagingFooterlink_Validation_QA or @SensaForgotPasswordFlow_QA or @SensaForgotPasswordFlowInvalidUsername_QA or @SensaForgotPasswordFlowfromForgotUsernamePage_Validation_QA or @SensaHomePage_Validation_QA or @SensaLoginPage_Validation_QA or @SensaInValidData_Validation_QA or @SensaRememberMe_Validation_QA or @SensaDevice_Validation_QA or @SensaOffersPage_Validation_QA or @Sensa_Registration_withTobacco_preferences_QA or @Sensa_Register_withAlreadyRegisteredUserofdiffbrand_QA or @Sensa_Register_withAlreadyRegisteredUserofsamebrand_QA or @Sensa_Registration_withoutTobacco_preferences_QA or @Sensa_Registration_ErrorStep1_QA or @Sensa_Registration_ErrorStep2_QA or @SensaLSGW_Validation_QA or @Sensa_Storelocator_UseMyLocationAndZipcode_Validation_QA or @Sensa_Storelocator_UseMyLocation_Validation_QA or @Sensa_Storelocator_FilterByProduct_Validation_QA or @SensaValidMobileNumber_Validation_QA or @SensaInValidMobileNumber_Validation_QA';

// Environment-specific specs
const specs = isProduction 
  ? [join(process.cwd(), './tests/features/**/*.feature')]
  : [join(process.cwd(), './tests/features/sensa/*.feature')];

// Environment-specific retry configuration
const retryCount = isProduction ? 1 : 2;

export const config: Options.Testrunner = {
  ...sauceSharedConfig,
  automationProtocol: 'webdriver',
  capabilities: [
    {
      // iPhone 15 Pro Max - Latest real iOS device
      'appium:deviceName': 'iPhone.*',
      'appium:automationName': 'XCUITest',
      'appium:platformVersion': '18.*.*',
      'appium:autoAcceptAlerts': false,
      'appium:newCommandTimeout': 300, // 5 minutes timeout for Appium commands
      browserName: 'Safari',
      platformName: 'iOS',
      'sauce:options': {
        build,
        appiumVersion: isProduction ? 'latest' : 'stable',
        maxDuration: 3600,
        idleTimeout: 300, // 5 minutes
        commandTimeout: 300, // 5 minutes
      },
      webSocketUrl: !isProduction, // Enable WebSocket for QA, disable for Prod
    },
  ],

  specs,

  // Standardized service configuration
  services: [
    [
      'sauce',
      {
        region: 'us-west-1',
      },
    ],
    [
      'gmail',
      {
        credentials: join(
          process.cwd(),
          'tests/resources/google-key/gmailCredentials.json',
        ),
        token: join(process.cwd(), 'tests/resources/google-key/token.json'),
        intervalSec: 10,
        timeoutSec: 60,
      },
    ],
  ],

  // Session isolation configuration for scenario-level sessions
  maxInstances: 1, // Force single instance to ensure session isolation
  maxInstancesPerCapability: 1, // One session per capability at a time
  waitforTimeout: 50000,
  connectionRetryTimeout: 150000,
  connectionRetryCount: 3,
  framework: 'cucumber',
  specFileRetries: 0,
  specFileRetriesDelay: 0,
  specFileRetriesDeferred: false,

  // Force session isolation at scenario level
  injectGlobals: true,
  bail: 0, // Don't bail on first failure to allow scenario isolation

  // TypeScript compilation configuration
  autoCompileOpts: {
    autoCompile: true,
    tsNodeOpts: {
      transpileOnly: true,
      project: './tsconfig.json',
    },
  },

  // Enhanced logging for detailed step output
  logLevel: 'info',

  // Cucumber specific configuration
  cucumberOpts: {
    require: getStepDefinitionFiles(),
    backtrace: true, // Enable backtrace for better error details
    requireModule: ['ts-node/register'],
    dryRun: false,
    failFast: false,
    colors: true,
    snippets: true,
    source: true,
    strict: false,
    tags: environmentTags,
    timeout: 1600000,
    ignoreUndefinedDefinitions: false,
    retry: retryCount,
    retryTagFilter: '', // Only retry scenarios tagged with @flaky
    scenarioLevelReporter: true,
    // Prioritize step-by-step output with detailed formatting
    format: [
      'pretty', // Primary formatter for step-by-step console output
      'summary', // Summary at the end
      './tests/support/formatters/EnhancedCucumberFormatter.ts', // Enhanced native Cucumber formatter
      'json:reports/cucumber-report.json',
      'rerun:reports/rerun.txt',
      'html:reports/cucumber-report.html',
      'junit:reports/cucumber-report.xml',
    ],
    // Enable detailed step reporting
    formatOptions: {
      snippetInterface: 'async-await',
    },
  },
};
