Feature: My Account Page Validation for Camel Website

    Scenario Outline: Updating Contact Info Details for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        Then The user click on MyAccount link
        When The user click on Contact Info link
        Then The user validates the contact info page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user enters Mobile Number <mobileNumber> Current Address <currentaddr> ZipCode <zipcode> in ContactInfo page
        Then The user validates the successfull message <successMessage>
        Then The user validates that successfully logged out of camel brand

        @CamelContactInfo_Update_QA
        Examples:
            | Brand | URL                         | Username                            | Password  | mobileNumber | currentaddr   | zipcode | successMessage                | filename              | sheetname    | scenarioname                 |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | ************ | 81 W 125TH ST | 10027   | Profile updated successfully. | aem-mobile-camel.json | Profile Page | Contact Info page Validation |

        @CamelContactInfo_Update_PROD
        Examples:
            | Brand | URL                   | Username                             | Password  | mobileNumber | currentaddr   | zipcode | successMessage                | filename              | sheetname    | scenarioname                 |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | ************ | 81 W 125TH ST | 10027   | Profile updated successfully. | aem-mobile-camel.json | Profile Page | Contact Info page Validation |

    Scenario Outline: Updating Password and Security Question for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        Then The user click on MyAccount link
        When The user click on Security link
        Then The user validates Security Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user enters Current Password <Password> New Password <NewPassword> and Confirm Password <confirmPassword> and validates error message<errormssg>
        Then The user enters Current Password <Password> and New Password same as current password and validates error message<samepassworderrormssg>
        Then The user enters Current Password <Password> New Password <NewPassword> and Confirm Password and validates success message<passwordsuccessmssg>
        When The user selects Security Question <securityquestion> and enters Answer <answer> and validates success message <securitysuccessmssg>
        Then The user validates that successfully logged out of camel brand
        When The user login with valid user id <Username> and password <Password>
        Then The user validates login error message <loginerror>
        When The user login with valid user id <Username> and password <NewPassword>
        Then The user click on MyAccount link
        When The user click on Security link
        Then The user enters Current Password <NewPassword> New Password <Password> and Confirm Password and validates success message<passwordsuccessmssg>
        Then The user validates that successfully logged out of camel brand

        @CamelSecurityPage_Update_QA 
        Examples:
            | Brand | URL                         | Username                              | Password  | NewPassword | confirmPassword | errormssg                   | samepassworderrormssg                                    | passwordsuccessmssg            | securityquestion                        | answer | securitysuccessmssg            | loginerror                                                         | filename              | sheetname    | scenarioname             |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | Password12  | Password123     | New passwords do not match. | New password cannot be the same as the current password. | Password updated successfully. | What was the name of your first school? | school | Question updated successfully. | Username or password does not match our records. Please try again. | aem-mobile-camel.json | Profile Page | Security Page Validation |

        @CamelSecurityPage_Update_PROD
        Examples:
            | Brand | URL                   | Username                                    | Password  | NewPassword | confirmPassword | errormssg                   | samepassworderrormssg                                    | passwordsuccessmssg            | securityquestion                        | answer | securitysuccessmssg            | loginerror                                                         | filename              | sheetname    | scenarioname             |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | Password12  | Password123     | New passwords do not match. | New password cannot be the same as the current password. | Password updated successfully. | What was the name of your first school? | school | Question updated successfully. | Username or password does not match our records. Please try again. | aem-mobile-camel.json | Profile Page | Security Page Validation |

    Scenario Outline: Updating Tobacco Preferences for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        Then The user click on MyAccount link
        When The user click on Tobacco Preferences link
        When The user click on <product> product checkbox
        Then The user validates the page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname> based on <product>
        When The user selects brand <brand> flavour <flavour> last purchases <purchases> and prefer nicotine <prefernicotine> and regular cur <regularcut> if present based on <product>
        Then The user validates the success message <sucessmssg>
        Then The user validates that successfully logged out of camel brand

        @CamelTobaccoPreferences_Update_QA
        Examples:
            | Brand | URL                         | Username                           | Password  | product         | brand  | flavour     | purchases | prefernicotine | regularcut | sucessmssg                        | filename              | sheetname    | scenarioname                        |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | MOIST SNUFF/DIP | KODIAK | WINTERGREEN | 7         | Nicotine       | FINE CUT   | Preferences updated successfully. | aem-mobile-camel.json | Profile Page | Tobacco Preferences Page Validation |

        @CamelTobaccoPreferences_Update_PROD
        Examples:
            | Brand | URL                   | Username                               | Password  | product         | brand  | flavour     | purchases | prefernicotine | regularcut | sucessmssg                        | filename              | sheetname    | scenarioname                        |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | MOIST SNUFF/DIP | KODIAK | WINTERGREEN | 7         | Nicotine       | FINE CUT   | Preferences updated successfully. | aem-mobile-camel.json | Profile Page | Tobacco Preferences Page Validation |

    Scenario Outline: Updating Retailer Account for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        Then The user click on MyAccount link
        When The user click on Retailer Account
        Then The user links the Retailer loyalty and verifies the success message <successmssg>
        Then The user unlinks the Retailer loyalty and verifies the unlinked message <unlinkedmssg>
        Then The user validates that successfully logged out of camel brand

        @CamelRetaileAccount_Update_QA
        Examples:
            | Brand | URL                         | Username                           | Password  | successmssg      | unlinkedmssg       |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | Link successful. | Unlink successful. |

        @CamelRetaileAccount_Update_PROD 
        Examples:
            | Brand | URL                   | Username                            | Password  | successmssg      | unlinkedmssg       |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | Link successful. | Unlink successful. |

    Scenario Outline: Validation Coupons page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        Then The user click on MyAccount link
        When The user click on Coupons link
        When The user validates Coupons Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user validates that successfully logged out of camel brand

        @CamelCouponsPage_Validation_QA
        Examples:
            | Brand | URL                         | Username                           | Password  | filename              | sheetname    | scenarioname            |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Profile Page | Coupons Page Validation |

        @CamelCouponsPage_Validation_PROD
        Examples:
            | Brand | URL                   | Username                             | Password  | filename              | sheetname    | scenarioname            |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Profile Page | Coupons Page Validation |


