Feature: Forgot Password Flow Validation for Camel Website


    Scenario Outline:Validating Forgot Password Flow for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user click on ForgotPassword link
        Then The user validates the Forgot Password page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user enter valid username <Username> and click Continue
        And The user validates the Reset Request Success Page page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When I search for email with subject containing <SubjectText>
        Then The user click on the link with text <linkText> in the email
        Then The user validates the Reset Password page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        And The user enter valid data in Password <Password> and ConfirmPassword <ConfirmPassword> fields and click Continue on ResetPassword form page
        Then The user should be able to login to the camel application successfully
        Then The user validates that successfully logged out of camel brand

        @CamelForgotPasswordFlow_QA
        Examples:
            | Brand | URL                         | Username                   | ConfirmPassword | Password  | SenderEmail                             | SubjectText                                 | linkText   | filename              | sheetname       | scenarioname                     |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1       | Password1 | <EMAIL> | Retrieve your Camel password with this link | click here | aem-mobile-camel.json | Forgot Password | Validate Forgot Password Content |

    Scenario Outline:Validating Errors when entered invalid Username in Forgot Password Flow for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user click on ForgotPassword link
        Then The user enter invalid username <invalidFormatUsername> and validate error Message as <errormssg>

        @CamelForgotPasswordFlow_QA
        Examples:
            | Brand | URL                         | invalidFormatUsername      | errormssg                           |
            | Camel | https://aem-stage.camel.com | satya%^()[]{}<EMAIL> | Please enter a valid email address. |


    Scenario Outline: Updating Password through link in Forgot Username Page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        Then The user clicks on Forgot Username Link
        When The user enters First Name as <FirstName> Last Name as <LastName> street address as <streetAddr> Zipcode as <zipCode> City as <city> State as <state> and DOB as "<dateOfBirth>"
        When The user enters answer as <answer> for the security question
        Then The user clicks on Reset Your Password link
        Then The user enter valid username <Username> and click Continue
        When I search for email with subject containing <SubjectText>
        Then The user click on the link with text <linkText> in the email
        Then The user enters Password as <Password> and confirm Password <wrongconfirmPassword> and validates error message <errormessage>
        And The user enter valid data in Password <Password> and ConfirmPassword <ConfirmPassword> fields and click Continue on ResetPassword form page
        Then The user should be able to login to the camel application successfully
        Then The user validates that successfully logged out of camel brand

        @CamelForgotPassword_Validation_QA
        Examples:
            | Brand | URL                         | Username                   | Password  | wrongconfirmPassword | ConfirmPassword | FirstName | LastName | streetAddr      | zipCode | city          | state | dateOfBirth | answer | SenderEmail                             | SubjectText                                 | errormessage                                         | linkText   | filename              | sheetname       | scenarioname                   |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | Password123          | Password1       | SAMODIN   | BTRIM    | 5027 WINSTER DR | 27106   | Winston-Salem | NC    | 01-01-2001  | school | <EMAIL> | Retrieve your Camel password with this link | Oops, that password doesn't match. Please try again. | click here | aem-mobile-camel.json | Forgot Username | Validate Forgot Username Pages |
