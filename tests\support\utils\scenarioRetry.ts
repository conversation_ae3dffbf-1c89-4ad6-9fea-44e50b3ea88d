import { ScenarioReporter } from './scenarioReporter.ts';

interface RetryMetrics {
    scenarioId: string;
    attempts: number;
    passed: boolean;
    errors: string[];
    totalDuration: number;
    lastAttemptTime: number;
}

interface RetryConfig {
    maxRetries: number;
    retryDelayMs: number;
    source: 'cli' | 'cucumber' | 'default';
}

interface BrowserConfig {
    cucumberOpts?: {
        retry?: number;
    };
}

interface GlobalWithBrowser {
    browser?: {
        options?: BrowserConfig;
    };
}

interface ScenarioWorld {
    pickle?: {
        name?: string;
        uri?: string;
    };
}

interface ScenarioResult {
    error?: string | { message: string };
    passed: boolean;
}


export class EnhancedScenarioRetry {
    private retryMetrics: Map<string, RetryMetrics> = new Map();
    private config: RetryConfig;
    private customConfig?: Partial<RetryConfig>;
    private initialized = false;

    constructor(customConfig?: Partial<RetryConfig>) {
        this.customConfig = customConfig;
        // Do not detect config here; defer to initialize()
        this.config = {
            maxRetries: 3,
            retryDelayMs: 2000,
            source: 'default',
        };
    }

    /**
     * Detect retry configuration with priority: CLI > cucumberOpts > custom > default
     */
    private detectRetryConfiguration(): RetryConfig {
        const defaultConfig: RetryConfig = {
            maxRetries: 0, // No retries by default
            retryDelayMs: 2000,
            source: 'default',
        };

        // Start with custom config if provided
        const config = { ...defaultConfig, ...this.customConfig };

        // 1. Check CLI arguments first (highest priority)
        const cliRetryArg = process.argv.find(arg => arg.startsWith('--retry'));
        if (cliRetryArg) {
            const retryValue = cliRetryArg.split('=')[1] || 
                             process.argv[process.argv.indexOf(cliRetryArg) + 1];
            if (retryValue && !isNaN(Number(retryValue))) {
                config.maxRetries = Number(retryValue); // CLI retry is exact retry count
                config.source = 'cli';
                console.log(`🔧 Using CLI retry: --retry ${retryValue} = ${config.maxRetries} total attempts`);
                return config;
            }
        }

        // 2. Check cucumberOpts.retry from conf file (second priority)
        let cucumberRetry: number | undefined;
        try {
            cucumberRetry = (global as GlobalWithBrowser)?.browser?.options?.cucumberOpts?.retry;
        } catch {
            cucumberRetry = undefined;
        }
        if (typeof cucumberRetry === 'number' && !isNaN(cucumberRetry)) {
            config.maxRetries = Number(cucumberRetry); // Cucumber retry is exact retry count
            config.source = 'cucumber';
            console.log(`🔧 Using cucumberOpts retry from conf: ${cucumberRetry} = ${config.maxRetries} total attempts`);
            return config;
        }

        // 3. If cucumberOpts.retry is 0 or not set, use default/custom
        console.log(`🔧 Using ${this.customConfig?.maxRetries ? 'custom' : 'default'} retry: ${config.maxRetries} retries`);
        return config;
    }

    /**
     * Detect cucumberOpts retry at runtime (after browser is available)
     */
    detectCucumberOptsRetryIfAvailable(): void {
        let cucumberRetry: number | undefined;
        try {
            cucumberRetry = (global as GlobalWithBrowser)?.browser?.options?.cucumberOpts?.retry;
        } catch {
            cucumberRetry = undefined;
        }
        if (typeof cucumberRetry === 'number' && !isNaN(cucumberRetry)) {
            const prevConfig = { ...this.config };
            this.config.maxRetries = Number(cucumberRetry) + 1; // Cucumber retry is additional attempts
            this.config.source = 'cucumber';
            console.log('[EnhancedScenarioRetry][RUNTIME] cucumberOpts.retry detected at runtime. Overriding retry config:');
            console.log('   Previous:', JSON.stringify(prevConfig));
            console.log('   New:     ', JSON.stringify(this.config));
        } else {
            console.log('[EnhancedScenarioRetry][RUNTIME] No cucumberOpts.retry detected at runtime. Using config:', JSON.stringify(this.config));
        }
    }

    /**
     * Initialize retry system
     */
    /**
     * Initialize retry system, optionally with a custom config (e.g., from conf file)
     */
    initialize(customConfig?: Partial<RetryConfig>): void {
        if (customConfig) {
            this.customConfig = customConfig;
        }
        // Detect CLI/custom/default config
        this.config = this.detectRetryConfiguration();
        // Log startup config explicitly
        console.log('[EnhancedScenarioRetry][STARTUP] Initial retry config:', JSON.stringify(this.config));
        // Now, if browser is available, update with cucumberOpts
        this.detectCucumberOptsRetryIfAvailable();
        this.initialized = true;
        console.log('🔄 Initializing Enhanced Scenario Retry System...');
        this.retryMetrics.clear();
        this.logRetryConfiguration();
        this.validateRetryConfiguration();
    }

    /**
     * Log current retry configuration for debugging
     */
    private logRetryConfiguration(): void {
        console.log('🔧 Retry Configuration Detection:');
        console.log(`   CLI Args: ${process.argv.join(' ')}`);

        const cliRetryArg = process.argv.find(arg => arg.startsWith('--retry'));
        const cucumberRetry = (global as GlobalWithBrowser).browser?.options?.cucumberOpts?.retry;

        console.log(`   ${cliRetryArg ? '✅' : '❌'} CLI Retry (Priority 1): ${cliRetryArg || 'Not provided'}`);
        if (cucumberRetry !== undefined) {
            console.log(`   ✅ CucumberOpts Retry (Priority 2): ${cucumberRetry}`);
        } else {
            console.log('   ⏳ CucumberOpts Retry (Priority 2): Not configured at startup (will check at runtime in beforeScenario)');
        }
        console.log(`   🎯 Active Configuration: ${this.config.maxRetries} total attempts (source: ${this.config.source})`);
        console.log(`   ⏱️  Retry Delay: ${this.config.retryDelayMs}ms`);
    }

    /**
     * Validate retry configuration
     */
    private validateRetryConfiguration(): boolean {
        if (this.config.maxRetries < 0) {
            console.error('❌ Invalid retry configuration: maxRetries must be non-negative');
            return false;
        }
        
        if (this.config.maxRetries > 10) {
            console.warn(`⚠️  High retry count detected: maxRetries = ${this.config.maxRetries}`);
        }
        
        if (this.config.retryDelayMs < 0) {
            console.error('❌ Invalid retry delay: retryDelayMs must be non-negative');
            return false;
        }
        
        console.log(`✅ Retry configuration is valid: maxRetries = ${this.config.maxRetries}, delay = ${this.config.retryDelayMs}ms`);
        return true;
    }

    /**
     * Generate scenario ID from world object
     */
    private getScenarioId(world: ScenarioWorld): { scenarioId: string; scenarioName: string; featureName: string } {
        const scenarioName = world.pickle?.name || 'Unknown Scenario';
        const featureName = world.pickle?.uri?.split('/').pop()?.replace('.feature', '') || 'Unknown Feature';
        const scenarioId = `${featureName}::${scenarioName}`;
        
        return { scenarioId, scenarioName, featureName };
    }

    /**
     * Generate scenario ID from names
     */
    private getScenarioIdFromNames(scenarioName: string, featureName: string): string {
        return `${featureName}::${scenarioName}`;
    }

    /**
     * Check if a scenario should be retried
     */
    shouldRetryScenario(scenarioName: string, featureName: string): boolean {
        const scenarioId = this.getScenarioIdFromNames(scenarioName, featureName);
        const metrics = this.retryMetrics.get(scenarioId);

        if (!metrics) {
            return true; // First attempt, always proceed
        }

        const shouldRetry = metrics.attempts < this.config.maxRetries && !metrics.passed;
        
        console.log(`🔍 Retry check for ${scenarioId}: ${metrics.attempts}/${this.config.maxRetries} attempts, passed: ${metrics.passed}, should retry: ${shouldRetry}`);
        
        return shouldRetry;
    }

    /**
     * Record a scenario attempt with simplified interface
     */
    recordAttempt(
        scenarioName: string,
        featureName: string,
        passed: boolean,
        duration: number,
        error?: string,
    ): void {
        const scenarioId = this.getScenarioIdFromNames(scenarioName, featureName);
        this.updateMetrics(scenarioId, scenarioName, passed, duration, error);
    }

    /**
     * Record attempt from world object
     */
    recordAttemptFromWorld(
        world: ScenarioWorld,
        passed: boolean,
        duration: number,
        error?: string,
    ): void {
        const { scenarioId, scenarioName } = this.getScenarioId(world);
        this.updateMetrics(scenarioId, scenarioName, passed, duration, error);
    }

    /**
     * Internal method to update metrics
     */
    private updateMetrics(
        scenarioId: string,
        scenarioName: string,
        passed: boolean,
        duration: number,
        error?: string,
    ): void {
        const existingMetrics = this.retryMetrics.get(scenarioId);

        if (existingMetrics) {
            existingMetrics.attempts += 1;
            existingMetrics.passed = passed;
            existingMetrics.totalDuration += duration;
            existingMetrics.lastAttemptTime = Date.now();
            // Only add to errors if this attempt failed
            if (error && passed === false) {
                existingMetrics.errors.push(error);
            }
        } else {
            // Only add to errors if this attempt failed
            const errorsArr = error && passed === false ? [error] : [];
            const newMetrics: RetryMetrics = {
                scenarioId,
                attempts: 1,
                passed,
                errors: errorsArr,
                totalDuration: duration,
                lastAttemptTime: Date.now(),
            };
            this.retryMetrics.set(scenarioId, newMetrics);
        }

        const currentMetrics = this.retryMetrics.get(scenarioId)!;

        // Update scenario reporter
        ScenarioReporter.updateScenario(scenarioName, {
            attempts: currentMetrics.attempts,
            status: passed ? 'passed' : 'failed',
            duration,
            error: error,
        });

        console.log(`📊 Recorded attempt for ${scenarioId}: ${currentMetrics.attempts} attempts, passed: ${passed}`);
    }

    /**
     * Get current attempt number for a scenario
     */
    getCurrentAttempt(scenarioName: string, featureName: string): number {
        const scenarioId = this.getScenarioIdFromNames(scenarioName, featureName);
        const metrics = this.retryMetrics.get(scenarioId);
        return metrics ? metrics.attempts + 1 : 1;
    }

    /**
     * Get current attempt from world object
     */
    getCurrentAttemptFromWorld(world: ScenarioWorld): number {
        const { scenarioId } = this.getScenarioId(world);
        const metrics = this.retryMetrics.get(scenarioId);
        return metrics ? metrics.attempts + 1 : 1;
    }

    /**
     * Add delay between retry attempts
     */
    async delayBeforeRetry(scenarioName: string, featureName: string): Promise<void> {
        const scenarioId = this.getScenarioIdFromNames(scenarioName, featureName);
        await this.executeRetryDelay(scenarioId);
    }

    /**
     * Add delay from world object
     */
    async delayBeforeRetryFromWorld(world: ScenarioWorld): Promise<void> {
        const { scenarioId } = this.getScenarioId(world);
        await this.executeRetryDelay(scenarioId);
    }

    /**
     * Internal method to execute retry delay
     */
    private async executeRetryDelay(scenarioId: string): Promise<void> {
        const metrics = this.retryMetrics.get(scenarioId);
        
        if (metrics && metrics.attempts > 0) {
            console.log(`⏳ Waiting ${this.config.retryDelayMs}ms before retrying ${scenarioId}...`);
            await new Promise(resolve => setTimeout(resolve, this.config.retryDelayMs));
        }
    }

    /**
     * Get retry metrics for a scenario
     */
    getRetryMetrics(scenarioName: string, featureName: string): RetryMetrics | undefined {
        const scenarioId = this.getScenarioIdFromNames(scenarioName, featureName);
        return this.retryMetrics.get(scenarioId);
    }

    /**
     * Get all retry metrics
     */
    getAllRetryMetrics(): Map<string, RetryMetrics> {
        return new Map(this.retryMetrics);
    }

    /**
     * Get current configuration
     */
    getConfig(): RetryConfig {
        return { ...this.config };
    }

    /**
     * Update configuration
     */
    updateConfig(newConfig: Partial<RetryConfig>): void {
        this.config = { ...this.config, ...newConfig };
        console.log(`🔄 Configuration updated: maxRetries=${this.config.maxRetries}, retryDelayMs=${this.config.retryDelayMs}`);
    }

    /**
     * Print retry summary with enhanced statistics
     */
    printRetrySummary(): void {
        if (this.retryMetrics.size === 0) {
            console.log('📊 No retry metrics to display');
            return;
        }

        console.log('\n' + '='.repeat(80));
        console.log('🔄 SCENARIO RETRY SUMMARY');
        console.log('='.repeat(80));

        let totalScenarios = 0;
        let scenariosWithRetries = 0;
        let totalAttempts = 0;
        let passedScenarios = 0;
        let failedScenarios = 0;

        for (const [scenarioId, metrics] of this.retryMetrics.entries()) {
            totalScenarios++;
            totalAttempts += metrics.attempts;
            if (metrics.passed) passedScenarios++;
            else failedScenarios++;

            // Only count as retried if there was at least one failed attempt before pass (for passed scenarios)
            // For failed scenarios, count as retried if there was at least one failed attempt (i.e., errors.length > 0)
            const wasRetried = metrics.errors.length > 0 && (metrics.passed || (!metrics.passed && metrics.attempts > 1));
            if (wasRetried) {
                scenariosWithRetries++;
                const statusIcon = metrics.passed ? '✅' : '❌';
                const retryInfo = `${metrics.attempts}/${this.config.maxRetries} attempts`;
                const durationInfo = `${metrics.totalDuration}ms total`;

                console.log(`${statusIcon} ${scenarioId} - ${retryInfo} (${durationInfo})`);

                console.log(`   💥 Errors: ${metrics.errors.length}`);
                metrics.errors.forEach((error, index) => {
                    const truncatedError = error.length > 100 ? `${error.substring(0, 100)}...` : error;
                    console.log(`      ${index + 1}. ${truncatedError}`);
                });
            }
        }

        console.log('\n📈 RETRY STATISTICS:');
        console.log(`   Total Scenarios: ${totalScenarios}`);
        console.log(`   Passed Scenarios: ${passedScenarios} (${((passedScenarios / totalScenarios) * 100).toFixed(1)}%)`);
        console.log(`   Failed Scenarios: ${failedScenarios} (${((failedScenarios / totalScenarios) * 100).toFixed(1)}%)`);
        console.log(`   Scenarios with Retries: ${scenariosWithRetries}`);
        console.log(`   Total Attempts: ${totalAttempts}`);
        console.log(`   Average Attempts: ${(totalAttempts / totalScenarios).toFixed(2)}`);
        console.log(`   Retry Rate: ${((scenariosWithRetries / totalScenarios) * 100).toFixed(1)}%`);
        console.log(`   Configuration Source: ${this.config.source}`);

        console.log('='.repeat(80));
    }

    /**
     * Clear all retry metrics
     */
    clear(): void {
        this.retryMetrics.clear();
        console.log('🔄 Retry metrics cleared');
    }

    /**
     * Export retry metrics to JSON with enhanced metadata
     */
    exportMetrics(): Record<string, unknown> {
        const stats = this.calculateStatistics();
        
        return {
            timestamp: new Date().toISOString(),
            config: this.config,
            statistics: stats,
            scenarios: Array.from(this.retryMetrics.entries()).map(([_scenarioId, metrics]) => ({
                ...metrics,
            })),
        };
    }

    /**
     * Calculate comprehensive statistics
     */
    private calculateStatistics(): Record<string, number> {
        if (this.retryMetrics.size === 0) {
            return {};
        }

        let totalAttempts = 0;
        let scenariosWithRetries = 0;
        let passedScenarios = 0;
        let totalDuration = 0;

        for (const metrics of this.retryMetrics.values()) {
            totalAttempts += metrics.attempts;
            totalDuration += metrics.totalDuration;
            // Only count as retried if there was at least one failed attempt before pass (for passed scenarios)
            // For failed scenarios, count as retried if there was at least one failed attempt (i.e., errors.length > 0)
            const wasRetried = metrics.errors.length > 0 && (metrics.passed || (!metrics.passed && metrics.attempts > 1));
            if (wasRetried) scenariosWithRetries++;
            if (metrics.passed) passedScenarios++;
        }

        return {
            totalScenarios: this.retryMetrics.size,
            passedScenarios,
            failedScenarios: this.retryMetrics.size - passedScenarios,
            scenariosWithRetries,
            totalAttempts,
            averageAttempts: Number((totalAttempts / this.retryMetrics.size).toFixed(2)),
            retryRate: Number(((scenariosWithRetries / this.retryMetrics.size) * 100).toFixed(1)),
            passRate: Number(((passedScenarios / this.retryMetrics.size) * 100).toFixed(1)),
            totalDuration,
            averageDuration: Number((totalDuration / this.retryMetrics.size).toFixed(2)),
        };
    }

    /**
     * Integration hook for beforeScenario - simplified interface
     */
    async handleBeforeScenario(world: ScenarioWorld): Promise<void> {
        await this.delayBeforeRetryFromWorld(world);
        
        const currentAttempt = this.getCurrentAttemptFromWorld(world);
        const { scenarioId } = this.getScenarioId(world);
        
        console.log(`🔄 Starting attempt ${currentAttempt} for ${scenarioId}`);
    }

    /**
     * Integration hook for afterScenario - simplified interface
     */
    handleAfterScenario(
        world: ScenarioWorld,
        result: ScenarioResult,
        duration: number,
    ): void {
        const error = result.error ?
            (typeof result.error === 'string' ? result.error : result.error.message) :
            undefined;
            
        this.recordAttemptFromWorld(world, result.passed, duration, error);
    }
}

// Export singleton instance for backward compatibility
export const enhancedScenarioRetry = new EnhancedScenarioRetry();