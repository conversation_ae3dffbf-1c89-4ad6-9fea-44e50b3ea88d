class HomePageObject {
    get lblhambergercloseicon_sensa() { return $('//*[@class="hamburger-wrapper hamburger-menu-close-icon"]'); }
    get lnkflavors_sensa() { return $('//*[@title="Flavors"]'); }
    get lnkstorelocator_sensa() { return $('//*[@title="Store Locator"]'); }
    get lblteaser_sensa() { return $('(//*[@class="cmp-teaser__title "])[1]'); }
    get lblteaserdesc_sensa() { return $('//*[@class="cmp-teaser__description "]//p'); }
    get btnfindyourflavor_sensa() { return $('//*[@class="cmp-teaser__action-link"][text()="Find Your Flavor"]'); }
    get lblberryfusion_sensa() { return $('//figcaption[normalize-space()="BERRY FUSION"]'); }
    get imgberryfusion_sensa() { return $('//figcaption[normalize-space()="BERRY FUSION"]//parent::*//img'); }
    get lblberrywatermelonfusion_sensa() { return $('//figcaption[normalize-space()="BERRY WATERMELON FUSION"]'); }
    get imgberrywatermelonfusion_sensa() { return $('//figcaption[normalize-space()="BERRY WATERMELON FUSION"]//parent::*//img'); }
    get lblpassionfrost_sensa() { return $('//figcaption[normalize-space()="PASSIONFRUIT FROST"]'); }
    get imgpassionfrost_sensa() { return $('//figcaption[normalize-space()="PASSIONFRUIT FROST"]//parent::*//img'); }
    get lblmintfrost_sensa() { return $('//figcaption[normalize-space()="MINT FROST"]'); }
    get imgmintfrost_sensa() { return $('//figcaption[normalize-space()="MINT FROST"]//parent::*//img'); }
    get lblwatermelonfrost_sensa() { return $('//figcaption[normalize-space()="WATERMELONFROST"]'); }
    get imgwatermelonfrost_sensa() { return $('//figcaption[normalize-space()="WATERMELONFROST"]//parent::*//img'); }
    get lblblueberryfrost_sensa() { return $('//figcaption[normalize-space()="BLUEBERRY FROST"]'); }
    get imgblueberryfrost_sensa() { return $('//figcaption[normalize-space()="BLUEBERRY FROST"]//parent::*//img'); }
    get lblindulgeinflavor_sensa() { return $('//*[@class="sensa-flavor-content"]/h2'); }
    get lblgettoknowzeronicotine_sensa() { return $('//*[@class="sensa-flavor-content"]/p'); }
    get btndiscoversensa_sensa() { return $('//*[@class="sensa-flavor-content"]/button'); }
    get lblallflavors_sensa() { return $('(//h2[contains(text(),"All")])[2]'); }
    get lblzeronictine_sensa() { return $('//h2[contains(text(),"Zero")]'); }
    get lblvideo_sensa() { return $('//*[contains(@class,"cmp-video-external")]'); }
    get btndiscoverflavor_sensa() { return $('//*[@id="discover-flavor"]'); }
    get lblstorelocatortile_sensa() { return $('(//*[@class="cmp-teaser__content row"]//h3)[2]'); }
    get lblclaimmobiletile_sensa() { return $('(//*[@class="cmp-teaser__content row"]//h3)[3]'); }
    get btnfindSensa_sensa() { return $('//a[normalize-space()="Find Sensa"]'); }
    get imgstorefinder_sensa() { return $('//img[@alt="test"]'); }
    get btnclaimoffers_sensa() { return $('//a[normalize-space()="Claim Offer"]'); }
    get imgclaimoffers_sensa() { return $('(//img[@aria-label="Image"])[2]'); }
    get hdrflavorspage_sensa() { return $('(//div[contains(@class,"col cmp-teaser__first-container")])[1]'); }
    get hdrstorelocatorpage_sensa() { return $('//h2[normalize-space()="Find A Store"]'); }
    get lnkwatiszeronicotine_sensa() { return $('//a[normalize-space()="*WHAT IS ZERO NICOTINE"]'); }
    get lblcopyright_sensa() { return $('//div[@class="cmp-footer__copyright col"]'); }
    get lblallowpopup_sensa() { return $('~Allow'); }
    get lblallowthistimepopup_sensa() { return $('~Allow this time'); }
    get lblcancelpopup_sensa() { return $('~Cancel'); }

    // footerlinks
    get lnkoffers_sensa() { return $('(//span[contains(text(),"Offers")])[2]'); }
    get lnkproducts_sensa() { return $('(//span[contains(text(),"Products")])[2]'); }
    get lnkmyAccount_sensa() { return $('(//span[contains(text(),"My Account")])[2]'); }
    get lnkcontactus_sensa() { return $('(//span[contains(text(),"Contact Us")])[2]'); }
    get lnkfaq_sensa() { return $('(//span[contains(text(),"FAQ")])[2]'); }
    get lnksiterrequirements_sensa() { return $('(//span[contains(text(),"Site Requirements")])[2]'); }
    get lnktermsofuse_sensa() { return $('(//span[contains(text(),"Terms of Use")])[2]'); }
    get lnkprivacypolicy_sensa() { return $('(//span[contains(text(),"Privacy Policy")])[2]'); }
    get lnktextmessaging_sensa() { return $('(//span[contains(text(),"Text Messaging Terms & Conditions")])[2]'); }
}
export default new HomePageObject();