class TextSignUpCamelPageObject {

     get hdrpromotions_camel() { return $('//*[@title="Promotions"]'); }
     get lblsignuprules_camel() { return $('.cmp-sms-opt-in-two ul'); }
     get lblsignupfor_camel() { return $('//h4[contains(text(),"Sign up for Camel text")]'); }
     get lblentermobnum_camel() { return $('//h4[normalize-space()="Enter your mobile number to sign up"]'); }
     get btnsignup_camel() { return $('[aria-label="Sign up for texts"]'); }
     get lblsuccess_camel() { return $('//*[@class="cmp-sms-opt-in-two__success-content"]'); }
     get hdrcomments_camel() { return $('//*[normalize-space()="Comments"]'); }
     get txtcommentbox_camel() { return $('//*[@class="cmp-comments__reply-comment-text"]'); }
     get btnshare_camel() { return $('//*[@class="cmp-comments__submit-reply"]'); }
     get lblcharactremaining_camel() { return $('//*[@class="cmp-comments__characters-remaining"]'); }
     get lblsubmissionguide_camel() { return $('//*[@class="cmp-comments__submission-guidelines"]'); }
     get lblcommenttime_camel() { return $('(//*[@class="cmp-comments__comment-timestamp"])[1]'); }
     get lblcommentmssg_camel() { return $('(//*[@class="cmp-comments__comment-content"])[1]'); }


}
export default new TextSignUpCamelPageObject();