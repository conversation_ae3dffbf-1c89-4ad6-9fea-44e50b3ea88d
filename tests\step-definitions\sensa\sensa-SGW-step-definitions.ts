import sensaHomepagePage from '../../pages/sensa/sensa-homepage.page.ts';
import { Then } from '@wdio/cucumber-framework';
import sensaSgwPage from '../../pages/commonteps/sgw.page.ts';
import logger from '../../support/utils/logger.util.ts';
import sensaHomepagePageObject from '../../page-object/sensa/sensa-homepage.pageObject.ts';

Then(/^The user click on Flavours link$/, async function () {
    await sensaHomepagePage.clickonflavorsfromheader();
    await expect(sensaHomepagePageObject.hdrflavorspage_sensa).toBeDisplayed();
    logger.info('Navigated to Flavours Page Successfully');
});

Then(/^The user Validates SGW Message with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaSgwPage.compareSGWText(filepath, sheetname, scenarioname);
    logger.info('Validated to SGW Message Successfully');
});