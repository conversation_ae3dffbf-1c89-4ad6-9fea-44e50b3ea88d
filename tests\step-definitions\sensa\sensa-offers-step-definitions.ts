import { Then, When } from '@wdio/cucumber-framework';
import sensaOffersPage from '../../pages/sensa/sensa-offers.page.ts';
import sensaOffersPageObject from '../../page-object/sensa/sensa-offers.pageObject.ts';
import logger from '../../support/utils/logger.util.ts';

When(/^The user clicks on Offers Link$/, async function () {
    await sensaOffersPage.clickonofferslink();
    await expect(sensaOffersPageObject.txtoffertitle_sensa).toBeDisplayed();
    logger.info('Navigated to Offers Page Successfully');

});

Then(/^The user validates offers page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaOffersPage.offersPageValidation(filepath, sheetname, scenarioname);
    logger.info('Validated to Offers Page Successfully');
});

When(/^The user click on Claim Mobile Offers button$/, async function () {
    await sensaOffersPage.clickonclaimoffersbuttonandvalidatenavigationtospa();
    await expect(sensaOffersPageObject.btnclaimoffers_sensa).toBeDisplayed();
    logger.info('Navigated to SPA Page and Navigated back to offers page successfully');

});