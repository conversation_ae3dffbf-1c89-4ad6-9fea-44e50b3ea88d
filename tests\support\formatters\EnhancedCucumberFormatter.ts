// Helper type for testCaseFinished with result
interface TestCaseFinishedWithResult {
  result?: { status?: StatusType };
}

// tests/support/formatters/EnhancedCucumberFormatter.ts
import { Formatter, Status, IFormatterOptions } from '@cucumber/cucumber';
import * as messages from '@cucumber/messages';


type StatusType = typeof Status[keyof typeof Status];

interface StepResult {
  text: string;
  status: StatusType;
  duration: number;
  error?: string;
}

interface ScenarioResult {
  name: string;
  status: StatusType;
  duration: number;
  steps: StepResult[];
  tags: string[];
  attempts: number;
}

interface FeatureResult {
  name: string;
  scenarios: ScenarioResult[];
  duration: number;
}

export default class EnhancedCucumberFormatter extends Formatter {
  private features: Map<string, FeatureResult> = new Map();
  private currentFeature: string | null = null;
  private currentScenario: ScenarioResult | null = null;
  private stepStartTimes: Map<string, number> = new Map();
  private scenarioStartTimes: Map<string, number> = new Map();
  private featureStartTimes: Map<string, number> = new Map();

  constructor(options: IFormatterOptions) {
    super(options);

    // Set up event listeners for Cucumber events
    options.eventBroadcaster.on('envelope', (envelope: messages.Envelope) => {
      this.handleEnvelope(envelope);
    });
  }

  private handleEnvelope(envelope: messages.Envelope): void {
    if (envelope.testRunStarted) {
      this.onTestRunStarted();
    } else if (envelope.gherkinDocument) {
      this.onGherkinDocument(envelope.gherkinDocument);
    } else if (envelope.testCaseStarted) {
      this.onTestCaseStarted(envelope.testCaseStarted);
    } else if (envelope.testStepStarted) {
      this.onTestStepStarted(envelope.testStepStarted);
    } else if (envelope.testStepFinished) {
      this.onTestStepFinished(envelope.testStepFinished);
    } else if (envelope.testCaseFinished) {
      this.onTestCaseFinished(envelope.testCaseFinished);
    } else if (envelope.testRunFinished) {
      this.onTestRunFinished();
    }
  }

  private onTestRunStarted(): void {
    console.log('\n🚀 Enhanced Cucumber Test Execution Started');
    console.log('='.repeat(80));
    console.log(`📅 Started at: ${new Date().toISOString()}`);
    console.log('='.repeat(80));
  }

  private onGherkinDocument(gherkinDocument: messages.GherkinDocument): void {
    if (gherkinDocument.feature) {
      const featureName = gherkinDocument.feature.name;
      this.currentFeature = featureName;
      
      this.features.set(featureName, {
        name: featureName,
        scenarios: [],
        duration: 0,
      });

      this.featureStartTimes.set(featureName, Date.now());
      
      console.log(`\n📁 Feature: ${featureName}`);
      console.log('-'.repeat(60));
      
      // Show feature description if available
      if (gherkinDocument.feature.description) {
        console.log(`   📝 ${gherkinDocument.feature.description.trim()}`);
      }
    }
  }

  private onTestCaseStarted(testCaseStarted: messages.TestCaseStarted): void {
    // Find the scenario info from the test case
    const scenario = this.findScenarioForTestCase(testCaseStarted.testCaseId);
    if (scenario) {
      const scenarioName = scenario.name;
      const tags = scenario.tags?.map((tag: { name: string }) => tag.name) || [];
      
      this.currentScenario = {
        name: scenarioName,
        status: Status.UNKNOWN,
        duration: 0,
        steps: [],
        tags,
        attempts: this.getScenarioAttempts(scenarioName),
      };

      this.scenarioStartTimes.set(testCaseStarted.testCaseId, Date.now());
      
      console.log(`\n🎯 Scenario: ${scenarioName}`);
      
      if (tags.length > 0) {
        console.log(`   📋 Tags: ${tags.join(', ')}`);
      }
      
      if (this.currentScenario.attempts > 1) {
        console.log(`   🔄 Attempt: ${this.currentScenario.attempts}`);
      }
    }
  }

  private onTestStepStarted(testStepStarted: messages.TestStepStarted): void {
    const stepId = testStepStarted.testStepId;
    this.stepStartTimes.set(stepId, Date.now());
    
    // Find step text - only log if we have valid step text
    const stepText = this.findStepText(stepId);
    if (stepText && stepText !== 'Unknown Step') {
      console.log(`      📝 Starting: ${stepText}`);
    }
  }

  private onTestStepFinished(testStepFinished: messages.TestStepFinished): void {
    const stepId = testStepFinished.testStepId;
    const startTime = this.stepStartTimes.get(stepId) || Date.now();
    const duration = Date.now() - startTime;
    const stepText = this.findStepText(stepId) || `Step ${stepId.substring(0, 8)}`;
    
    let status: StatusType = Status.PASSED;
    let error: string | undefined;

    if (testStepFinished.testStepResult) {
      status = testStepFinished.testStepResult.status;
      if (testStepFinished.testStepResult.message) {
        error = testStepFinished.testStepResult.message;
      }
    }

    // Add step to current scenario
    if (this.currentScenario) {
      this.currentScenario.steps.push({
        text: stepText,
        status,
        duration,
        error,
      });
    }

    // Log step completion - only if we have meaningful step text
    if (stepText && !stepText.startsWith('Step ')) {
      const statusIcon = this.getStatusIcon(status);
      console.log(`      ${statusIcon} ${stepText} (${duration}ms)`);
      
      if (error && status === Status.FAILED) {
        console.log(`         💥 ${error.substring(0, 100)}...`);
      }
    }

    this.stepStartTimes.delete(stepId);
  }

  private onTestCaseFinished(testCaseFinished: messages.TestCaseFinished): void {
    if (!this.currentScenario || !this.currentFeature) return;

    // Use the correct property for testCaseId and result
    const testCaseId = (testCaseFinished as messages.TestCaseFinished).testCaseStartedId;
    const startTime = this.scenarioStartTimes.get(testCaseId) || Date.now();
    const duration = Date.now() - startTime;
    let status: StatusType = Status.PASSED;
    // Type guard for result property
    const hasResult = (obj: unknown): obj is TestCaseFinishedWithResult =>
      typeof obj === 'object' && obj !== null && 'result' in obj;
    let resultStatus: StatusType | undefined;
    if (hasResult(testCaseFinished) && testCaseFinished.result && testCaseFinished.result.status) {
      resultStatus = testCaseFinished.result.status;
    }
    if (resultStatus) {
      status = resultStatus;
    }

    this.currentScenario.status = status;
    this.currentScenario.duration = duration;

    // Add scenario to feature
    const feature = this.features.get(this.currentFeature);
    if (feature) {
      feature.scenarios.push({ ...this.currentScenario });
    }

    // Log scenario completion
    const statusIcon = this.getStatusIcon(status);
    const attemptsInfo = this.currentScenario.attempts > 1 ? ` (${this.currentScenario.attempts} attempts)` : '';
    console.log('   ' + statusIcon + ' ' + this.currentScenario.name + attemptsInfo + ` (${duration}ms)`);

    // Show step summary
    this.printScenarioStepSummary(this.currentScenario);

    this.scenarioStartTimes.delete(testCaseId);
    this.currentScenario = null;
  }

  private onTestRunFinished(): void {
    // Calculate feature durations
    this.features.forEach((feature, featureName) => {
      const startTime = this.featureStartTimes.get(featureName) || Date.now();
      feature.duration = Date.now() - startTime;
      
      console.log(`\n📊 Feature "${featureName}" completed in ${feature.duration}ms`);
      
      const passed = feature.scenarios.filter(s => s.status === Status.PASSED).length;
      const failed = feature.scenarios.filter(s => s.status === Status.FAILED).length;
      const skipped = feature.scenarios.filter(s => s.status === Status.SKIPPED).length;
      
      console.log(`   ✅ Passed: ${passed} | ❌ Failed: ${failed} | ⏭️ Skipped: ${skipped}`);
    });

    this.printComprehensiveSummary();
  }

  private printScenarioStepSummary(scenario: ScenarioResult): void {
    if (scenario.steps.length === 0) return;

    const passed = scenario.steps.filter(s => s.status === Status.PASSED).length;
    const failed = scenario.steps.filter(s => s.status === Status.FAILED).length;
    const skipped = scenario.steps.filter(s => s.status === Status.SKIPPED).length;

    console.log(`      📝 Steps: ${scenario.steps.length} total | ✅ ${passed} passed | ❌ ${failed} failed | ⏭️ ${skipped} skipped`);

    // Show failed steps
    const failedSteps = scenario.steps.filter(s => s.status === Status.FAILED);
    if (failedSteps.length > 0) {
      console.log('      🔍 Failed Steps:');
      failedSteps.forEach((step, index) => {
        console.log(`         ${index + 1}. ${step.text} (${step.duration}ms)`);
        if (step.error) {
          console.log(`            💥 ${step.error.substring(0, 80)}...`);
        }
      });
    }
  }

  private printComprehensiveSummary(): void {
    console.log('\n' + '='.repeat(100));
    console.log('📊 ENHANCED CUCUMBER TEST EXECUTION SUMMARY');
    console.log('='.repeat(100));

    // Calculate totals
    let totalScenarios = 0;
    let totalPassedScenarios = 0;
    let totalFailedScenarios = 0;
    let totalSkippedScenarios = 0;
    let totalSteps = 0;
    let totalDuration = 0;

    this.features.forEach((feature) => {
      totalScenarios += feature.scenarios.length;
      totalDuration += feature.duration;
      
      for (const scenario of feature.scenarios) {
        totalSteps += scenario.steps.length;
        
        switch (scenario.status) {
          case Status.PASSED:
            totalPassedScenarios++;
            break;
          case Status.FAILED:
            totalFailedScenarios++;
            break;
          case Status.SKIPPED:
            totalSkippedScenarios++;
            break;
        }
      }
    });

    // Print overall summary
    console.log('\n🎯 EXECUTION OVERVIEW:');
    console.log(`   📅 Completed at: ${new Date().toISOString()}`);
    console.log(`   ⏱️  Total Duration: ${totalDuration}ms (${(totalDuration / 1000).toFixed(2)}s)`);
    console.log(`   📁 Features: ${this.features.size}`);
    console.log(`   🎭 Scenarios: ${totalScenarios} | ✅ ${totalPassedScenarios} passed | ❌ ${totalFailedScenarios} failed | ⏭️ ${totalSkippedScenarios} skipped`);
    console.log(`   📝 Steps: ${totalSteps}`);
    console.log(`   📊 Success Rate: ${totalScenarios > 0 ? ((totalPassedScenarios / totalScenarios) * 100).toFixed(1) : 0}%`);

    // Print feature breakdown
    console.log('\n📁 FEATURE BREAKDOWN:');
    this.features.forEach((feature, featureName) => {
      console.log(`\n   📁 ${featureName}:`);
      console.log(`      ⏱️  Duration: ${feature.duration}ms`);
      console.log(`      🎭 Scenarios: ${feature.scenarios.length}`);

      feature.scenarios.forEach((scenario, index) => {
        const statusIcon = this.getStatusIcon(scenario.status);
        const attemptsInfo = scenario.attempts > 1 ? ` (${scenario.attempts} attempts)` : '';
        const stepInfo = scenario.steps.length > 0 ? ` [${scenario.steps.length} steps]` : '';
        const tagsInfo = scenario.tags.length > 0 ? ` ${scenario.tags.join(' ')}` : '';
        
        console.log(`         ${index + 1}. ${statusIcon} ${scenario.name}${attemptsInfo}${stepInfo}${tagsInfo} (${scenario.duration}ms)`);
        
        // Show failed step details
        if (scenario.status === Status.FAILED) {
          const failedSteps = scenario.steps.filter(s => s.status === Status.FAILED);
          if (failedSteps.length > 0) {
            console.log(`            🔍 Failed Steps: ${failedSteps.map(s => s.text).join(', ')}`);
          }
        }
      });
    });

    console.log('\n' + '='.repeat(100));
    
    // Final status
    if (totalFailedScenarios > 0) {
      console.log(`❌ TEST EXECUTION FAILED - ${totalFailedScenarios} scenario(s) failed`);
    } else {
      console.log(`✅ TEST EXECUTION PASSED - All ${totalPassedScenarios} scenario(s) passed`);
    }
    
    console.log('='.repeat(100));
  }

  private getStatusIcon(status: StatusType): string {
    switch (status) {
      case Status.PASSED:
        return '✅';
      case Status.FAILED:
        return '❌';
      case Status.SKIPPED:
        return '⏭️';
      case Status.PENDING:
        return '⏸️';
      default:
        return '❓';
    }
  }

  private findScenarioForTestCase(_testCaseId: string): { name: string; tags: { name: string }[] } {
    // Try to get current scenario from the Cucumber world if available
    try {
      // This could be enhanced with proper pickle mapping in the future
      // For now, we'll use a fallback approach
      const scenarioName = this.currentScenario?.name || 'Current Scenario';
      return { name: scenarioName, tags: [] };
    } catch {
      return { name: 'Unknown Scenario', tags: [] };
    }
  }

  private findStepText(_stepId: string): string | null {
    // Return null for now - step text will be captured from step definitions
    // This allows the formatter to work without complex pickle mapping
    return null;
  }

  private getScenarioAttempts(scenarioName: string): number {
    // Try to get attempts from global retry tracking
    try {
      const global = globalThis as unknown as { currentScenarioAttempts?: Map<string, number> };
      if (global.currentScenarioAttempts && global.currentScenarioAttempts.has(scenarioName)) {
        return global.currentScenarioAttempts.get(scenarioName) as number;
      }
    } catch {
      // Fallback to 1 if retry tracking not available
    }
    return 1;
  }
}