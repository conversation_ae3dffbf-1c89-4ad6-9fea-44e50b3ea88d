import WDIOReporter, { RunnerStats, SuiteStats, TestStats } from '@wdio/reporter';

interface StepInfo {
  text: string;
  status: 'passed' | 'failed' | 'skipped' | 'pending';
  duration: number;
  error?: string;
}

interface ScenarioInfo {
  name: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  attempts: number;
  steps: StepInfo[];
  tags: string[];
  error?: string;
}

interface FeatureInfo {
  name: string;
  scenarios: ScenarioInfo[];
  totalDuration: number;
  passedScenarios: number;
  failedScenarios: number;
  skippedScenarios: number;
}

export default class EnhancedCucumberReporter extends WDIOReporter {
  private features: Map<string, FeatureInfo> = new Map();
  private currentFeature: string | null = null;
  private currentScenario: string | null = null;
  private currentSteps: StepInfo[] = [];
  private scenarioStartTime: number = 0;
  private stepStartTime: number = 0;

  constructor(options: object) {
    super(options);
    
    console.log('🔗 Enhanced Cucumber Reporter initialized');
  }

  onRunnerStart(runner: RunnerStats): void {
    console.log('\n🚀 Enhanced WebdriverIO Test Execution Started');
    console.log('='.repeat(80));
    console.log(`📅 Started at: ${new Date().toISOString()}`);
    console.log(`🔧 Runner: ${runner.cid}`);
    console.log(`📊 Capabilities: ${JSON.stringify(runner.capabilities, null, 2)}`);
    console.log('='.repeat(80));
  }

  onSuiteStart(suite: SuiteStats): void {
    // Extract feature name from suite title
    const featureName = this.extractFeatureName(suite.title);
    this.currentFeature = featureName;

    if (!this.features.has(featureName)) {
      this.features.set(featureName, {
        name: featureName,
        scenarios: [],
        totalDuration: 0,
        passedScenarios: 0,
        failedScenarios: 0,
        skippedScenarios: 0,
      });
    }

    console.log(`\n📁 Feature: ${featureName}`);
    console.log('-'.repeat(60));
  }

  onTestStart(test: TestStats): void {
    // Extract scenario name
    const scenarioName = this.extractScenarioName(test.title);
    this.currentScenario = scenarioName;
    this.currentSteps = [];
    this.scenarioStartTime = Date.now();

    console.log(`\n🎯 Scenario: ${scenarioName}`);
    
    // Extract tags if available
    const tags = this.extractTags(test.title);
    if (tags.length > 0) {
      console.log(`   📋 Tags: ${tags.join(', ')}`);
    }
  }

  onTestPass(test: TestStats): void {
    this.finalizeScenario(test, 'passed');
  }

  onTestFail(test: TestStats): void {
    this.finalizeScenario(test, 'failed', test.error?.message);
  }

  onTestSkip(test: TestStats): void {
    this.finalizeScenario(test, 'skipped');
  }

  onSuiteEnd(suite: SuiteStats): void {
    if (this.currentFeature) {
      const feature = this.features.get(this.currentFeature);
      if (feature) {
        feature.totalDuration = suite.duration;
        console.log(`\n📊 Feature "${this.currentFeature}" completed in ${suite.duration}ms`);
        console.log(`   ✅ Passed: ${feature.passedScenarios} | ❌ Failed: ${feature.failedScenarios} | ⏭️ Skipped: ${feature.skippedScenarios}`);
      }
    }
  }

  onRunnerEnd(runner: RunnerStats): void {
    this.printComprehensiveSummary(runner);
  }

  private extractFeatureName(title: string): string {
    // Remove common prefixes and clean up the title
    const cleaned = title
      .replace(/^Feature:\s*/i, '')
      .replace(/\.feature$/, '')
      .trim();
    
    return cleaned || 'Unknown Feature';
  }

  private extractScenarioName(title: string): string {
    // Remove common prefixes and clean up
    const cleaned = title
      .replace(/^Scenario:\s*/i, '')
      .replace(/^Scenario Outline:\s*/i, '')
      .trim();
    
    return cleaned || 'Unknown Scenario';
  }

  private extractTags(title: string): string[] {
    // Extract tags from title if present (format: @tag1 @tag2)
    const tagMatch = title.match(/@\w+/g);
    return tagMatch || [];
  }

  private finalizeScenario(test: TestStats, status: 'passed' | 'failed' | 'skipped', error?: string): void {
    if (!this.currentFeature || !this.currentScenario) return;

    const duration = Date.now() - this.scenarioStartTime;
    const feature = this.features.get(this.currentFeature);
    
    if (feature) {
      const scenario: ScenarioInfo = {
        name: this.currentScenario,
        status,
        duration,
        attempts: this.getScenarioAttempts(this.currentScenario),
        steps: [...this.currentSteps],
        tags: this.extractTags(test.title),
        error,
      };

      feature.scenarios.push(scenario);

      // Update feature counters
      switch (status) {
        case 'passed':
          feature.passedScenarios++;
          break;
        case 'failed':
          feature.failedScenarios++;
          break;
        case 'skipped':
          feature.skippedScenarios++;
          break;
      }

      // Print scenario result
      const statusIcon = status === 'passed' ? '✅' : status === 'failed' ? '❌' : '⏭️';
      const attemptsInfo = scenario.attempts > 1 ? ` (${scenario.attempts} attempts)` : '';
      console.log(`   ${statusIcon} ${this.currentScenario}${attemptsInfo} (${duration}ms)`);

      // Print step summary for this scenario
      if (this.currentSteps.length > 0) {
        this.printScenarioStepSummary(scenario);
      }

      if (error) {
        console.log(`      💥 Error: ${error.substring(0, 100)}...`);
      }
    }
  }

  private getScenarioAttempts(scenarioName: string): number {
    // Try to get attempts from global retry tracking
    try {
      const global = globalThis as Record<string, unknown>;
      const attemptsMap = global.currentScenarioAttempts;
      if (attemptsMap instanceof Map && attemptsMap.has(scenarioName)) {
        return attemptsMap.get(scenarioName);
      }
    } catch {
      // Fallback to 1 if retry tracking not available
    }
    return 1;
  }

  private printScenarioStepSummary(scenario: ScenarioInfo): void {
    const passedSteps = scenario.steps.filter(s => s.status === 'passed').length;
    const failedSteps = scenario.steps.filter(s => s.status === 'failed').length;
    const skippedSteps = scenario.steps.filter(s => s.status === 'skipped').length;

    if (scenario.steps.length > 0) {
      console.log(`      📝 Steps: ${scenario.steps.length} total | ✅ ${passedSteps} passed | ❌ ${failedSteps} failed | ⏭️ ${skippedSteps} skipped`);
      
      // Show failed steps
      const failedStepDetails = scenario.steps.filter(s => s.status === 'failed');
      if (failedStepDetails.length > 0) {
        console.log('      🔍 Failed Steps:');
        failedStepDetails.forEach((step, index) => {
          console.log(`         ${index + 1}. ${step.text} (${step.duration}ms)`);
          if (step.error) {
            console.log(`            💥 ${step.error.substring(0, 80)}...`);
          }
        });
      }
    }
  }

  private printComprehensiveSummary(runner: RunnerStats): void {
    console.log('\n' + '='.repeat(100));
    console.log('📊 ENHANCED WEBDRIVERIO TEST EXECUTION SUMMARY');
    console.log('='.repeat(100));

    // Overall statistics
    const totalFeatures = this.features.size;
    let totalScenarios = 0;
    let totalPassedScenarios = 0;
    let totalFailedScenarios = 0;
    let totalSkippedScenarios = 0;
    let totalSteps = 0;
    let totalDuration = 0;

    // Calculate totals
    for (const feature of this.features.values()) {
      totalScenarios += feature.scenarios.length;
      totalPassedScenarios += feature.passedScenarios;
      totalFailedScenarios += feature.failedScenarios;
      totalSkippedScenarios += feature.skippedScenarios;
      totalDuration += feature.totalDuration;
      
      for (const scenario of feature.scenarios) {
        totalSteps += scenario.steps.length;
      }
    }

    // Print overall summary
    console.log('\n🎯 EXECUTION OVERVIEW:');
    console.log(`   📅 Completed at: ${new Date().toISOString()}`);
    console.log(`   ⏱️  Total Duration: ${totalDuration}ms (${(totalDuration / 1000).toFixed(2)}s)`);
    console.log(`   📁 Features: ${totalFeatures}`);
    console.log(`   🎭 Scenarios: ${totalScenarios} | ✅ ${totalPassedScenarios} passed | ❌ ${totalFailedScenarios} failed | ⏭️ ${totalSkippedScenarios} skipped`);
    console.log(`   📝 Steps: ${totalSteps}`);
    console.log(`   📊 Success Rate: ${totalScenarios > 0 ? ((totalPassedScenarios / totalScenarios) * 100).toFixed(1) : 0}%`);

    // Print feature-by-feature breakdown
    console.log('\n📁 FEATURE BREAKDOWN:');
    for (const [featureName, feature] of this.features.entries()) {
      console.log(`\n   📁 ${featureName}:`);
      console.log(`      ⏱️  Duration: ${feature.totalDuration}ms`);
      console.log(`      🎭 Scenarios: ${feature.scenarios.length} | ✅ ${feature.passedScenarios} | ❌ ${feature.failedScenarios} | ⏭️ ${feature.skippedScenarios}`);
      
      // List scenarios with their step counts
      feature.scenarios.forEach((scenario, index) => {
        const statusIcon = scenario.status === 'passed' ? '✅' : scenario.status === 'failed' ? '❌' : '⏭️';
        const attemptsInfo = scenario.attempts > 1 ? ` (${scenario.attempts} attempts)` : '';
        const stepInfo = scenario.steps.length > 0 ? ` [${scenario.steps.length} steps]` : '';
        const tagsInfo = scenario.tags.length > 0 ? ` ${scenario.tags.join(' ')}` : '';
        
        console.log(`         ${index + 1}. ${statusIcon} ${scenario.name}${attemptsInfo}${stepInfo}${tagsInfo} (${scenario.duration}ms)`);
        
        // Show step details for failed scenarios
        if (scenario.status === 'failed' && scenario.steps.length > 0) {
          const failedSteps = scenario.steps.filter(s => s.status === 'failed');
          if (failedSteps.length > 0) {
            console.log(`            🔍 Failed Steps: ${failedSteps.map(s => s.text).join(', ')}`);
          }
        }
      });
    }

    // Print retry information if available
    this.printRetryInformation();

    // Print device/capability information
    this.printCapabilityInformation(runner);

    console.log('\n' + '='.repeat(100));
    
    // Final status
    if (totalFailedScenarios > 0) {
      console.log(`❌ TEST EXECUTION FAILED - ${totalFailedScenarios} scenario(s) failed`);
    } else {
      console.log(`✅ TEST EXECUTION PASSED - All ${totalPassedScenarios} scenario(s) passed`);
    }
    
    console.log('='.repeat(100));
  }

  private printRetryInformation(): void {
    try {
      // Try to get retry information from enhanced systems
      const global = globalThis as Record<string, unknown>;
      let hasRetryInfo = false;

      const attemptsMap = global.currentScenarioAttempts;
      if (attemptsMap instanceof Map && attemptsMap.size > 0) {
        console.log('\n🔄 RETRY INFORMATION:');
        hasRetryInfo = true;
        
        let totalRetries = 0;
        let scenariosWithRetries = 0;
        
        for (const [scenarioName, attempts] of attemptsMap.entries()) {
          if (attempts > 1) {
            scenariosWithRetries++;
            totalRetries += (attempts - 1);
            console.log(`      🔄 ${scenarioName}: ${attempts} attempts (${attempts - 1} retries)`);
          }
        }
        
        console.log(`      📊 Total Retries: ${totalRetries} across ${scenariosWithRetries} scenario(s)`);
      }

      if (!hasRetryInfo) {
        // Check if any scenarios had multiple attempts based on our feature data
        for (const feature of this.features.values()) {
          for (const scenario of feature.scenarios) {
            if (scenario.attempts > 1) {
              if (!hasRetryInfo) {
                console.log('\n🔄 RETRY INFORMATION:');
                hasRetryInfo = true;
              }
              console.log(`      🔄 ${scenario.name}: ${scenario.attempts} attempts`);
            }
          }
        }
      }
    } catch {
      // Silently handle any errors in retry reporting
      console.log('\n🔄 RETRY INFORMATION: Unable to retrieve retry details');
    }
  }

  private printCapabilityInformation(runner: RunnerStats): void {
    console.log('\n📱 DEVICE/BROWSER INFORMATION:');
    
    try {
      const capabilities = runner.capabilities as Record<string, unknown>;
      
      const deviceName = (capabilities['appium:deviceName'] as string) || 
                        (capabilities.deviceName as string) || 
                        'Unknown Device';
      const platformName = (capabilities.platformName as string) || 'Unknown Platform';
      const platformVersion = (capabilities['appium:platformVersion'] as string) || 
                             (capabilities.platformVersion as string) || 
                             'Unknown Version';
      const browserName = (capabilities.browserName as string) || 'Unknown Browser';
      
      console.log(`      📱 Device: ${deviceName}`);
      console.log(`      🖥️  Platform: ${platformName} ${platformVersion}`);
      console.log(`      🌐 Browser: ${browserName}`);
      
      // Add Sauce Labs information if available
      const sauceOptions = capabilities['sauce:options'] as Record<string, unknown>;
      if (sauceOptions?.build) {
        console.log(`      🏗️  Build: ${sauceOptions.build}`);
      }
      
    } catch {
      console.log('      ⚠️  Could not retrieve device information');
    }
  }

  // Hook into step execution if possible
  onHookStart(): void {
    this.stepStartTime = Date.now();
  }

  onHookEnd(): void {
    // Handle hook completion
  }

  // Method to track step information (can be called from step definitions)
  public trackStep(stepText: string, status: string, error?: string): void {
    const statusTyped = status as 'passed' | 'failed' | 'skipped' | 'pending';
    const duration = Date.now() - this.stepStartTime;
    
    this.currentSteps.push({
      text: stepText,
      status: statusTyped,
      duration,
      error,
    });
    
    const statusIcon = statusTyped === 'passed' ? '✅' : statusTyped === 'failed' ? '❌' : statusTyped === 'skipped' ? '⏭️' : '⏸️';
    console.log(`      ${statusIcon} ${stepText} (${duration}ms)`);
    
    if (error) {
      console.log(`         💥 ${error.substring(0, 100)}...`);
    }
  }
}