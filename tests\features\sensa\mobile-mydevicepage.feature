Feature: My Device Page Validation for Sensa Brand

    Scenario Outline: Vaidate the Content and links of My Device page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the application successfully
        When The user clicks on My device link
        Then The user Validates My Device Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user clicks on links
        Then The user validates that successfully logged out

        @SensaDevice_Validation_QA
        Examples:
            | Brand | URL                             | Username                           | Password  | filename              | sheetname | scenarioname             |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | My Device | Validate My Device Pages |

        @SensaDevice_Validation_PROD
        Examples:
            | Brand | URL                       | Username                             | Password  | filename              | sheetname | scenarioname             |
            | Sensa | https://www.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | My Device | Validate My Device Pages |
