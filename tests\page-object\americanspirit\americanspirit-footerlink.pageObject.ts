class NasFooterlinkPageObject {

  //FAQ

  get lnkpostfaq_nas() { return $('(//span[contains(text(),"FAQ")])[9]'); }
  get lnkPreFAQ_nas() { return $('//*[contains(text(),"FAQ")]'); }
  get contactUsHeader() { return $('//h1[contains(text(), "Contact Us")]'); }
  get postcontactUsHeader() { return $('//h1[contains(text(), "CONTACT US")]'); }
  get lblfurtherassistance() { return $('//p[contains(text(),"Click here for ")]//parent::div'); }
  get lblfrequentQuestions() { return $('//h2[normalize-space()="FREQUENTLY ASKED QUESTIONS"]'); }
  get lblonthisPage_nas() { return $('//p[contains(text(),"On this page:")]//parent::div'); }

  get lblproducts_nas() { return $('//b[text()="Products"]'); }
  get lblwarningQ1_nas() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[1]'); }
  get lblwarningQ2_nas() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[2]'); }
  get lblwarningQ3_nas() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[3]'); }
  get lblwarningQ4_nas() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[4]'); }
  get lblwarningQ5_nas() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[5]'); }
  get lblwarningQ6_nas() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[6]'); }
  get lblwarningQ7_nas() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[7]'); }
  get lblwarningQ8_nas() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[8]'); }
  get lblwarningQ9_nas() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[9]'); }
  get lblwarningQ10_nas() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[10]'); }
  get lblwarningQ11_nas() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[11]'); }
  get lblwarningQ12_nas() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[12]'); }
  get lblwarningQ13_nas() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[13]'); }
  get lblwarningQ14_nas() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[14]'); }
  get lblwarningQ15_nas() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[15]'); }
  get lblwarningQ16_nas() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[16]'); }
  get lblwarningQ17_nas() { return $('(//*[@class="cmp-accordion-item__headerText"]/span)[17]'); }

  get lblwarningA1_nas() { return $('(//*[@class="cmp-accordion-item__content"]//p)[1]'); }
  get lblwarningA2_nas() { return $('(//*[@class="cmp-accordion-item__content"]//p)[2]'); }
  get lblwarningA3_nas() { return $('(//*[@class="cmp-accordion-item__content"]//p)[3]'); }
  get lblwarningA4_nas() { return $('(//*[@class="cmp-accordion-item__content"]//p)[4]'); }
  get lblwarningA5_nas() { return $('(//*[@class="cmp-accordion-item__content"]//p)[5]'); }
  get lblwarningA6_nas() { return $('(//*[@class="cmp-accordion-item__content"]//p)[6]'); }
  get lblwarningA7_nas() { return $('(//*[@class="cmp-accordion-item__content"]//p)[7]'); }
  get lblwarningA8_nas() { return $('(//*[@class="cmp-accordion-item__content"]//p)[8]'); }
  get lblwarningA9_nas() { return $('(//*[@class="cmp-accordion-item__content"]//p)[9]'); }
  get lblwarningA10_nas() { return $('(//*[@class="cmp-accordion-item__content"]//p)[10]'); }
  get lblwarningA11_nas() { return $('(//*[@class="cmp-accordion-item__content"]//p)[11]'); }
  get lblwarningA12_nas() { return $('(//*[@class="cmp-accordion-item__content"]//p)[12]'); }
  get lblwarningA13_nas() { return $('(//*[@class="cmp-accordion-item__content"]//p)[13]'); }
  get lblwarningA14_nas() { return $('(//*[@class="cmp-accordion-item__content"]//p)[14]'); }
  get lblwarningA15_nas() { return $('(//*[@class="cmp-accordion-item__content"]//p)[15]'); }
  get lblwarningA16_nas() { return $('(//*[@class="cmp-accordion-item__content"]//p)[16]'); }
  get lblwarningA17_nas() { return $('(//*[@class="cmp-accordion-item__content"]//p)[17]'); }
  get listFaqSection1_nas() { return $('(//*[@class="cmp-accordion-item__content"]//ul)[1]'); }
  get listFaqSection2_nas() { return $('(//*[@class="cmp-accordion-item__content"]//ul)[2]'); }

  get lblProducts_nas() { return $('(//*[@class="cmp-section__container"]//b)[1]'); }
  get lblLoginAndPassword_nas() { return $('(//*[@class="cmp-section__container"]//b)[2]'); }
  get lblOffersAndPromotions_nas() { return $('(//*[@class="cmp-section__container"]//b)[3]'); }
  get lblAgeVerification_nas() { return $('(//*[@class="cmp-section__container"]//b)[4]'); }
  get lblPrivacy_nas() { return $('(//*[@class="cmp-section__container"]//b)[5]'); }
  get lblTroubleshooting_nas() { return $('(//*[@class="cmp-section__container"]//b)[6]'); }
  get lblNeedFurtherAssistance_nas() { return $('(//*[@class="cmp-section__container"]//b)[7]'); }
  get lblHaveQuestions_nas() { return $('(//*[@class="cmp-section__container"]//b)[8]'); }
  get listCmpText3_nas() { return $('(//*[@class="cmp-text"]//ul)[3]'); }

  //contact us 

  get hdrfaq_ContactUs_nas() { return $('//h1[text()="Contact Us"]'); }

  //Site Requirement locator
  get hdrsitereq_nas() { return $('//h1[normalize-space()="Site Requirements"]'); }
  get hdrmobilereq_nas() { return $('(//*[@class="cmp-section__container"]//h2)[1]'); }
  get lblmobilebrowser_nas() { return $('(//*[@class="cmp-section__container"]//h3)[1]'); }
  get lblmobilebrowserdes_nas() { return $('(//*[@class="cmp-section__container"]//p)[2]'); }
  get lblmobileos_nas() { return $('(//*[@class="cmp-section__container"]//h3)[2]'); }
  get lblmobileosdes_nas() { return $('(//*[@class="cmp-section__container"]//p)[3]'); }
  get hdrdesktopreq_nas() { return $('(//*[@class="cmp-section__container"]//h2)[2]'); }
  get lbldesktopbrowser_nas() { return $('(//*[@class="cmp-section__container"]//h3)[3]'); }
  get lbldesktopbrowserdes_nas() { return $('(//*[@class="cmp-section__container"]//p)[4]'); }
  get lbldesktopOS_nas() { return $('(//*[@class="cmp-section__container"]//h3)[4]'); }
  get lblimportantdesc_nas() { return $('(//*[@class="cmp-section__container"]//p)[5]'); }
  get hdreqdesktopandmobile_nas() { return $('(//*[@class="cmp-section__container"]//h2)[3]'); }
  get hdrjavascript_nas() { return $('(//*[@class="cmp-section__container"]//h3)[5]'); }
  get lbljavscriptdesc_nas() { return $('(//*[@class="cmp-section__container"]//p)[6]'); }
  get hdrcookies_nas() { return $('(//*[@class="cmp-section__container"]//h3)[6]'); }
  get lblcookiesdesc_nas() { return $('(//*[@class="cmp-section__container"]//p)[7]'); }
  get lnkmsg_nas() { return $('(//*[@class="cmp-navigation__group"]//li)[7]'); }
  get lnkspirit_nas() { return $('(//*[@class="cmp-navigation__group"]//li)[8]'); }

  get lnkprivacyPolicy_nas() { return $('//*[contains(text(),"Privacy Policy")]'); }
  get lnktextMessaging_nas() { return $('(//*[text()="Text Messaging Terms & Conditions"])'); }
  get hdrtextMessaging_nas() { return $('//*[contains(text(),"TEXT MESSAGING TERMS AND CONDITIONS")]'); }

  //Spririt Circle
  get hdrSpiritCircleTerms_nas() { return $('//*[text()="The Spirit Circle Terms & Conditions"]'); }
  get hdrSpiritCircleProgramTerms_nas() { return $('//h1[contains(text(), "The SPIRIT circle® Program Terms")]'); }
  get prodhdrSpiritCircleProgramTerms_nas() { return $('//h1[contains(text(), "The SPIRIT Circle")]'); }
  get lbleligibilitydesc1_nas() { return $$('.cmp-section__container p')[20]; }
  get lbleligibilitydesc2_nas() { return $$('.cmp-text')[3]; }
  get lblhowewecommunicatedesc1_nas() { return $$('.cmp-text')[4]; }
  get lbljoiningpgrdesc1_nas() { return $$('.cmp-text')[5]; }
  get lblhowtoearndesc1_nas() { return $$('.cmp-text')[6]; }
  get lblhowtoredeemdesc1_nas() { return $$('.cmp-text')[7]; }
  get lblhowtoavoiddesc1_nas() { return $$('.cmp-text')[8]; }
  get lblavailablerwardsdesc1_nas() { return $$('.cmp-text')[9]; }
  get lblreserveddesc1_nas() { return $$('.cmp-text')[10]; }
  get lblprogramdesc1_nas() { return $$('.cmp-text')[11]; }
  get lbladditionaldesc1_nas() { return $$('.cmp-text')[12]; }

  get lblclosingdesc1_nas() { return $$('.cmp-text')[13]; }
  get lbldisclaimerdesc1_nas() { return $$('.cmp-text')[14]; }
  get lblchangesdesc1_nas() { return $$('.cmp-text')[15]; }
  get lblindemnitydesc1_nas() { return $$('.cmp-text')[16]; }
  get lblchoiceoflawdesc1_nas() { return $$('.cmp-text')[17]; }


}
export default new NasFooterlinkPageObject();