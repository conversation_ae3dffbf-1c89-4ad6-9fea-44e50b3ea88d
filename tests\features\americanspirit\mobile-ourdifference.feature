Feature: Our Difference Validation for Nas Website

    Scenario Outline: Validate Discover Page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the nas application successfully
        When The user clicks on Discover
        Then The user Validates Nas Discover Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user validates that successfully logged out

        @NasDiscoverPage_Validation_QA
        Examples:
            | Brand | URL                                  | Username                           | Password  | filename            | sheetname      | scenarioname             |
            | Nas   | https://aem-stage.americanspirit.com | <EMAIL> | Password1 | aem-mobile-nas.json | Our Difference | Discover Page Validation |

        @NasDiscoverPage_Validation_PROD
        Examples:
            | Brand | URL                            | Username                                 | Password  | filename            | sheetname      | scenarioname             |
            | Nas   | https://www.americanspirit.com | <EMAIL> | Password1 | aem-mobile-nas.json | Our Difference | Discover Page Validation |


    Scenario Outline: Validate Our Partners Page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the nas application successfully
        When The user clicks on Our Partner
        Then The user Validates Nas Our Partner Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user validates that successfully logged out

        @NasOurPartnerPage_Validation_QA
        Examples:
            | Brand | URL                                  | Username                           | Password  | filename            | sheetname      | scenarioname                 |
            | Nas   | https://aem-stage.americanspirit.com | <EMAIL> | Password1 | aem-mobile-nas.json | Our Difference | Our Partners Page Validation |

        @NasOurPartnerPage_Validation_PROD
        Examples:
            | Brand | URL                            | Username                                 | Password  | filename            | sheetname      | scenarioname                 |
            | Nas   | https://www.americanspirit.com | <EMAIL> | Password1 | aem-mobile-nas.json | Our Difference | Our Partners Page Validation |

    Scenario Outline: Validate Our Tobacco Page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the nas application successfully
        When The user clicks on Our Tobacco
        Then The user Validates Nas Our Tobacco Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user validates that successfully logged out

        @NasOurTobaccoPage_Validation_QA
        Examples:
            | Brand | URL                                  | Username                           | Password  | filename            | sheetname      | scenarioname                 |
            | Nas   | https://aem-stage.americanspirit.com | <EMAIL> | Password1 | aem-mobile-nas.json | Our Difference | Our Tobacco Page Validation |

        @NasOurTobaccoPage_Validation_PROD
        Examples:
            | Brand | URL                            | Username                                 | Password  | filename            | sheetname      | scenarioname                 |
            | Nas   | https://www.americanspirit.com | <EMAIL> | Password1 | aem-mobile-nas.json | Our Difference | Our Tobacco Page Validation |