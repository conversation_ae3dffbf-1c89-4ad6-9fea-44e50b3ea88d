import elementActions from '../../support/actions/element.actions.ts';
import sensaFooterlinkPage from './footerlink.page.ts';
import logger from '../../support/utils/logger.util.ts';
import storelocatorObject from '../../page-object/sensa/sensa-storelocatorObject.ts';



class Storlocator {


  public async navigateToStorelocatorPage() {
    try {
      await elementActions.assertion(storelocatorObject.hamburgerMenu);
      await elementActions.click(storelocatorObject.hamburgerMenu);
      const url = await browser.getUrl();
      if (url.includes('americanspirit')) {
        await elementActions.clickusingJavascript(storelocatorObject.sensaStorelocator_NAS);
        await elementActions.waitForDisplayed(storelocatorObject.sensafindStoresCamel);
        await expect(storelocatorObject.sensafindStoresCamel).toBeDisplayed();
        await elementActions.assertion(storelocatorObject.sensafindStoresCamel);
      } else {
        await elementActions.click(storelocatorObject.sensaStorelocator);

        await elementActions.waitForClickable(storelocatorObject.sensafindastore);
        await expect(storelocatorObject.sensafindastore).toBeDisplayed();
        await elementActions.assertion(storelocatorObject.sensafindastore);
      }
    } catch (error) {
      console.error('Error in navigateToStorelocatorPage:', error);
      throw error;
    }
  }



  public async usemylocation() {
    try {
      const currentUrl = await browser.getUrl();
       if (currentUrl.includes('americanspirit')) {
        await elementActions.assertion(storelocatorObject.btnfindme_nas);
      await elementActions.clickusingJavascript(storelocatorObject.btnfindme_nas);
       }else{
      await elementActions.assertion(storelocatorObject.sensaUsemyLocation);
      await elementActions.clickusingJavascript(storelocatorObject.sensaUsemyLocation);
       }
      await sensaFooterlinkPage.allowpopup();
      await elementActions.assertion(storelocatorObject.sensaZipcode);
      if (currentUrl.includes('camel') || currentUrl.includes('americanspirit')) {
        await elementActions.assertion(storelocatorObject.sensafindStoresCamel);
      } else {
        await elementActions.assertion(storelocatorObject.sensafindStores);
        await elementActions.assertion(storelocatorObject.sensafilterbyproducttext);
      }
    } catch (error) {
      console.error('Error in usemylocation:', error);
      throw error;
    }
  }


  public async enterZipcode(Zipcode: string) {
    try {
      const currentUrl = await browser.getUrl();
       if (currentUrl.includes('americanspirit')) {
        await elementActions.assertion(storelocatorObject.btnfindme_nas);
       }else{
      await elementActions.assertion(storelocatorObject.sensaUsemyLocation);
       }
      await elementActions.assertion(storelocatorObject.sensaZipcode);
      await elementActions.setValue(storelocatorObject.sensaZipcode, Zipcode);
      if (currentUrl.includes('camel') || currentUrl.includes('americanspirit')) {
        await elementActions.clickusingJavascript(storelocatorObject.sensafindStoresCamel);
        await elementActions.assertion(storelocatorObject.sensafilterbyproductCamel);
      } else {
        await elementActions.clickusingJavascript(storelocatorObject.sensafindStores);
        await elementActions.assertion(storelocatorObject.sensafilterbyproduct);
        await elementActions.assertion(storelocatorObject.sensafilterbyproducttext);
      }

    } catch (error) {
      console.error('Error in enterZipcode:', error);
      throw error;
    }
  }


  public async validateAllStores() {
    try {
      for (let i = 1; i <= 3; i++) {
        await this.validateStoreCardByIndex(i);
      }
      console.log('Validated all initial stores');
    } catch (error) {
      logger.error('Failed to validate all stores', { error });
      throw error;
    }
  }

  public async validateLoadMoreStores() {
    try {
      await storelocatorObject.sensaloadMore.scrollIntoView();
      await elementActions.click(storelocatorObject.sensaloadMore);

      for (let i = 11; i <= 13; i++) {
        await this.validateStoreCardByIndex(i);
      }

      console.log('Validated Load More store results');
    } catch (error) {
      logger.error('Failed to validate Load More store results', { error });
      throw error;
    }
  }
  private async validateStoreCardByIndex(index: number) {
    const numberLocator = storelocatorObject.getnumberLocator(index);
    const nameLocator = storelocatorObject.getnameLocator(index);
    const addressLine1 = storelocatorObject.getaddressLine1(index);
    const addressLine2 = storelocatorObject.getaddressLine2(index);
    const directionLink = storelocatorObject.getdirectionLink(index);
    const distanceLocator = storelocatorObject.getdistanceLocator(index);
    const contactNumber = storelocatorObject.getcontactNumber(index);

    const locators = [
      { name: 'Store Number', locator: numberLocator },
      { name: 'Store Name', locator: nameLocator },
      { name: 'Address Line 1', locator: addressLine1 },
      { name: 'Address Line 2', locator: addressLine2 },
      { name: 'Contact Number', locator: contactNumber },
      { name: 'Get Directions Link', locator: directionLink },
      { name: 'Distance', locator: distanceLocator },
    ];

    for (const { name, locator } of locators) {
      const isDisplayed = await locator.isDisplayed().catch(() => false);
      if (isDisplayed) {
        await elementActions.assertion(locator);
        console.log(`${name} is displayed for index ${index}`);
      } else {
        console.warn(`${name} is NOT displayed for index ${index}`);
      }
    }
  }

  public async ClickonFilterbyProductvalidateAll6ProductsFromFilterByProduct() {
    try {
      const url = await browser.getUrl();
      if (url.includes('camel') || url.includes('americanspirit')) {
        await elementActions.click(storelocatorObject.sensafilterbyproductCamel);
      } else {
        await elementActions.click(storelocatorObject.sensafilterbyproduct);
      }


      const products = await storelocatorObject.products;
      for (let i = 1; i <= 4; i++) {

        const product = products[i];

        await elementActions.waitForDisplayed(product);
        await elementActions.clickusingJavascript(product);

        await elementActions.assertion(storelocatorObject.btnApplyFilter);
        await elementActions.clickusingJavascript(storelocatorObject.btnApplyFilter);
        await browser.waitUntil(async () => true, { timeout: 5000 });
        const clearfilter = await storelocatorObject.clearFilter;
        const isClickable = await clearfilter.isClickable();
        console.log('Clear Filter - isClickable:', isClickable);
        if (isClickable) {
          await elementActions.clickusingJavascript(storelocatorObject.clearFilter);
          await elementActions.assertion(storelocatorObject.btnfilterproduct);
          await elementActions.click(storelocatorObject.btnfilterproduct);
        } else {
          await elementActions.waitForDisplayed(storelocatorObject.labelProduct);
          await elementActions.assertion(storelocatorObject.labelProduct);
          await elementActions.assertion(storelocatorObject.firstStoreNumber_Stores);
          await elementActions.click(storelocatorObject.firstStoreNumber_Stores);
          await elementActions.assertion(storelocatorObject.firstStoreHeader_Stores);

          await elementActions.assertion(storelocatorObject.btnfilterproduct);
          await elementActions.click(storelocatorObject.btnfilterproduct);
        }
      }
      await elementActions.assertion(storelocatorObject.closebutton);
      await elementActions.click(storelocatorObject.closebutton);
      console.log(' Successfully validated products from filter by product');
    } catch (error) {
      logger.error(' Failed to validate products from filter by product:', { error });
      throw error;
    }
  }
}

export default new Storlocator();