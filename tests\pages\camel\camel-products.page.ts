import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import path from 'path';
import sensaAccountPage from '../commonteps/account.page.ts';
import camelHomepagePageObject from '../../page-object/camel/camel-homepage.pageObject.ts';
import camelProductsPageObject from '../../page-object/camel/camel-products.pageObject.ts';
import camelHomePage from './camel-home.page.ts';




class Products {


    async productsPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdProducts = testData.getCellValue(SHEET_NAME, scenarioname, 'hdProducts');
            const hdrproductsdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrproductsdesc');
            const lblcrush = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcrush');
            const lblclassic = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclassic');
            const lblturkish = testData.getCellValue(SHEET_NAME, scenarioname, 'lblturkish');
            const lblno9 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblno9');
            const lblredkamel = testData.getCellValue(SHEET_NAME, scenarioname, 'lblredkamel');
            const lblviewall = testData.getCellValue(SHEET_NAME, scenarioname, 'lblviewall');
            await sensaAccountPage.mssgcomparision(camelHomepagePageObject.lblproductpageheader_camel, hdProducts);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.hdrproductsdesc_camel, hdrproductsdesc);
            await elementActions.assertion(camelProductsPageObject.btnfindmyblend_camel);
            await elementActions.assertion(camelProductsPageObject.imgcrush_camel);
            await elementActions.assertion(camelProductsPageObject.imgclassic_camel);
            await elementActions.assertion(camelProductsPageObject.imgturkish_camel);
            await elementActions.assertion(camelProductsPageObject.imgno9_camel);
            await elementActions.assertion(camelProductsPageObject.imgredkamel_camel);
            await elementActions.assertion(camelProductsPageObject.imgviewall_camel);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblcrush_camel, lblcrush);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblclassic_camel, lblclassic);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblturkish_camel, lblturkish);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblno9_camel, lblno9);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblredkamel_camel, lblredkamel);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblviewall_camel, lblviewall);
            console.log('Validated the Content in Products page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Products  page', { error });
            throw error;
        }
    }


    async crushProductsPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdrcrushdesc1 = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrcrushdesc1');
            const hdrcrushdesc2 = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrcrushdesc2');
            const lblcrushnonmentholproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcrushnonmentholproduct');
            const lblcrushmentholproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcrushmentholproduct');
            const lblcrushsilverproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcrushsilverproduct');
            const lblcrushrichproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcrushrichproduct');
            const lblcrushsmoothproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcrushsmoothproduct');
            const lblcrushsmoothsilverproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcrushsmoothsilverproduct');
            await elementActions.assertion(camelProductsPageObject.hdrcrush_camel);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.hdrcrushdesc1_camel, hdrcrushdesc1);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.hdrcrushdesc2_camel, hdrcrushdesc2);
            await elementActions.assertion(camelProductsPageObject.imgcrushmentholproduct_camel);
            await elementActions.assertion(camelProductsPageObject.imgcrushnonmentholproduct);
            await elementActions.assertion(camelProductsPageObject.imgcrushsilverproduct_camel);
            await elementActions.assertion(camelProductsPageObject.imgcrushrichproduct_camel);
            await elementActions.assertion(camelProductsPageObject.imgcrushsmoothproduct_camel);
            await elementActions.assertion(camelProductsPageObject.imgcrushsmoothsilverproduct_camel);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblcrushnonmentholproduct_camel, lblcrushnonmentholproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblcrushmentholproduct_camel, lblcrushmentholproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblcrushsilverproduct_camel, lblcrushsilverproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblcrushrichproduct_camel, lblcrushrichproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblcrushsmoothproduct_camel, lblcrushsmoothproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblcrushsmoothsilverproduct_camel, lblcrushsmoothsilverproduct);

            const lbltakesyouto = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltakesyouto');
            const hdrcrushmentholdesc2 = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrcrushmentholdesc2');
            const hdrsmoothesc2 = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrsmoothesc2');
            const hdrsmoothSilveresc2 = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrsmoothSilveresc2');
            const lblrelatedproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblrelatedproduct');
            await this.clickoneachproductandvalidate(camelProductsPageObject.lblcrushnonmentholproduct_camel, camelProductsPageObject.imgcrushnonmentholproduct, lbltakesyouto, lblrelatedproduct);
            await this.clickoneachproductandvalidate(camelProductsPageObject.lblcrushmentholproduct_camel, camelProductsPageObject.imgcrushmentholproduct_camel, hdrcrushmentholdesc2, lblrelatedproduct);
            await this.clickoneachproductandvalidate(camelProductsPageObject.lblcrushsilverproduct_camel, camelProductsPageObject.imgcrushsilverproduct_camel, hdrcrushmentholdesc2, lblrelatedproduct);
            await this.clickoneachproductandvalidate(camelProductsPageObject.lblcrushrichproduct_camel, camelProductsPageObject.imgcrushrichproduct_camel, lbltakesyouto, lblrelatedproduct);
            await this.clickoneachproductandvalidate(camelProductsPageObject.lblcrushsmoothproduct_camel, camelProductsPageObject.imgcrushsmoothproduct_camel, hdrsmoothesc2, lblrelatedproduct);
            await this.clickoneachproductandvalidate(camelProductsPageObject.lblcrushsmoothsilverproduct_camel, camelProductsPageObject.imgcrushsmoothsilverproduct_camel, hdrsmoothSilveresc2, lblrelatedproduct);
            console.log('Validated the Content in Crush product page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Products  page', { error });
            throw error;
        }
    }

    async clickoneachproductandvalidate(element: ChainablePromiseElement, element2: ChainablePromiseElement, text1: string, text2: string) {
        await elementActions.waitForDisplayed(element);
        const title = await element.getText();
        await elementActions.waitForDisplayed(element2);
        await elementActions.clickusingJavascript(element2);
        if (title.trim() === 'CLASSIC GOLD') {
            await elementActions.assertion(camelProductsPageObject.lblcrushtitle_camel);
        } else if (title.trim() === 'CLASSIC WIDES BLUE' || title.trim() === 'CLASSIC WIDES FILTERS') {
            const elementtitle = title.replace(/CLASSIC/i, '').trim();
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblcrushtitle_camel, elementtitle);
        } else if (title.trim() === 'CLASSIC WIDES MENTHOL' || title.trim() === 'CLASSIC WIDES MENTHOL SILVER') {
            const elementtitle = title.replace(/CLASSIC/i, '').trim();
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblcrushtitle_camel, elementtitle);
        } else {
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblcrushtitle_camel, title);
        }
        await elementActions.assertion(camelProductsPageObject.lblproductimg_camel);
        await sensaAccountPage.mssgcomparision(camelProductsPageObject.lbltakesyouto_camel, text1);
        await elementActions.assertion(camelProductsPageObject.imgproductdesc_camel);
        await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblrelatedproduct_camel, text2);
        const relatedproducts = camelProductsPageObject.imgrelatedproduct1_camel;
        for (const products of relatedproducts) {
            if (await products.isDisplayed()) {
                await elementActions.assertion(products);
            }
        }
        const url = await browser.getUrl();
        if (url.includes('turkish') || url.includes('no9') || url.includes('red-kamel')) {
            await elementActions.clickusingJavascript(camelProductsPageObject.lnkbacktoallturkish_camel);
        } else {
            await elementActions.clickusingJavascript(camelProductsPageObject.lnkbacktoall_camel);
        }
    }


    async navigatetocrushpage() {
        try {
            await camelHomePage.navigatetoProductspage();
            await elementActions.clickusingJavascript(camelProductsPageObject.imgcrush_camel);
            await elementActions.assertion(camelProductsPageObject.hdrcrush_camel);
            console.log('Navigated to Crush Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Crush Page', { error });
            throw error;
        }
    }
    async navigatetoclassicpage() {
        try {
            await camelHomePage.navigatetoProductspage();
            await elementActions.clickusingJavascript(camelProductsPageObject.imgclassic_camel);
            await elementActions.assertion(camelProductsPageObject.hdrcrush_camel);
            console.log('Navigated to Classic Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Classic Page', { error });
            throw error;
        }
    }

    async navigatetoturkishpage() {
        try {
            await camelHomePage.navigatetoProductspage();
            await elementActions.clickusingJavascript(camelProductsPageObject.imgturkish_camel);
            await elementActions.assertion(camelProductsPageObject.hdrcrush_camel);
            console.log('Navigated to Turkish Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Turkish Page', { error });
            throw error;
        }
    }
    async navigatetono9page() {
        try {
            await camelHomePage.navigatetoProductspage();
            await elementActions.clickusingJavascript(camelProductsPageObject.imgno9_camel);
            await elementActions.assertion(camelProductsPageObject.hdrcrush_camel);
            console.log('Navigated to Turkish Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Turkish Page', { error });
            throw error;
        }
    }

    async navigatetoredkamelpage() {
        try {
            await camelHomePage.navigatetoProductspage();
            await elementActions.clickusingJavascript(camelProductsPageObject.imgredkamel_camel);
            await elementActions.assertion(camelProductsPageObject.hdrcrush_camel);
            console.log('Navigated to Red Kamel Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Red Kamel Page', { error });
            throw error;
        }
    }


    async classicProductsPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdclassicdesc2 = testData.getCellValue(SHEET_NAME, scenarioname, 'hdclassicdesc2');
            const hdclassicdesc1 = testData.getCellValue(SHEET_NAME, scenarioname, 'hdclassicdesc1');
            const lblclassicgoldproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclassicgoldproduct');
            const lblclassicblueproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclassicblueproduct');
            const lblclassicfiltersproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclassicfiltersproduct');
            const lblclassicplatinumproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclassicplatinumproduct');
            const lblclassicmentholproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclassicmentholproduct');
            const lblclassicwidesblueproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclassicwidesblueproduct');
            const lblclassicwidesfilterproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclassicwidesfilterproduct');
            const lblclassicwidesmentholproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclassicwidesmentholproduct');
            const lblclassicwidesmentholsilverproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclassicwidesmentholsilverproduct');
            await elementActions.assertion(camelProductsPageObject.hdrcrush_camel);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.hdrcrushdesc1_camel, hdclassicdesc1);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.hdrcrushdesc2_camel, hdclassicdesc2);
            await elementActions.assertion(camelProductsPageObject.imgclassicgoldproduct);
            await elementActions.assertion(camelProductsPageObject.imgclassicblueproduct);
            await elementActions.assertion(camelProductsPageObject.imgclassicfiltersproduct);
            await elementActions.assertion(camelProductsPageObject.imgclassicplatinumproduct);
            await elementActions.assertion(camelProductsPageObject.imgclassicmentholproduct);
            await elementActions.assertion(camelProductsPageObject.imgclassicwidesblueproduct);
            await elementActions.assertion(camelProductsPageObject.imgclassicwidesfilterproduct);
            await elementActions.assertion(camelProductsPageObject.imgclassicwidesmentholproduct);
            await elementActions.assertion(camelProductsPageObject.imgclassicwidesmentholsilverproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblclassicgoldproduct_camel, lblclassicgoldproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblclassicblueproduct_camel, lblclassicblueproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblclassicfiltersproduct_camel, lblclassicfiltersproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblclassicplatinumproduct_camel, lblclassicplatinumproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblclassicmentholproduct_camel, lblclassicmentholproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblclassicwidesblueproduct_camel, lblclassicwidesblueproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblclassicwidesfilterproduct_camel, lblclassicwidesfilterproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblclassicwidesmentholproduct_camel, lblclassicwidesmentholproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblclassicwidesmentholsilverproduct_camel, lblclassicwidesmentholsilverproduct);

            const lblclassicgoldproductdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclassicgoldproductdesc');
            const lblclassicblueproductdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclassicblueproductdesc');
            const lblclassicfiltersproductdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclassicfiltersproductdesc');
            const lblclassicplatinumproductdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclassicplatinumproductdesc');
            const lblclassicmentholproductdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclassicmentholproductdesc');
            const lblclassicwidesblueproductdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclassicwidesblueproductdesc');
            const lblclassicwidesfilterproductdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclassicwidesfilterproductdesc');
            const lblclassicwidesmentholproductdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclassicwidesmentholproductdesc');
            const lblclassicwidesmentholsilverproductdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclassicwidesmentholsilverproductdesc');
            const lblrelatedproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblrelatedproduct');

            await this.clickoneachproductandvalidate(camelProductsPageObject.lblclassicgoldproduct_camel, camelProductsPageObject.imgclassicgoldproduct, lblclassicgoldproductdesc, lblrelatedproduct);
            await this.clickoneachproductandvalidate(camelProductsPageObject.lblclassicblueproduct_camel, camelProductsPageObject.imgclassicblueproduct, lblclassicblueproductdesc, lblrelatedproduct);
            await this.clickoneachproductandvalidate(camelProductsPageObject.lblclassicfiltersproduct_camel, camelProductsPageObject.imgclassicfiltersproduct, lblclassicfiltersproductdesc, lblrelatedproduct);
            await this.clickoneachproductandvalidate(camelProductsPageObject.lblclassicplatinumproduct_camel, camelProductsPageObject.imgclassicplatinumproduct, lblclassicplatinumproductdesc, lblrelatedproduct);
            await this.clickoneachproductandvalidate(camelProductsPageObject.lblclassicmentholproduct_camel, camelProductsPageObject.imgclassicmentholproduct, lblclassicmentholproductdesc, lblrelatedproduct);
            await this.clickoneachproductandvalidate(camelProductsPageObject.lblclassicwidesblueproduct_camel, camelProductsPageObject.imgclassicwidesblueproduct, lblclassicwidesblueproductdesc, lblrelatedproduct);
            await this.clickoneachproductandvalidate(camelProductsPageObject.lblclassicwidesfilterproduct_camel, camelProductsPageObject.imgclassicwidesfilterproduct, lblclassicwidesfilterproductdesc, lblrelatedproduct);
            await this.clickoneachproductandvalidate(camelProductsPageObject.lblclassicwidesmentholproduct_camel, camelProductsPageObject.imgclassicwidesmentholproduct, lblclassicwidesmentholproductdesc, lblrelatedproduct);
            await this.clickoneachproductandvalidate(camelProductsPageObject.lblclassicwidesmentholsilverproduct_camel, camelProductsPageObject.imgclassicwidesmentholsilverproduct, lblclassicwidesmentholsilverproductdesc, lblrelatedproduct);

            console.log('Validated the Content in Crush product page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Products  page', { error });
            throw error;
        }
    }


    async turkishProductsPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdturkishdesc1 = testData.getCellValue(SHEET_NAME, scenarioname, 'hdturkishdesc1');
            const hdturkishdesc2 = testData.getCellValue(SHEET_NAME, scenarioname, 'hdturkishdesc2');
            const lblturkishloyalproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblturkishloyalproduct');
            const lblturkishgoldproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblturkishgoldproduct');
            const lblturkishsilverproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblturkishsilverproduct');
            const lblturkishjadeproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblturkishjadeproduct');
            const lblturkishjadesilverproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblturkishjadesilverproduct');
            await elementActions.assertion(camelProductsPageObject.hdrcrush_camel);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.hdrcrushdesc1_camel, hdturkishdesc1);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.hdrcrushdesc2_camel, hdturkishdesc2);
            await elementActions.assertion(camelProductsPageObject.imgturkishloyalproduct);
            await elementActions.assertion(camelProductsPageObject.imgturkishgoldproduct);
            await elementActions.assertion(camelProductsPageObject.imgturkishsilverproduct);
            await elementActions.assertion(camelProductsPageObject.imgturkishjadeproduct);
            await elementActions.assertion(camelProductsPageObject.imgturkishjadesilverproduct);

            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblturkishloyalproduct_camel, lblturkishloyalproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblturkishgoldproduct_camel, lblturkishgoldproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblturkishsilverproduct_camel, lblturkishsilverproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblturkishjadeproduct_camel, lblturkishjadeproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblturkishjadesilverproduct_camel, lblturkishjadesilverproduct);

            const lblturkishloyalproductdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblturkishloyalproductdesc');
            const lblturkishgoldproductdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblturkishgoldproductdesc');
            const lblturkishsilverproductdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblturkishsilverproductdesc');
            const lblturkishjadeproductdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblturkishjadeproductdesc');
            const lblturkishjadesilverproductdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblturkishjadesilverproductdesc');
            const lblrelatedproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblrelatedproduct');

            await this.clickoneachproductandvalidate(camelProductsPageObject.lblturkishloyalproduct_camel, camelProductsPageObject.imgturkishloyalproduct, lblturkishloyalproductdesc, lblrelatedproduct);
            await this.clickoneachproductandvalidate(camelProductsPageObject.lblturkishgoldproduct_camel, camelProductsPageObject.imgturkishgoldproduct, lblturkishgoldproductdesc, lblrelatedproduct);
            await this.clickoneachproductandvalidate(camelProductsPageObject.lblturkishsilverproduct_camel, camelProductsPageObject.imgturkishsilverproduct, lblturkishsilverproductdesc, lblrelatedproduct);
            await this.clickoneachproductandvalidate(camelProductsPageObject.lblturkishjadeproduct_camel, camelProductsPageObject.imgturkishjadeproduct, lblturkishjadeproductdesc, lblrelatedproduct);
            await this.clickoneachproductandvalidate(camelProductsPageObject.lblturkishjadesilverproduct_camel, camelProductsPageObject.imgturkishjadesilverproduct, lblturkishjadesilverproductdesc, lblrelatedproduct);

            console.log('Validated the Content in Turkish product page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Turkish Products  page', { error });
            throw error;
        }
    }


    async redkamelProductsPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdredkameldesc1 = testData.getCellValue(SHEET_NAME, scenarioname, 'hdredkameldesc1');
            const hdredkameldesc2 = testData.getCellValue(SHEET_NAME, scenarioname, 'hdredkameldesc2');
            const lblredkamelproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblredkamelproduct');
            const lblredkamelsmoothproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblredkamelsmoothproduct');
            await elementActions.assertion(camelProductsPageObject.hdrcrush_camel);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.hdrcrushdesc1_camel, hdredkameldesc1);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.hdrcrushdesc2_camel, hdredkameldesc2);
            await elementActions.assertion(camelProductsPageObject.imgredkamelproduct);
            await elementActions.assertion(camelProductsPageObject.imgtredkamelsmoothproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblredkamelproduct_camel, lblredkamelproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblredkamelsmoothproduct_camel, lblredkamelsmoothproduct);

            const lblredkamelproductdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblredkamelproductdesc');
            const lblredkamelsmoothproductdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblredkamelsmoothproductdesc');
            const lblrelatedproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblrelatedproduct');

            await this.clickoneachproductandvalidate(camelProductsPageObject.lblredkamelproduct_camel, camelProductsPageObject.imgredkamelproduct, lblredkamelproductdesc, lblrelatedproduct);
            await this.clickoneachproductandvalidate(camelProductsPageObject.lblredkamelsmoothproduct_camel, camelProductsPageObject.imgtredkamelsmoothproduct, lblredkamelsmoothproductdesc, lblrelatedproduct);

            console.log('Validated the Content in Red Kamel product page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Red Kamel Products  page', { error });
            throw error;
        }
    }

    async no9ProductsPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);

            const hdno9desc1 = testData.getCellValue(SHEET_NAME, scenarioname, 'hdno9desc1');
            const hdno9desc2 = testData.getCellValue(SHEET_NAME, scenarioname, 'hdno9desc2');
            const lblno9product = testData.getCellValue(SHEET_NAME, scenarioname, 'lblno9product');
            const lbltno9mentheproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltno9mentheproduct');
            await elementActions.assertion(camelProductsPageObject.hdrcrush_camel);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.hdrcrushdesc1_camel, hdno9desc1);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.hdrcrushdesc2_camel, hdno9desc2);
            await elementActions.assertion(camelProductsPageObject.imgno9product);
            await elementActions.assertion(camelProductsPageObject.imgtno9mentheproduct);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lblno9product_camel, lblno9product);
            await sensaAccountPage.mssgcomparision(camelProductsPageObject.lbltno9mentheproduct_camel, lbltno9mentheproduct);

            const lblno9productdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblno9productdesc');
            const lbltno9mentheproductdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltno9mentheproductdesc');
            const lblrelatedproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblrelatedproduct');

            await this.clickoneachproductandvalidate(camelProductsPageObject.lblno9product_camel, camelProductsPageObject.imgno9product, lblno9productdesc, lblrelatedproduct);
            await this.clickoneachproductandvalidate(camelProductsPageObject.lbltno9mentheproduct_camel, camelProductsPageObject.imgtno9mentheproduct, lbltno9mentheproductdesc, lblrelatedproduct);

            console.log('Validated the Content in No9 product page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the No9 Products  page', { error });
            throw error;
        }
    }





}
export default new Products();