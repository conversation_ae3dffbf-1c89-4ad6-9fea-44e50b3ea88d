import { Then } from '@wdio/cucumber-framework';
import camelLoginPage from '../../pages/camel/camel-login.page.ts';
import logger from '../../support/utils/logger.util.ts';
import sensaRegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';
import camelLoginPageObject from '../../page-object/camel/camel-login.pageObject.ts';
import Registration from '../../pages/commonteps/registration.page.ts';

Then(/^The user Validates camel Signin Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await camelLoginPage.camelloginPageValidation(filepath, sheetname, scenarioname);
    await expect(sensaRegistrationPageObject.lblsignIn_sensa).toBeDisplayed();
    logger.info('User Validates Camel Signin Page');
});

Then(/^The user should be able to login to the camel application successfully$/, async function () {
    await camelLoginPage.camelloginPage();
    await expect(camelLoginPageObject.lbllogo_camel).toBeDisplayed();
    logger.info('User logsin Successfully');

});

Then(/^The user validates that successfully logged out of camel brand$/, async function () {
    await camelLoginPage.camellogoutsucessfully();
    await expect(sensaRegistrationPageObject.lblsignIn_sensa).toBeDisplayed();
    logger.info('User is on Signin Page');
});

Then(/^The user should be able to login to the nas application successfully$/, async function () {
    await camelLoginPage.nasloginPage();
    await expect(camelLoginPageObject.lbllogo_nas).toBeDisplayed();
    logger.info('User logsin Successfully');

});

Then(/^The user validates that successfully logged out of nas brand$/, async function () {
    await Registration.successfullLogoutfromSensasite();
    await expect(sensaRegistrationPageObject.lblsignIn_sensa).toBeDisplayed();
    logger.info('User is on Signin Page');
});





