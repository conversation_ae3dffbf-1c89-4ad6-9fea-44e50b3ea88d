import { When, Then } from '@wdio/cucumber-framework';
import sensaMydevicePage from '../../pages/sensa/sensa-mydevice.page.ts';
import sensaMydevicePageObject from '../../page-object/sensa/sensa-mydevice.pageObject.ts';
import logger from '../../support/utils/logger.util.ts';

When(/^The user clicks on My device link$/, async function () {
    await sensaMydevicePage.clickonmyDeviceLink();
    await expect(sensaMydevicePageObject.lblmydevicetitle_sensa).toBeDisplayed();
    logger.info('Navigated my device Page Successfully');

});
Then(/^The user clicks on links$/, async function () {
    await sensaMydevicePage.clickoneachlinkandvalidatethevideo();
    logger.info('Validated All links Successfully');
});

Then(/^The user Validates My Device Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaMydevicePage.myDevicepagevalidation(filepath, sheetname, scenarioname);
    logger.info('Validated my device Page Successfully');
});