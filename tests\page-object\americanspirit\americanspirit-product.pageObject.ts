class NasProductPageObject {

    get Nas_ourProductsTitle() { return $('[title="Our Products"]'); }
    get Nas_productHeroImage() { return $('[src="/content/dam/nas/website/product2025/2025-products-hero-d.png"]'); }
    get Nas_filtersHeading() { return $('[class="cmp-find-my-pack__filters-heading"]'); }
    get Nas_myTaste() { return $('//*[text()="MY TASTE"]'); }
    get Nas_organic() { return $('//*[text()="Organic"]'); }
    get Nas_menthol() { return $('//*[text()="Menthol"]'); }
    get Nas_usGrown() { return $('//*[text()="U.S. Grown"]'); }
    get Nas_nonFiltered() { return $('//*[text()="Non-Filtered"]'); }
    get Nas_rollYourOwn() { return $('//*[text()="Roll-Your-Own"]'); }
    get Nas_mystyle() { return $('//*[text()="MY STYLE"]'); }
    get Nas_smoothMellow() { return $('//*[text()="Smooth, Mellow"]'); }
    get Nas_balanced() { return $('//*[text()="Balanced"]'); }
    get Nas_fullBodied() { return $('//*[text()="Full-Bodied"]'); }
    get Nas_richRobust() { return $('//*[text()="Rich, Robust"]'); }


    //Product Fronside Locator

    get Nas_Yellowsignaturestyle() { return $$('[class="cmp-find-my-pack__products-front-side"]')[0]; }
    get Nas_Bluesignaturestyle() { return $$('[class="cmp-find-my-pack__products-front-side"]')[1]; }
    get Nas_Turquoiseorganicstyle() { return $$('[class="cmp-find-my-pack__products-front-side"]')[2]; }
    get Nas_Goldorganicstyle() { return $$('[class="cmp-find-my-pack__products-front-side"]')[3]; }
    get Nas_Greenorganicstyle() { return $$('[class="cmp-find-my-pack__products-front-side"]')[4]; }
    get Nas_Darkgreenorganicstyle() { return $$('[class="cmp-find-my-pack__products-front-side"]')[5]; }
    get Nas_Skyorganicstyle() { return $$('[class="cmp-find-my-pack__products-front-side"]')[6]; }
    get Nas_Orangesignaturestyle() { return $$('[class="cmp-find-my-pack__products-front-side"]')[7]; }
    get Nas_Blackperiquestyle() { return $$('[class="cmp-find-my-pack__products-front-side"]')[8]; }
    get Nas_Greyperiquestyle() { return $$('[class="cmp-find-my-pack__products-front-side"]')[9]; }
    get Nas_Darkbluesignaturestyle() { return $$('[class="cmp-find-my-pack__products-front-side"]')[10]; }
    get Nas_Celadonsignaturestyle() { return $$('[class="cmp-find-my-pack__products-front-side"]')[11]; }
    get Nas_Huntersignaturestyle() { return $$('[class="cmp-find-my-pack__products-front-side"]')[12]; }
    get Nas_Brownsignaturestyle() { return $$('[class="cmp-find-my-pack__products-front-side"]')[13]; }
    get Nas_Tansignaturestyle() { return $$('[class="cmp-find-my-pack__products-front-side"]')[14]; }
    get Nas_Bluerollyourown() { return $$('[class="cmp-find-my-pack__products-front-side"]')[15]; }
    get Nas_Turquoiserollyourown() { return $$('[class="cmp-find-my-pack__products-front-side"]')[16]; }
    get Nas_Darkblueollyourown() { return $$('[class="cmp-find-my-pack__products-front-side"]')[17]; }
    get Nas_Blackrollyourown() { return $$('[class="cmp-find-my-pack__products-front-side"]')[18]; }

    //Product BacksideLocator
    get Nas_productcardBackside1() { return $$('[class="cmp-find-my-pack__products-back-side"]')[0]; }
    get Nas_productcardBackside2() { return $$('[class="cmp-find-my-pack__products-back-side"]')[1]; }
    get Nas_productcardBackside3() { return $$('[class="cmp-find-my-pack__products-back-side"]')[2]; }
    get Nas_productcardBackside4() { return $$('[class="cmp-find-my-pack__products-back-side"]')[3]; }
    get Nas_productcardBackside5() { return $$('[class="cmp-find-my-pack__products-back-side"]')[4]; }
    get Nas_productcardBackside6() { return $$('[class="cmp-find-my-pack__products-back-side"]')[5]; }
    get Nas_productcardBackside7() { return $$('[class="cmp-find-my-pack__products-back-side"]')[6]; }
    get Nas_productcardBackside8() { return $$('[class="cmp-find-my-pack__products-back-side"]')[7]; }
    get Nas_productcardBackside9() { return $$('[class="cmp-find-my-pack__products-back-side"]')[8]; }
    get Nas_productcardBackside10() { return $$('[class="cmp-find-my-pack__products-back-side"]')[9]; }
    get Nas_productcardBackside11() { return $$('[class="cmp-find-my-pack__products-back-side"]')[10]; }
    get Nas_productcardBackside12() { return $$('[class="cmp-find-my-pack__products-back-side"]')[11]; }
    get Nas_productcardBackside13() { return $$('[class="cmp-find-my-pack__products-back-side"]')[12]; }
    get Nas_productcardBackside14() { return $$('[class="cmp-find-my-pack__products-back-side"]')[13]; }
    get Nas_productcardBackside15() { return $$('[class="cmp-find-my-pack__products-back-side"]')[14]; }
    get Nas_productcardBackside16() { return $$('[class="cmp-find-my-pack__products-back-side"]')[15]; }
    get Nas_productcardBackside17() { return $$('[class="cmp-find-my-pack__products-back-side"]')[16]; }
    get Nas_productcardBackside18() { return $$('[class="cmp-find-my-pack__products-back-side"]')[17]; }
    get Nas_productcardBackside19() { return $$('[class="cmp-find-my-pack__products-back-side"]')[18]; }

    //Product name Locator
    get Nas_productname1() { return $$('[class="cmp-find-my-pack__products-name"]')[0]; }
    get Nas_productname2() { return $$('[class="cmp-find-my-pack__products-name"]')[2]; }
    get Nas_productname3() { return $$('[class="cmp-find-my-pack__products-name"]')[4]; }
    get Nas_productname4() { return $$('[class="cmp-find-my-pack__products-name"]')[6]; }
    get Nas_productname5() { return $$('[class="cmp-find-my-pack__products-name"]')[8]; }
    get Nas_productname6() { return $$('[class="cmp-find-my-pack__products-name"]')[10]; }
    get Nas_productname7() { return $$('[class="cmp-find-my-pack__products-name"]')[12]; }
    get Nas_productname8() { return $$('[class="cmp-find-my-pack__products-name"]')[14]; }
    get Nas_productname9() { return $$('[class="cmp-find-my-pack__products-name"]')[16]; }
    get Nas_productname10() { return $$('[class="cmp-find-my-pack__products-name"]')[18]; }
    get Nas_productname11() { return $$('[class="cmp-find-my-pack__products-name"]')[20]; }
    get Nas_productname12() { return $$('[class="cmp-find-my-pack__products-name"]')[22]; }
    get Nas_productname13() { return $$('[class="cmp-find-my-pack__products-name"]')[24]; }
    get Nas_productname14() { return $$('[class="cmp-find-my-pack__products-name"]')[26]; }
    get Nas_productname15() { return $$('[class="cmp-find-my-pack__products-name"]')[28]; }
    get Nas_productname16() { return $$('[class="cmp-find-my-pack__products-name"]')[30]; }
    get Nas_productname17() { return $$('[class="cmp-find-my-pack__products-name"]')[32]; }
    get Nas_productname18() { return $$('[class="cmp-find-my-pack__products-name"]')[34]; }
    get Nas_productname19() { return $$('[class="cmp-find-my-pack__products-name"]')[36]; }

    get Nas_productfloatinglabel1() { return $$('[class="cmp-find-my-pack__products-floating-label"]')[0]; }
    get Nas_productfloatinglabel2() { return $$('[class="cmp-find-my-pack__products-floating-label"]')[2]; }
    get Nas_productfloatinglabel3() { return $$('[class="cmp-find-my-pack__products-floating-label"]')[4]; }
    get Nas_productfloatinglabel4() { return $$('[class="cmp-find-my-pack__products-floating-label"]')[6]; }
    get Nas_productfloatinglabel5() { return $$('[class="cmp-find-my-pack__products-floating-label"]')[8]; }
    get Nas_productfloatinglabel6() { return $$('[class="cmp-find-my-pack__products-floating-label"]')[10]; }
    get Nas_productfloatinglabel7() { return $$('[class="cmp-find-my-pack__products-floating-label"]')[12]; }
    get Nas_productfloatinglabel8() { return $$('[class="cmp-find-my-pack__products-floating-label"]')[14]; }
    get Nas_productfloatinglabel9() { return $$('[class="cmp-find-my-pack__products-floating-label"]')[16]; }
    get Nas_productfloatinglabel10() { return $$('[class="cmp-find-my-pack__products-floating-label"]')[18]; }
    get Nas_productfloatinglabel11() { return $$('[class="cmp-find-my-pack__products-floating-label"]')[20]; }
    get Nas_productfloatinglabel12() { return $$('[class="cmp-find-my-pack__products-floating-label"]')[22]; }
    get Nas_productfloatinglabel13() { return $$('[class="cmp-find-my-pack__products-floating-label"]')[24]; }
    get Nas_productfloatinglabel14() { return $$('[class="cmp-find-my-pack__products-floating-label"]')[26]; }
    get Nas_productfloatinglabel15() { return $$('[class="cmp-find-my-pack__products-floating-label"]')[28]; }
    get Nas_productfloatinglabel16() { return $$('[class="cmp-find-my-pack__products-floating-label"]')[30]; }
    get Nas_productfloatinglabel17() { return $$('[class="cmp-find-my-pack__products-floating-label"]')[32]; }
    get Nas_productfloatinglabel18() { return $$('[class="cmp-find-my-pack__products-floating-label"]')[34]; }
    get Nas_productfloatinglabel19() { return $$('[class="cmp-find-my-pack__products-floating-label"]')[36]; }

    get Nas_productTasteDescription1() { return $$('[class="cmp-find-my-pack__products-tastedescription"]')[0]; }
    get Nas_productTasteDescription2() { return $$('[class="cmp-find-my-pack__products-tastedescription"]')[1]; }
    get Nas_productTasteDescription3() { return $$('[class="cmp-find-my-pack__products-tastedescription"]')[2]; }
    get Nas_productTasteDescription4() { return $$('[class="cmp-find-my-pack__products-tastedescription"]')[3]; }
    get Nas_productTasteDescription5() { return $$('[class="cmp-find-my-pack__products-tastedescription"]')[4]; }
    get Nas_productTasteDescription6() { return $$('[class="cmp-find-my-pack__products-tastedescription"]')[5]; }
    get Nas_productTasteDescription7() { return $$('[class="cmp-find-my-pack__products-tastedescription"]')[6]; }
    get Nas_productTasteDescription8() { return $$('[class="cmp-find-my-pack__products-tastedescription"]')[7]; }
    get Nas_productTasteDescription9() { return $$('[class="cmp-find-my-pack__products-tastedescription"]')[8]; }
    get Nas_productTasteDescription10() { return $$('[class="cmp-find-my-pack__products-tastedescription"]')[9]; }
    get Nas_productTasteDescription11() { return $$('[class="cmp-find-my-pack__products-tastedescription"]')[10]; }
    get Nas_productTasteDescription12() { return $$('[class="cmp-find-my-pack__products-tastedescription"]')[11]; }
    get Nas_productTasteDescription13() { return $$('[class="cmp-find-my-pack__products-tastedescription"]')[12]; }
    get Nas_productTasteDescription14() { return $$('[class="cmp-find-my-pack__products-tastedescription"]')[13]; }
    get Nas_productTasteDescription15() { return $$('[class="cmp-find-my-pack__products-tastedescription"]')[14]; }
    get Nas_productTasteDescription16() { return $$('[class="cmp-find-my-pack__products-tastedescription"]')[15]; }
    get Nas_productTasteDescription17() { return $$('[class="cmp-find-my-pack__products-tastedescription"]')[16]; }
    get Nas_productTasteDescription18() { return $$('[class="cmp-find-my-pack__products-tastedescription"]')[17]; }
    get Nas_productTasteDescription19() { return $$('[class="cmp-find-my-pack__products-tastedescription"]')[18]; }

    get Nas_productTitle1() { return $$('[class="cmp-find-my-pack__products-title"]')[0]; }
    get Nasproductssubtitle() { return $('[class="cmp-find-my-pack__products-subtitle"]'); }
    get Nas_productTitle2() { return $$('[class="cmp-find-my-pack__products-title"]')[1]; }
    get Nas_productTitle3() { return $$('[class="cmp-find-my-pack__products-title"]')[2]; }
    get Nas_productTitle4() { return $$('[class="cmp-find-my-pack__products-title"]')[3]; }
    get Nas_productTitle5() { return $$('[class="cmp-find-my-pack__products-title"]')[4]; }
    get Nas_productTitle6() { return $$('[class="cmp-find-my-pack__products-title"]')[5]; }
    get Nas_productTitle7() { return $$('[class="cmp-find-my-pack__products-title"]')[6]; }
    get Nas_productTitle8() { return $$('[class="cmp-find-my-pack__products-title"]')[7]; }
    get Nas_productTitle9() { return $$('[class="cmp-find-my-pack__products-title"]')[8]; }
    get Nas_productTitle10() { return $$('[class="cmp-find-my-pack__products-title"]')[9]; }
    get Nas_productTitle11() { return $$('[class="cmp-find-my-pack__products-title"]')[10]; }
    get Nas_productTitle12() { return $$('[class="cmp-find-my-pack__products-title"]')[11]; }
    get Nas_productTitle13() { return $$('[class="cmp-find-my-pack__products-title"]')[12]; }
    get Nas_productTitle14() { return $$('[class="cmp-find-my-pack__products-title"]')[13]; }
    get Nas_productTitle15() { return $$('[class="cmp-find-my-pack__products-title"]')[14]; }
    get Nas_productTitle16() { return $$('[class="cmp-find-my-pack__products-title"]')[15]; }
    get Nas_productTitle17() { return $$('[class="cmp-find-my-pack__products-title"]')[16]; }
    get Nas_productTitle18() { return $$('[class="cmp-find-my-pack__products-title"]')[17]; }
    get Nas_productTitle19() { return $$('[class="cmp-find-my-pack__products-title"]')[18]; }
    get Nas_productTitle20() { return $$('[class="cmp-find-my-pack__products-title"]')[19]; }

    get Nas_productDescription1() { return $$('[class="cmp-find-my-pack__products-description"]')[0]; }
    get Nas_productDescription2() { return $$('[class="cmp-find-my-pack__products-description"]')[1]; }
    get Nas_productDescription3() { return $$('[class="cmp-find-my-pack__products-description"]')[2]; }
    get Nas_productDescription4() { return $$('[class="cmp-find-my-pack__products-description"]')[3]; }
    get Nas_productDescription5() { return $$('[class="cmp-find-my-pack__products-description"]')[4]; }
    get Nas_productDescription6() { return $$('[class="cmp-find-my-pack__products-description"]')[5]; }
    get Nas_productDescription7() { return $$('[class="cmp-find-my-pack__products-description"]')[6]; }
    get Nas_productDescription8() { return $$('[class="cmp-find-my-pack__products-description"]')[7]; }
    get Nas_productDescription9() { return $$('[class="cmp-find-my-pack__products-description"]')[8]; }
    get Nas_productDescription10() { return $$('[class="cmp-find-my-pack__products-description"]')[9]; }
    get Nas_productDescription11() { return $$('[class="cmp-find-my-pack__products-description"]')[10]; }
    get Nas_productDescription12() { return $$('[class="cmp-find-my-pack__products-description"]')[11]; }
    get Nas_productDescription13() { return $$('[class="cmp-find-my-pack__products-description"]')[12]; }
    get Nas_productDescription14() { return $$('[class="cmp-find-my-pack__products-description"]')[13]; }
    get Nas_productDescription15() { return $$('[class="cmp-find-my-pack__products-description"]')[14]; }
    get Nas_productDescription16() { return $$('[class="cmp-find-my-pack__products-description"]')[15]; }
    get Nas_productDescription17() { return $$('[class="cmp-find-my-pack__products-description"]')[16]; }
    get Nas_productDescription18() { return $$('[class="cmp-find-my-pack__products-description"]')[17]; }
    get Nas_productDescription19() { return $$('[class="cmp-find-my-pack__products-description"]')[18]; }

    get Nas_productratingname1() { return $$('[class="cmp-find-my-pack__products-ratingname"]')[0]; }
    get Nas_productratingname2() { return $$('[class="cmp-find-my-pack__products-ratingname"]')[1]; }
    get Nas_productratingname3() { return $$('[class="cmp-find-my-pack__products-ratingname"]')[2]; }
    get Nas_productratingname4() { return $$('[class="cmp-find-my-pack__products-ratingname"]')[3]; }
    get Nas_productratingname5() { return $$('[class="cmp-find-my-pack__products-ratingname"]')[4]; }
    get Nas_productratingname6() { return $$('[class="cmp-find-my-pack__products-ratingname"]')[5]; }
    get Nas_productratingname7() { return $$('[class="cmp-find-my-pack__products-ratingname"]')[6]; }
    get Nas_productratingname8() { return $$('[class="cmp-find-my-pack__products-ratingname"]')[7]; }
    get Nas_productratingname9() { return $$('[class="cmp-find-my-pack__products-ratingname"]')[8]; }
    get Nas_productratingname10() { return $$('[class="cmp-find-my-pack__products-ratingname"]')[9]; }
    get Nas_productratingname11() { return $$('[class="cmp-find-my-pack__products-ratingname"]')[10]; }
    get Nas_productratingname12() { return $$('[class="cmp-find-my-pack__products-ratingname"]')[11]; }
    get Nas_productratingname13() { return $$('[class="cmp-find-my-pack__products-ratingname"]')[12]; }
    get Nas_productratingname14() { return $$('[class="cmp-find-my-pack__products-ratingname"]')[13]; }
    get Nas_productratingname15() { return $$('[class="cmp-find-my-pack__products-ratingname"]')[14]; }
    get Nas_productratingname16() { return $$('[class="cmp-find-my-pack__products-ratingname"]')[15]; }
    get Nas_productratingname17() { return $$('[class="cmp-find-my-pack__products-ratingname"]')[16]; }
    get Nas_productratingname18() { return $$('[class="cmp-find-my-pack__products-ratingname"]')[17]; }
    get Nas_productratingname19() { return $$('[class="cmp-find-my-pack__products-ratingname"]')[18]; }

    get Nas_productdetailscontainer1() { return $$('[class="cmp-find-my-pack__products-details-container"]')[0]; }
    get Nas_productdetailscontainer2() { return $$('[class="cmp-find-my-pack__products-details-container"]')[1]; }
    get Nas_productdetailscontainer3() { return $$('[class="cmp-find-my-pack__products-details-container"]')[2]; }
    get Nas_productdetailscontainer4() { return $$('[class="cmp-find-my-pack__products-details-container"]')[3]; }
    get Nas_productdetailscontainer5() { return $$('[class="cmp-find-my-pack__products-details-container"]')[4]; }
    get Nas_productdetailscontainer6() { return $$('[class="cmp-find-my-pack__products-details-container"]')[5]; }
    get Nas_productdetailscontainer7() { return $$('[class="cmp-find-my-pack__products-details-container"]')[6]; }
    get Nas_productdetailscontainer8() { return $$('[class="cmp-find-my-pack__products-details-container"]')[7]; }
    get Nas_productdetailscontainer9() { return $$('[class="cmp-find-my-pack__products-details-container"]')[8]; }
    get Nas_productdetailscontainer10() { return $$('[class="cmp-find-my-pack__products-details-container"]')[9]; }
    get Nas_productdetailscontainer11() { return $$('[class="cmp-find-my-pack__products-details-container"]')[10]; }
    get Nas_productdetailscontainer12() { return $$('[class="cmp-find-my-pack__products-details-container"]')[11]; }
    get Nas_productdetailscontainer13() { return $$('[class="cmp-find-my-pack__products-details-container"]')[12]; }
    get Nas_productdetailscontainer14() { return $$('[class="cmp-find-my-pack__products-details-container"]')[13]; }
    get Nas_productdetailscontainer15() { return $$('[class="cmp-find-my-pack__products-details-container"]')[14]; }
    get Nas_productdetailscontainer16() { return $$('[class="cmp-find-my-pack__products-details-container"]')[15]; }
    get Nas_productdetailscontainer17() { return $$('[class="cmp-find-my-pack__products-details-container"]')[16]; }
    get Nas_productdetailscontainer18() { return $$('[class="cmp-find-my-pack__products-details-container"]')[17]; }
    get Nas_productdetailscontainer19() { return $$('[class="cmp-find-my-pack__products-details-container"]')[18]; }
    get equityProductsDescription1() {
        return $$('[class="cmp-find-my-pack__equity-products-description"]')[0];
    }

    get equityProductsDescription2() {
        return $$('[class="cmp-find-my-pack__equity-products-description"]')[1];
    }

    get equityProductsDescription3() {
        return $$('[class="cmp-find-my-pack__equity-products-description"]')[2];
    }

    get equityProductsDescription4() {
        return $$('[class="cmp-find-my-pack__equity-products-description"]')[3];
    }

    get equityProductsDescription5() {
        return $$('[class="cmp-find-my-pack__equity-products-description"]')[4];
    }

    get equityProductsDescription6() {
        return $$('[class="cmp-find-my-pack__equity-products-description"]')[5];
    }
    get Nas_zeroWasteHeadline() {
        return $('[id="btext"] span.text--large');
    }

    get exploreOrganicButton() {
        return $$('.cmp-find-my-pack__equity-products-btn')[0];
    }
    get getStartedButton() {
        return $$('.cmp-find-my-pack__equity-products-btn')[1];
    }
    get exploreOurTobaccoButton() {
        return $$('.cmp-find-my-pack__equity-products-btn')[2];
    }
    get meetOurPartnersButton() {
        return $$('.cmp-find-my-pack__equity-products-btn')[3];
    }
    get exploreNowButton() {
        return $$('.cmp-find-my-pack__equity-products-btn')[4];
    }
    get checkForOffersButton() {
        return $$('.cmp-find-my-pack__equity-products-btn')[5];
    }
    get dontKnowWhereToStartButton() {
        return $('//*[text()="Don\'t Know Where To Start?"]');
    }
    get tobaccoPreferenceParagraph2() {
        return $('(//*[contains(text(),"Answer a few questions about your tobacco preferences")])[2]');
    }
    get getStartedButton2() {
        return $$('//*[contains(text(), "Get Started")]')[1];
    }


    get redeemMobileOffersText1() {
        return $('(//*[contains(text(), "Redeem mobile offers on your phone!")])[2]');
    }

    get beTheFirstToKnowText1() {
        return $$('//*[contains(text(), "Be the first to know what’s next. Exclusive content")]')[1];
    }
    get seeOffersButton1() {
        return $$('//*[contains(text(), "See Offers")]')[0];
    }

    get findYourSpiritText() {
        return $('//*[contains(text(), "Find your Spirits")]');
    }

    get productTasteParagraph() {
        return $('(//*[contains(text(), "Check out organic and non-organ")])[2]');
    }
    get findAStoreButton() {
        return $('//*[contains(text(),"Find a Store")]');
    }
    get styleAvailabilityNote() {
        return $('(//*[contains(text(), "Some styles may not be available in your state")])[2]');
    }





}
export default new NasProductPageObject();