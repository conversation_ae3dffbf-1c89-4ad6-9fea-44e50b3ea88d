
Feature: Home Page Validation for Sensa Brand

  Scenario Outline: Vaidate the Content and links of Home page for <Brand>
    Given The user is on the login page for <Brand> with login <URL>
    When The user login with valid user id <Username> and password <Password>
    Then The user should be able to login to the application successfully
    Then The user Validates Homepage Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
    Then The user clicks on all buttons and verify the navigations from home page
    Then The user validates that successfully logged out

    @SensaHomePage_Validation_QA
    Examples:
      | Brand | URL                             | Username                           | Password  | filename              | sheetname | scenarioname       |
      | Sensa | https://aem-stage.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | Home Page | Validate Home Page |

    @SensaHomePage_Validation_PROD 
    Examples:
      | Brand | URL                       | Username                             | Password  | filename              | sheetname | scenarioname       |
      | Sensa | https://www.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | Home Page | Validate Home Page |
