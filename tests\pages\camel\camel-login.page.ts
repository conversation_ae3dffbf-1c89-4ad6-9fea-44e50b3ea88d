import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import RegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';
import path from 'path';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import sensaAccountPage from '../commonteps/account.page.ts';
import camelLoginPageObject from '../../page-object/camel/camel-login.pageObject.ts';
import sensaAccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';

class CamelLoginPage {

  async camelloginPageValidation(filename: string, sheetname: string, scenarioname: string) {
    try {

      const SHEET_NAME = sheetname;
      const jsonFilePath = path.join(process.cwd(), 'data', filename);
      const testData = new JsonTestDataHandler(jsonFilePath);
      const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
      console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
      const signin = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsignin');
      const alreadyhave = testData.getCellValue(SHEET_NAME, scenarioname, 'lblhaveanaccount');
      const forgot = testData.getCellValue(SHEET_NAME, scenarioname, 'lblforgot');
      const loginemail = testData.getCellValue(SHEET_NAME, scenarioname, 'lblloginemail');
      const loginremember = testData.getCellValue(SHEET_NAME, scenarioname, 'lblrememberme');
      const loginpassword = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpassword');
      const lblregister = testData.getCellValue(SHEET_NAME, scenarioname, 'lblregister');
      const lblnewaccount = testData.getCellValue(SHEET_NAME, scenarioname, 'lblnewaccount');
      const joinemail = testData.getCellValue(SHEET_NAME, scenarioname, 'lbljoinemail');
      const legalnotice = testData.getCellValue(SHEET_NAME, scenarioname, 'lbllegalNotice');
      const faq = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfaq');
      const siterequirement = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsiterequirement');
      const privacypolicy = testData.getCellValue(SHEET_NAME, scenarioname, 'lblprivacypolicy');
      const sustainability = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsustainability');
      const tobacco = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltobbacco');
      const camelpoints = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcamelpoints');
      const contactus = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcontactus');
      const termsofuse = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltermsuse');
      const textmessaging = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltextmessage');
      const copyright = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcopyright');
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lblsignIn_camel, signin);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lblalreadyhaveaccount_camel, alreadyhave);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lblforgot_camel, forgot);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lblloginemail_camel, loginemail);
      await elementActions.assertion(camelLoginPageObject.txtusername_camel);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lblloginrememberme_camel, loginremember);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lblloginpassword_camel, loginpassword);
      await elementActions.assertion(camelLoginPageObject.txtpassword_camel);
      await elementActions.assertion(camelLoginPageObject.btnlogin_camel);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lblregister_camel, lblregister);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lblregisteraccount_camel, lblnewaccount);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lbljoinemail_camel, joinemail);
      await elementActions.assertion(camelLoginPageObject.txtregemail_camel);
      await elementActions.assertion(camelLoginPageObject.txtjoinnow_camel);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lbllegalnotice_camel, legalnotice);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnkfaq_camel, faq);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnksiteRequirement_camel, siterequirement);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnkprivacyPolicy_camel, privacypolicy);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnkcontactUs_camel, contactus);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnktermsofUse_camel, termsofuse);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnktextMessaging_camel, textmessaging);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnktobacco_camel, tobacco);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnksustainability_camel, sustainability);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnkcamelpoits_camel, camelpoints);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lblcopyright_camel, copyright);
      console.log('Validated the Content in Login page Successfully');
    } catch (error) {
      logger.error('Failed to Validate the Content in Login page', { error });
      throw error;
    }
  }

  async nasloginPage() {
    try {
      await elementActions.assertion(camelLoginPageObject.lbllogo_nas);
      console.log('User Logged in Successfully');
    } catch (error) {
      logger.error('Unable to Log in ', { error });
      throw error;
    }
  }

  async camelloginPage() {
    try {
      await elementActions.assertion(camelLoginPageObject.lbllogo_camel);
      console.log('User Logged in Successfully');
    } catch (error) {
      logger.error('Unable to Log in ', { error });
      throw error;
    }
  }

  async camellogoutsucessfully() {
    try {
      const signin = await RegistrationPageObject.lblsignIn_sensa;
      const logout = await sensaAccountPageObject.lnklogout_sensa;
      let tries = 0;
      const maxTries = 3;
      if (await browser.getWindowHandle()) {
        while (tries < maxTries) {
          try {
            await browser.execute(() => {
              localStorage.clear();
              sessionStorage.clear();
            });
            await sensaAccountPage.clickonaccountlinkfromheader();
            await elementActions.waitForDisplayed(logout);
            await elementActions.clickusingJavascript(logout);

            await browser.waitUntil(
              async () => await signin.isDisplayed(),
              { timeout: 5000, timeoutMsg: 'Sign-in button did not appear after logout' },
            );
            console.log(`Logout successful on attempt ${tries + 1}`);
            break;
          } catch (attemptError) {
            logger.warn(`Logout attempt ${tries + 1} failed:`, { error: attemptError });
            tries++;
          }
        }
        if (!(await signin.isDisplayed())) {
          throw new Error(`Failed to logout after ${maxTries} attempts`);
        }
      } else {
        console.log('Session already terminated - skipping logout sequence');
      }
      console.log('User Logged out Successfully');
    } catch (error) {
      logger.error('Unable to Log out Successfully ', { error });
      throw error;
    }
  }

}
export default new CamelLoginPage();