import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import { expect } from '@wdio/globals';
import sensaAccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';
import sensaMydevicePageObject from '../../page-object/sensa/sensa-mydevice.pageObject.ts';
import path from 'path';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import sensaAccountPage from '../commonteps/account.page.ts';


class MyDevice {
    async clickonmyDeviceLink() {
        try {
            await elementActions.waitForDisplayed(sensaAccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.clickusingJavascript(sensaAccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.clickusingJavascript(sensaMydevicePageObject.lnkmydevice_sensa);
            await elementActions.assertion(sensaMydevicePageObject.lblmydevicetitle_sensa);
            console.log('Navigated to My Device Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to My Device Page', { error });
            throw error;
        }
    }
    async clickonlinkandcheckscroll(element: ChainablePromiseElement, link: ChainablePromiseElement) {
        try {
            const video = await element;
            await link.scrollIntoView({ block: 'center', inline: 'center', behavior: 'smooth' });
            await elementActions.clickusingJavascript(link);
            await elementActions.waitForDisplayed(video);
            const isInViewport = await browser.execute(function (ele) {
                const rect = ele.getBoundingClientRect();
                const windowHeight = window.innerHeight || document.documentElement.clientHeight;
                const windowWidth = window.innerWidth || document.documentElement.clientWidth;

                // Check if the element is at least partially in the viewport
                return (
                    rect.top < windowHeight && // ensures that the element has started scrolling into the viewport from the top
                    rect.bottom >= 0 && // ensures that the bottom of the element is not below the viewport.
                    rect.left < windowWidth &&//ensures that the element is horizontally within the viewport.
                    rect.right >= 0 //ensures that the element is horizontally within the viewport.
                );
            }, video);
            console.log(`Element is in the viewport: ${isInViewport}`);
            expect(isInViewport).toBe(true);
            console.log('Validated the links Successfully');
        } catch (error) {
            logger.error('Failed to validate the links', { error });
            throw error;
        }
    }

    async myDevicepagevalidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdrdevice = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrDevice');
            const devicesubtitle = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldevicesubtitle');
            const lbldevicedecode = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldevicedecode');
            const lbluserfriendly = testData.getCellValue(SHEET_NAME, scenarioname, 'lbluserfriendly');
            const lbllink1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lbllink1');
            const lbllink2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lbllink2');
            const lbllink3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lbllink3');
            const lbllink4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lbllink4');
            const commonQuestion = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcommonQuestion');
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblmydevicetitle_sensa, hdrdevice);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblmydevicedescription_sensa, devicesubtitle);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lbldevicedecode_sensa, lbldevicedecode);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lbluserfriendly_sensa, lbluserfriendly);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lbllink1_sensa, lbllink1);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lbllink2_sensa, lbllink2);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lbllink3_sensa, lbllink3);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lbllink4_sensa, lbllink4);
            await elementActions.assertion(sensaMydevicePageObject.lblvideo1_sensa);
            await elementActions.assertion(sensaMydevicePageObject.lblvideo2_sensa);
            await elementActions.assertion(sensaMydevicePageObject.lblvideo3_sensa);
            await elementActions.assertion(sensaMydevicePageObject.lblvideo4_sensa);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblcommonQuestion_sensa, commonQuestion);
            const lblQ1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQ1');
            const lblA11 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA11');
            const lblA12 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA12');
            const lblQ2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQ2');
            const lblA21 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA21');
            const lblA22 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA22');
            const lblA23 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA23');
            const lblQ3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQ3');
            const lblA31 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA31');
            const lblA32 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA32');
            const lblQ4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQ4');
            const lblA41 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA41');
            const lblQ5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQ5');
            const lblA51 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA51');
            const lblA52 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA52');
            const lblA53 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA53');
            const lblA54 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA54');
            const lblA55 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA55');
            const lblA56 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA56');
            const lblA57 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA57');
            const lblQ6 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQ6');
            const lblA61 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA61');
            const lblA62 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA62');
            const lblA63 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA63');
            const lblA64 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA64');
            const lblA64_Prod = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA64_Prod');
            const lblQ7 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQ7');
            const lblA711 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA711');
            const lblA712 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA712');
            const lblA713 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA713');
            const lblA714 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA714');
            const lblA715 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA715');
            const lblA716 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA716');
            const lblA717 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA717');
            const lblA718 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA718');
            const lblA719 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA719');
            const lblA720 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA720');
            const lblA721 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA721');
            const lblA722 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA722');
            const lblA723 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA723');
            const lblA724 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA724');
            const lblA725 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblA725');
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblQ1_sensa, lblQ1);
            await elementActions.click(sensaMydevicePageObject.lblQ1_sensa);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA11_sensa, lblA11);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA12_sensa, lblA12);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblQ2_sensa, lblQ2);
            await elementActions.click(sensaMydevicePageObject.lblQ2_sensa);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA21_sensa, lblA21);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA22_sensa, lblA22);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA23_sensa, lblA23);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblQ3_sensa, lblQ3);
            await elementActions.click(sensaMydevicePageObject.lblQ3_sensa);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA31_sensa, lblA31);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA32_sensa, lblA32);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblQ4_sensa, lblQ4);
            await elementActions.click(sensaMydevicePageObject.lblQ4_sensa);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA41_sensa, lblA41);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblQ5_sensa, lblQ5);
            await elementActions.click(sensaMydevicePageObject.lblQ5_sensa);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA51_sensa, lblA51);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA52_sensa, lblA52);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA53_sensa, lblA53);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA54_sensa, lblA54);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA55_sensa, lblA55);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA56_sensa, lblA56);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA57_sensa, lblA57);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblQ6_sensa, lblQ6);
            await elementActions.click(sensaMydevicePageObject.lblQ6_sensa);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA61_sensa, lblA61);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA62_sensa, lblA62);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA63_sensa, lblA63);
            const url = browser.getUrl();
            if ((await url).includes('aem')) {
                await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA64_sensa, lblA64);
            } else {
                await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA64_sensa, lblA64_Prod);
            }
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblQ7_sensa, lblQ7);
            await elementActions.click(sensaMydevicePageObject.lblQ7_sensa);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA711_sensa, lblA711);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA712_sensa, lblA712);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA713_sensa, lblA713);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA714_sensa, lblA714);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA715_sensa, lblA715);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA716_sensa, lblA716);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA717_sensa, lblA717);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA718_sensa, lblA718);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA719_sensa, lblA719);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA720_sensa, lblA720);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA721_sensa, lblA721);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA722_sensa, lblA722);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA723_sensa, lblA723);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA724_sensa, lblA724);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lblA725_sensa, lblA725);
            console.log('Validated the Content in Device page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Device page', { error });
            throw error;
        }
    }

    async clickoneachlinkandvalidatethevideo() {
        try {
            await this.clickonlinkandcheckscroll(sensaMydevicePageObject.lblvideo1_sensa, sensaMydevicePageObject.lbllink1_sensa);
            await this.clickonlinkandcheckscroll(sensaMydevicePageObject.lblvideo2_sensa, sensaMydevicePageObject.lbllink2_sensa);
            await this.clickonlinkandcheckscroll(sensaMydevicePageObject.lblvideo3_sensa, sensaMydevicePageObject.lbllink3_sensa);
            await this.clickonlinkandcheckscroll(sensaMydevicePageObject.lblvideo4_sensa, sensaMydevicePageObject.lbllink4_sensa);
            console.log('Validated All links Successfully');
        } catch (error) {
            logger.error('Failed to Validate All links', { error });
            throw error;
        }
    }

}
export default new MyDevice();